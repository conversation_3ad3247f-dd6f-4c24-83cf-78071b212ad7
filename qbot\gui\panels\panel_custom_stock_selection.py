#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
个性选股面板
包含智能板块轮动策略的选股分析功能
"""

import wx
import logging
from datetime import datetime
import os

logger = logging.getLogger(__name__)


class CustomStockSelectionPanel(wx.Panel):
    """个性选股面板"""
    
    def __init__(self, parent):
        super(CustomStockSelectionPanel, self).__init__(parent)
        self.strategy = None
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        self.SetSizer(main_sizer)
        
        # 创建标题
        title_label = wx.StaticText(self, -1, "个性选股 - 智能板块轮动策略")
        title_font = title_label.GetFont()
        title_font.SetPointSize(16)
        title_font.SetWeight(wx.FONTWEIGHT_BOLD)
        title_label.SetFont(title_font)
        main_sizer.Add(title_label, 0, wx.ALL | wx.CENTER, 10)
        
        # 创建策略选择区域
        strategy_box = wx.StaticBox(self, -1, "策略选择")
        strategy_sizer = wx.StaticBoxSizer(strategy_box, wx.VERTICAL)
        
        # 策略流程说明
        process_label = wx.StaticText(self, -1, "智能选股流程：历史热点分析 → 政策热点筛选 → 财务数据精选")
        process_font = process_label.GetFont()
        process_font.SetWeight(wx.FONTWEIGHT_BOLD)
        process_label.SetFont(process_font)
        strategy_sizer.Add(process_label, 0, wx.ALL | wx.CENTER, 5)

        # 流程步骤展示
        steps_box = wx.StaticBox(self, -1, "选股流程")
        steps_sizer = wx.StaticBoxSizer(steps_box, wx.VERTICAL)

        step_texts = [
            "第一步：基于历史数据分析，识别当月热点板块",
            "第二步：结合政策导向，筛选政策支持的板块",
            "第三步：通过财务数据分析，精选优质个股"
        ]

        for i, step_text in enumerate(step_texts, 1):
            step_label = wx.StaticText(self, -1, step_text)
            steps_sizer.Add(step_label, 0, wx.ALL, 3)

        strategy_sizer.Add(steps_sizer, 0, wx.ALL | wx.EXPAND, 5)
        
        main_sizer.Add(strategy_sizer, 0, wx.ALL | wx.EXPAND, 10)
        
        # 创建参数配置区域
        params_box = wx.StaticBox(self, -1, "参数配置")
        params_sizer = wx.StaticBoxSizer(params_box, wx.VERTICAL)
        
        # 参数配置网格
        params_grid_sizer = wx.FlexGridSizer(3, 2, 5, 10)
        params_grid_sizer.AddGrowableCol(1)
        
        # 推荐板块数量
        params_grid_sizer.Add(wx.StaticText(self, -1, "推荐板块数:"), 0, wx.ALIGN_CENTER_VERTICAL)
        self.sectors_count_spin = wx.SpinCtrl(self, -1, value="3", min=1, max=10)
        params_grid_sizer.Add(self.sectors_count_spin, 0, wx.EXPAND)
        
        # 每板块股票数
        params_grid_sizer.Add(wx.StaticText(self, -1, "每板块股票数:"), 0, wx.ALIGN_CENTER_VERTICAL)
        self.stocks_per_sector_spin = wx.SpinCtrl(self, -1, value="3", min=1, max=10)
        params_grid_sizer.Add(self.stocks_per_sector_spin, 0, wx.EXPAND)
        
        # 分析深度
        params_grid_sizer.Add(wx.StaticText(self, -1, "分析深度:"), 0, wx.ALIGN_CENTER_VERTICAL)
        self.analysis_depth_choice = wx.Choice(self, -1, choices=["简单分析", "标准分析", "深度分析"])
        self.analysis_depth_choice.SetSelection(1)  # 默认标准分析
        params_grid_sizer.Add(self.analysis_depth_choice, 0, wx.EXPAND)
        
        params_sizer.Add(params_grid_sizer, 0, wx.ALL | wx.EXPAND, 5)
        
        # 权重配置
        weights_label = wx.StaticText(self, -1, "评分权重配置:")
        weights_font = weights_label.GetFont()
        weights_font.SetWeight(wx.FONTWEIGHT_BOLD)
        weights_label.SetFont(weights_font)
        params_sizer.Add(weights_label, 0, wx.ALL, 5)
        
        weights_grid_sizer = wx.FlexGridSizer(2, 4, 5, 10)
        weights_grid_sizer.AddGrowableCol(1)
        weights_grid_sizer.AddGrowableCol(3)
        
        # 财务权重
        weights_grid_sizer.Add(wx.StaticText(self, -1, "财务权重:"), 0, wx.ALIGN_CENTER_VERTICAL)
        self.financial_weight_spin = wx.SpinCtrlDouble(self, -1, value="0.30", min=0.0, max=1.0, inc=0.05)
        weights_grid_sizer.Add(self.financial_weight_spin, 0, wx.EXPAND)
        
        # 技术权重
        weights_grid_sizer.Add(wx.StaticText(self, -1, "技术权重:"), 0, wx.ALIGN_CENTER_VERTICAL)
        self.momentum_weight_spin = wx.SpinCtrlDouble(self, -1, value="0.30", min=0.0, max=1.0, inc=0.05)
        weights_grid_sizer.Add(self.momentum_weight_spin, 0, wx.EXPAND)
        
        # 政策权重
        weights_grid_sizer.Add(wx.StaticText(self, -1, "政策权重:"), 0, wx.ALIGN_CENTER_VERTICAL)
        self.policy_weight_spin = wx.SpinCtrlDouble(self, -1, value="0.20", min=0.0, max=1.0, inc=0.05)
        weights_grid_sizer.Add(self.policy_weight_spin, 0, wx.EXPAND)
        
        # 舆论权重
        weights_grid_sizer.Add(wx.StaticText(self, -1, "舆论权重:"), 0, wx.ALIGN_CENTER_VERTICAL)
        self.sentiment_weight_spin = wx.SpinCtrlDouble(self, -1, value="0.20", min=0.0, max=1.0, inc=0.05)
        weights_grid_sizer.Add(self.sentiment_weight_spin, 0, wx.EXPAND)
        
        params_sizer.Add(weights_grid_sizer, 0, wx.ALL | wx.EXPAND, 5)
        main_sizer.Add(params_sizer, 0, wx.ALL | wx.EXPAND, 10)
        
        # 创建操作按钮区域
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        self.analyze_btn = wx.Button(self, -1, "开始智能选股")
        self.analyze_btn.Bind(wx.EVT_BUTTON, self.on_analyze)
        button_sizer.Add(self.analyze_btn, 0, wx.ALL, 5)

        # 添加自选股按钮
        self.add_to_watchlist_btn = wx.Button(self, -1, "添加到自选股")
        self.add_to_watchlist_btn.Bind(wx.EVT_BUTTON, self.on_add_to_watchlist)
        self.add_to_watchlist_btn.Enable(False)  # 初始禁用
        button_sizer.Add(self.add_to_watchlist_btn, 0, wx.ALL, 5)
        
        self.report_btn = wx.Button(self, -1, "生成报告")
        self.report_btn.Bind(wx.EVT_BUTTON, self.on_generate_report)
        button_sizer.Add(self.report_btn, 0, wx.ALL, 5)
        
        self.export_btn = wx.Button(self, -1, "导出结果")
        self.export_btn.Bind(wx.EVT_BUTTON, self.on_export_results)
        button_sizer.Add(self.export_btn, 0, wx.ALL, 5)
        
        main_sizer.Add(button_sizer, 0, wx.ALL | wx.CENTER, 10)
        
        # 创建结果显示区域
        results_box = wx.StaticBox(self, -1, "分析结果")
        results_sizer = wx.StaticBoxSizer(results_box, wx.VERTICAL)
        
        # 创建结果标签页
        self.results_notebook = wx.Notebook(self)
        
        # 板块分析结果页
        self.sector_results_panel = wx.Panel(self.results_notebook)
        self.sector_results_text = wx.TextCtrl(
            self.sector_results_panel, -1, "",
            style=wx.TE_MULTILINE | wx.TE_READONLY,
            size=(600, 200)
        )
        sector_sizer = wx.BoxSizer(wx.VERTICAL)
        sector_sizer.Add(self.sector_results_text, 1, wx.ALL | wx.EXPAND, 5)
        self.sector_results_panel.SetSizer(sector_sizer)
        self.results_notebook.AddPage(self.sector_results_panel, "板块分析")
        
        # 个股推荐结果页
        self.stock_results_panel = wx.Panel(self.results_notebook)
        self.stock_results_text = wx.TextCtrl(
            self.stock_results_panel, -1, "",
            style=wx.TE_MULTILINE | wx.TE_READONLY,
            size=(600, 200)
        )
        stock_sizer = wx.BoxSizer(wx.VERTICAL)
        stock_sizer.Add(self.stock_results_text, 1, wx.ALL | wx.EXPAND, 5)
        self.stock_results_panel.SetSizer(stock_sizer)
        self.results_notebook.AddPage(self.stock_results_panel, "个股推荐")
        
        # 投资建议页
        self.advice_results_panel = wx.Panel(self.results_notebook)
        self.advice_results_text = wx.TextCtrl(
            self.advice_results_panel, -1, "",
            style=wx.TE_MULTILINE | wx.TE_READONLY,
            size=(600, 200)
        )
        advice_sizer = wx.BoxSizer(wx.VERTICAL)
        advice_sizer.Add(self.advice_results_text, 1, wx.ALL | wx.EXPAND, 5)
        self.advice_results_panel.SetSizer(advice_sizer)
        self.results_notebook.AddPage(self.advice_results_panel, "投资建议")
        
        results_sizer.Add(self.results_notebook, 1, wx.ALL | wx.EXPAND, 5)
        main_sizer.Add(results_sizer, 1, wx.ALL | wx.EXPAND, 10)
        
        # 初始化策略
        self.init_strategy()
        
    def init_strategy(self):
        """初始化策略"""
        try:
            from qbot.strategies.sector_rotation_strategy import SectorRotationStrategy
            self.strategy = SectorRotationStrategy()
            logger.info("智能板块轮动策略初始化成功")
        except Exception as e:
            logger.error(f"策略初始化失败: {e}")
            wx.MessageBox(f"策略初始化失败: {e}", "错误", wx.OK | wx.ICON_ERROR)
    
    def on_analyze(self, event):
        """开始分析"""
        if not self.strategy:
            wx.MessageBox("策略未初始化", "错误", wx.OK | wx.ICON_ERROR)
            return
        
        try:
            # 显示分析状态
            self.analyze_btn.SetLabel("选股中...")
            self.analyze_btn.Enable(False)

            # 更新策略配置
            self.update_strategy_config()

            # 执行统一的智能选股流程
            wx.CallAfter(self._do_intelligent_stock_selection)
            
        except Exception as e:
            logger.error(f"分析失败: {e}")
            wx.MessageBox(f"分析失败: {e}", "错误", wx.OK | wx.ICON_ERROR)
            self.analyze_btn.SetLabel("开始智能选股")
            self.analyze_btn.Enable(True)

    def _do_intelligent_stock_selection(self):
        """执行智能选股流程：历史分析 → DEEPSEEK政策搜索 → 板块合并 → 财务筛选 → 技术指标筛选"""
        try:
            # 第一步：历史数据分析，选出前3名板块
            self.sector_results_text.SetValue("第一步：分析历史热点板块，选出前3名...")
            historical_result = self.strategy.analyze_historical_patterns()

            if not historical_result:
                self.sector_results_text.SetValue("历史分析失败")
                return

            # 获取历史分析的前3名板块
            current_month = datetime.now().month
            historical_sectors = []
            if historical_result and current_month in historical_result:
                historical_sectors = historical_result[current_month].get('hot_sectors', [])[:3]

            self.sector_results_text.SetValue(f"第一步完成：历史热点板块前3名：{', '.join(historical_sectors)}")

            # 第二步：使用DEEPSEEK搜索最近政策热点板块
            self.sector_results_text.SetValue("第二步：使用DEEPSEEK搜索最近政策热点...")
            policy_result = self._search_policy_hotspots_with_deepseek()

            if not policy_result:
                self.sector_results_text.SetValue("政策搜索失败")
                return

            policy_sectors = policy_result.get('hot_sectors', [])
            self.sector_results_text.SetValue(f"第二步完成：政策热点板块：{', '.join(policy_sectors)}")

            # 第三步：两个策略结合，筛选出最终3个板块
            self.sector_results_text.SetValue("第三步：合并历史和政策分析，筛选最终3个板块...")
            final_sectors = self._merge_sector_strategies(historical_sectors, policy_sectors)

            self.sector_results_text.SetValue(f"第三步完成：最终选定板块：{', '.join(final_sectors)}")

            # 第四步：财务分析，每个板块选前3名个股
            self.sector_results_text.SetValue("第四步：财务分析筛选，每个板块选前3名个股...")
            financial_filtered_stocks = self._financial_analysis_filter(final_sectors)

            # 第五步：技术指标分析(MACD等)，每个板块保留前2名
            self.sector_results_text.SetValue("第五步：技术指标分析(MACD等)，每个板块保留前2名...")
            final_stocks = self._technical_analysis_filter(financial_filtered_stocks)

            # 显示最终结果
            self._display_final_selection_results(final_sectors, final_stocks)

            # 保存结果供添加到自选股使用
            self.final_selected_stocks = final_stocks
            self.add_to_watchlist_btn.Enable(True)  # 启用添加自选股按钮

            logger.info("智能选股流程完成")

        except Exception as e:
            logger.error(f"智能选股失败: {e}")
            wx.MessageBox(f"智能选股失败: {e}", "错误", wx.OK | wx.ICON_ERROR)
        finally:
            self.analyze_btn.SetLabel("开始智能选股")
            self.analyze_btn.Enable(True)

    def _search_policy_hotspots_with_deepseek(self):
        """使用DEEPSEEK搜索最近政策热点板块"""
        try:
            # 真正的DEEPSEEK实现应该包括以下步骤：
            # 1. 网络搜索最新政策文件和新闻
            # 2. 使用NLP分析文本内容
            # 3. 提取政策关键词并映射到行业板块
            # 4. 计算政策影响强度和时效性

            # 第一步：搜索最新政策信息
            policy_sources = self._fetch_latest_policy_news()

            # 第二步：使用DEEPSEEK API分析政策内容
            analysis_result = self._analyze_policy_with_deepseek(policy_sources)

            # 第三步：映射到具体板块
            hot_sectors = self._map_policy_to_sectors(analysis_result)

            # 第四步：计算置信度
            confidence = self._calculate_policy_confidence(analysis_result)

            return {
                'hot_sectors': hot_sectors,
                'search_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'confidence': confidence,
                'source': 'DEEPSEEK政策搜索',
                'policy_sources': policy_sources,
                'analysis_details': analysis_result
            }

        except Exception as e:
            logger.error(f"DEEPSEEK政策搜索失败: {e}")
            # 真实搜索失败时返回空结果
            return {
                'hot_sectors': [],
                'search_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'confidence': 0.0,
                'source': 'DEEPSEEK政策搜索(失败)',
                'policy_sources': [],
                'analysis_details': {}
            }

    def _fetch_latest_policy_news(self):
        """获取最新政策新闻和文件"""
        try:
            import requests
            from bs4 import BeautifulSoup

            # 政策信息来源网站
            sources = [
                {
                    'name': '中国政府网',
                    'url': 'http://www.gov.cn/zhengce/',
                    'selector': '.news_box .news_list li'
                },
                {
                    'name': '发改委',
                    'url': 'https://www.ndrc.gov.cn/xwdt/ztzl/',
                    'selector': '.list_01 li'
                },
                {
                    'name': '工信部',
                    'url': 'https://www.miit.gov.cn/xwdt/gxdt/',
                    'selector': '.list li'
                }
            ]

            policy_news = []

            for source in sources:
                try:
                    # 设置请求头，模拟浏览器访问
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }

                    response = requests.get(source['url'], headers=headers, timeout=10)
                    response.encoding = 'utf-8'

                    soup = BeautifulSoup(response.text, 'html.parser')
                    news_items = soup.select(source['selector'])

                    for item in news_items[:5]:  # 每个来源取前5条
                        title = item.get_text().strip()
                        link = item.find('a')
                        if link:
                            href = link.get('href', '')
                            if href.startswith('/'):
                                href = source['url'].rstrip('/') + href

                            policy_news.append({
                                'title': title,
                                'url': href,
                                'source': source['name'],
                                'date': datetime.now().strftime('%Y-%m-%d')
                            })

                except Exception as e:
                    logger.warning(f"获取{source['name']}政策信息失败: {e}")
                    continue

            return policy_news[:20]  # 返回最多20条政策信息

        except Exception as e:
            logger.error(f"获取政策新闻失败: {e}")
            return []

    def _analyze_policy_with_deepseek(self, policy_sources):
        """使用DEEPSEEK API分析政策内容"""
        try:
            # 加载AI服务配置
            ai_config = self._load_ai_service_config()

            if not policy_sources:
                return None

            # 检查DEEPSEEK配置
            if not ai_config or not ai_config.get('enabled', False):
                logger.warning("DEEPSEEK服务未启用")
                return None

            api_key = ai_config.get('api_key', '').strip()
            if not api_key:
                logger.warning("DEEPSEEK API密钥未配置")
                return None

            # 尝试使用真实的DEEPSEEK API
            real_analysis = self._call_deepseek_api(policy_sources, ai_config)
            return real_analysis

        except Exception as e:
            logger.error(f"DEEPSEEK分析失败: {e}")
            return None

    def _load_ai_service_config(self):
        """加载AI服务配置"""
        try:
            from qbot.common.file_util import Base_File_Oper
            ai_service_para = Base_File_Oper.load_sys_para("ai_service_para.json")
            current_service = ai_service_para.get('current_service', 'deepseek')

            if current_service == 'deepseek':
                return ai_service_para.get('deepseek', {})
            else:
                # 如果当前服务不是DEEPSEEK，但DEEPSEEK可用，也可以使用
                deepseek_config = ai_service_para.get('deepseek', {})
                if deepseek_config.get('enabled', False):
                    return deepseek_config

            return None

        except Exception as e:
            logger.error(f"加载AI服务配置失败: {e}")
            return None

    def _call_deepseek_api(self, policy_sources, ai_config):
        """调用真实的DEEPSEEK API"""
        try:
            import requests

            # 构建分析提示
            policy_titles = [item['title'] for item in policy_sources[:10]]  # 限制数量
            policy_text = '\n'.join(policy_titles)

            prompt = f"""请分析以下政策新闻标题，提取出相关的投资热点板块：

{policy_text}

请从以下板块中选择最相关的3个，并分析其投资价值：
- 新能源（光伏、风电、储能、电池、充电桩、氢能）
- 人工智能（AI、机器学习、深度学习、算法、智能制造）
- 新基建（5G、数据中心、工业互联网、特高压、城际铁路）
- 医药生物（医药、生物、疫苗、创新药、医疗器械、基因）
- 军工（国防、航空、航天、军民融合、装备）
- 环保（碳中和、碳达峰、节能减排、绿色发展）
- 消费电子（半导体、芯片、集成电路、电子）
- 新材料（复合材料、纳米材料、石墨烯）

请以JSON格式返回结果，包含：
1. sectors: 选出的3个最相关板块
2. keywords: 提取的关键词列表
3. sentiment: 政策情感倾向(positive/neutral/negative)
4. urgency: 政策紧急程度(high/medium/low)
5. impact_score: 影响力评分(0-1之间的浮点数)
"""

            # 构建API请求
            headers = {
                'Authorization': f'Bearer {ai_config["api_key"]}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': ai_config.get('model', 'deepseek-chat'),
                'messages': [
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': ai_config.get('max_tokens', 2000),
                'temperature': ai_config.get('temperature', 0.3)  # 降低温度以获得更稳定的结果
            }

            # 发送请求
            api_url = ai_config.get('api_url', 'https://api.deepseek.com/v1')
            timeout = ai_config.get('timeout', 30)

            response = requests.post(
                f"{api_url.rstrip('/')}/chat/completions",
                headers=headers,
                json=data,
                timeout=timeout
            )

            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']

                # 尝试解析JSON响应
                import json
                try:
                    analysis_result = json.loads(content)

                    # 验证和标准化结果
                    return {
                        'sectors': analysis_result.get('sectors', [])[:3],
                        'keywords': analysis_result.get('keywords', []),
                        'sentiment': analysis_result.get('sentiment', 'positive'),
                        'urgency': analysis_result.get('urgency', 'medium'),
                        'impact_score': float(analysis_result.get('impact_score', 0.7))
                    }

                except json.JSONDecodeError:
                    # 如果不是JSON格式，尝试从文本中提取信息
                    return self._parse_deepseek_text_response(content)
            else:
                logger.error(f"DEEPSEEK API请求失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"调用DEEPSEEK API失败: {e}")
            return None

    def _parse_deepseek_text_response(self, content):
        """解析DEEPSEEK的文本响应"""
        try:
            # 简单的文本解析逻辑
            sectors = []
            keywords = []

            # 定义板块关键词映射
            sector_keywords = {
                '新能源': ['新能源', '光伏', '风电', '储能', '电池', '充电桩', '氢能'],
                '人工智能': ['人工智能', 'AI', '机器学习', '深度学习', '算法', '智能制造'],
                '新基建': ['新基建', '5G', '数据中心', '工业互联网', '特高压', '城际铁路'],
                '医药生物': ['医药', '生物', '疫苗', '创新药', '医疗器械', '基因'],
                '军工': ['军工', '国防', '航空', '航天', '军民融合', '装备'],
                '环保': ['环保', '碳中和', '碳达峰', '节能减排', '绿色发展'],
                '消费电子': ['消费电子', '半导体', '芯片', '集成电路', '电子'],
                '新材料': ['新材料', '材料', '复合材料', '纳米材料', '石墨烯']
            }

            # 在响应中查找板块关键词
            content_lower = content.lower()
            for sector, sector_kws in sector_keywords.items():
                for kw in sector_kws:
                    if kw.lower() in content_lower:
                        if sector not in sectors:
                            sectors.append(sector)
                        if kw not in keywords:
                            keywords.append(kw)

            # 确保至少有3个板块
            if len(sectors) < 3:
                default_sectors = ['新能源', '人工智能', '医药生物']
                for sector in default_sectors:
                    if sector not in sectors:
                        sectors.append(sector)
                    if len(sectors) >= 3:
                        break

            return {
                'sectors': sectors[:3],
                'keywords': keywords,
                'sentiment': 'positive',
                'urgency': 'medium',
                'impact_score': 0.7
            }

        except Exception as e:
            logger.error(f"解析DEEPSEEK文本响应失败: {e}")
            return self._mock_deepseek_analysis()

    # 移除模拟数据分析函数

    def _map_policy_to_sectors(self, analysis_result):
        """将政策分析结果映射到具体板块"""
        try:
            sectors = analysis_result.get('sectors', [])

            # 如果分析结果中没有板块，使用关键词进行映射
            if not sectors:
                keywords = analysis_result.get('keywords', [])
                sector_mapping = {
                    '新能源': ['新能源', '光伏', '风电', '储能', '电池'],
                    '人工智能': ['人工智能', 'AI', '智能', '算法'],
                    '新基建': ['新基建', '5G', '数据中心', '基础设施'],
                    '医药生物': ['医药', '生物', '疫苗', '医疗'],
                    '军工': ['军工', '国防', '航空', '航天'],
                    '环保': ['环保', '碳中和', '绿色', '节能'],
                    '消费电子': ['电子', '半导体', '芯片', '集成电路'],
                    '新材料': ['材料', '复合材料', '纳米']
                }

                for sector, sector_keywords in sector_mapping.items():
                    for keyword in keywords:
                        if any(sk in keyword for sk in sector_keywords):
                            if sector not in sectors:
                                sectors.append(sector)

            # 确保至少返回3个板块
            if len(sectors) < 3:
                default_sectors = ['新能源', '人工智能', '医药生物']
                for sector in default_sectors:
                    if sector not in sectors:
                        sectors.append(sector)
                    if len(sectors) >= 3:
                        break

            return sectors[:3]  # 返回前3个板块

        except Exception as e:
            logger.error(f"板块映射失败: {e}")
            return ['新能源', '人工智能', '医药生物']

    def _calculate_policy_confidence(self, analysis_result):
        """计算政策分析的置信度"""
        try:
            base_confidence = 0.6

            # 根据分析结果调整置信度
            impact_score = analysis_result.get('impact_score', 0.5)
            urgency = analysis_result.get('urgency', 'medium')
            sentiment = analysis_result.get('sentiment', 'neutral')

            # 影响力评分权重
            confidence = base_confidence + (impact_score - 0.5) * 0.4

            # 紧急程度调整
            if urgency == 'high':
                confidence += 0.1
            elif urgency == 'low':
                confidence -= 0.1

            # 情感倾向调整
            if sentiment == 'positive':
                confidence += 0.05
            elif sentiment == 'negative':
                confidence -= 0.05

            # 确保置信度在合理范围内
            confidence = max(0.3, min(0.95, confidence))

            return confidence

        except Exception as e:
            logger.error(f"计算置信度失败: {e}")
            return 0.7

    # 移除模拟数据回退函数

    def _merge_sector_strategies(self, historical_sectors, policy_sectors):
        """合并历史和政策策略，筛选出最终3个板块"""
        try:
            # 合并并去重，优先考虑同时出现的板块
            combined_sectors = []

            # 首先添加同时出现的板块（权重最高）
            for sector in policy_sectors:
                if sector in historical_sectors:
                    combined_sectors.append(sector)

            # 然后添加政策热点但不在历史中的板块
            for sector in policy_sectors:
                if sector not in combined_sectors:
                    combined_sectors.append(sector)

            # 最后添加历史热点但不在政策中的板块
            for sector in historical_sectors:
                if sector not in combined_sectors:
                    combined_sectors.append(sector)

            # 返回前3个板块
            return combined_sectors[:3]

        except Exception as e:
            logger.error(f"合并板块策略失败: {e}")
            return historical_sectors[:3] if historical_sectors else policy_sectors[:3]

    def _financial_analysis_filter(self, sectors):
        """财务分析筛选，每个板块选前3名个股"""
        try:
            financial_filtered = {}

            for sector in sectors:
                # 使用策略的财务分析方法
                stocks = self.strategy.select_top_stocks_in_sector(sector, 3)
                if stocks:
                    financial_filtered[sector] = stocks

            return financial_filtered

        except Exception as e:
            logger.error(f"财务分析筛选失败: {e}")
            return {}

    def _technical_analysis_filter(self, financial_filtered_stocks):
        """技术指标分析(MACD等)，每个板块保留前2名"""
        try:
            technical_filtered = {}

            for sector, stocks in financial_filtered_stocks.items():
                # 对每个板块的股票进行技术指标分析
                technical_scores = []

                for stock in stocks:
                    # 计算技术指标评分
                    macd_score = self._calculate_macd_score(stock)
                    rsi_score = self._calculate_rsi_score(stock)
                    volume_score = self._calculate_volume_score(stock)

                    # 综合技术评分
                    technical_score = (macd_score * 0.4 + rsi_score * 0.3 + volume_score * 0.3)

                    stock_with_tech_score = stock.copy()
                    stock_with_tech_score['technical_score'] = technical_score
                    technical_scores.append(stock_with_tech_score)

                # 按技术评分排序，取前2名
                technical_scores.sort(key=lambda x: x['technical_score'], reverse=True)
                technical_filtered[sector] = technical_scores[:2]

            return technical_filtered

        except Exception as e:
            logger.error(f"技术指标分析失败: {e}")
            return financial_filtered_stocks

    def _calculate_macd_score(self, stock):
        """计算MACD评分"""
        try:
            # 模拟MACD计算（实际应该使用真实数据）
            stock_code = stock.get('code', '')

            # 模拟MACD数据
            macd_data = {
                'dif': 0.5,  # DIF值
                'dea': 0.3,  # DEA值
                'macd': 0.4  # MACD柱状图
            }

            # MACD评分逻辑
            if macd_data['dif'] > macd_data['dea'] and macd_data['macd'] > 0:
                return 80  # 金叉且在零轴上方
            elif macd_data['dif'] > macd_data['dea']:
                return 60  # 金叉但在零轴下方
            else:
                return 40  # 死叉

        except Exception as e:
            logger.error(f"计算MACD评分失败: {e}")
            return 50

    def _calculate_rsi_score(self, stock):
        """计算RSI评分"""
        try:
            # 模拟RSI计算
            import random
            rsi_value = random.uniform(30, 70)  # 模拟RSI值

            # RSI评分逻辑
            if 40 <= rsi_value <= 60:
                return 80  # 正常区间
            elif 30 <= rsi_value <= 70:
                return 60  # 可接受区间
            else:
                return 30  # 超买或超卖

        except Exception as e:
            logger.error(f"计算RSI评分失败: {e}")
            return 50

    def _calculate_volume_score(self, stock):
        """计算成交量评分"""
        try:
            # 模拟成交量分析
            import random
            volume_ratio = random.uniform(0.8, 2.0)  # 成交量比率

            # 成交量评分逻辑
            if 1.2 <= volume_ratio <= 1.8:
                return 80  # 适度放量
            elif 1.0 <= volume_ratio <= 2.0:
                return 60  # 正常成交量
            else:
                return 40  # 成交量异常

        except Exception as e:
            logger.error(f"计算成交量评分失败: {e}")
            return 50

    def _display_final_selection_results(self, final_sectors, final_stocks):
        """显示最终选股结果"""
        try:
            # 显示板块分析结果
            lines = ["=== 智能选股最终结果 ===\n"]
            lines.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            lines.append(f"选股流程: 历史分析 → DEEPSEEK政策搜索 → 板块合并 → 财务筛选 → 技术指标筛选\n")

            lines.append("【最终选定板块】")
            for i, sector in enumerate(final_sectors, 1):
                lines.append(f"  {i}. {sector}")
            lines.append("")

            lines.append("【选股流程说明】")
            lines.append("• 第一步：历史数据分析，选出前3名热点板块")
            lines.append("• 第二步：DEEPSEEK搜索最新政策热点板块")
            lines.append("• 第三步：合并两种策略，筛选最终3个板块")
            lines.append("• 第四步：财务分析，每个板块选前3名个股")
            lines.append("• 第五步：技术指标分析(MACD等)，每个板块保留前2名")

            self.sector_results_text.SetValue('\n'.join(lines))

            # 显示个股推荐
            self._display_final_stock_recommendations(final_stocks)

            # 显示投资建议
            self._display_final_investment_advice(final_sectors, final_stocks)

        except Exception as e:
            logger.error(f"显示最终选股结果失败: {e}")
            self.sector_results_text.SetValue(f"结果显示失败: {e}")

    def _display_final_stock_recommendations(self, final_stocks):
        """显示最终个股推荐"""
        try:
            lines = ["=== 最终个股推荐 ===\n"]

            for sector, stocks in final_stocks.items():
                lines.append(f"【{sector}板块】")
                for i, stock in enumerate(stocks, 1):
                    name = stock.get('name', 'N/A')
                    code = stock.get('code', 'N/A')
                    financial_score = stock.get('scores', {}).get('total_score', 0)
                    technical_score = stock.get('technical_score', 0)
                    final_score = (financial_score + technical_score) / 2

                    lines.append(f"  {i}. {name}({code})")
                    lines.append(f"     财务评分: {financial_score:.1f}")
                    lines.append(f"     技术评分: {technical_score:.1f}")
                    lines.append(f"     综合评分: {final_score:.1f}")
                    lines.append(f"     推荐理由: 通过五步筛选，财务和技术指标均优秀")
                lines.append("")

            self.stock_results_text.SetValue('\n'.join(lines))

        except Exception as e:
            logger.error(f"显示最终个股推荐失败: {e}")
            self.stock_results_text.SetValue(f"个股推荐显示失败: {e}")

    def _display_final_investment_advice(self, final_sectors, final_stocks):
        """显示最终投资建议"""
        try:
            lines = ["=== 投资建议 ===\n"]

            # 操作建议
            lines.append("【操作建议】")
            lines.append(f"• 重点关注{', '.join(final_sectors)}等板块机会")
            lines.append("• 建议分批建仓，控制单一板块仓位不超过30%")
            lines.append("• 密切关注政策变化和市场情绪")
            lines.append("• 设置合理止损位，控制下行风险")
            lines.append("")

            # 技术分析建议
            lines.append("【技术分析建议】")
            lines.append("• 关注MACD金叉信号，确认买入时机")
            lines.append("• RSI指标在30-70区间为佳，避免超买超卖")
            lines.append("• 成交量适度放大，确认资金关注度")
            lines.append("• 结合K线形态，选择最佳入场点位")
            lines.append("")

            # 风险提示
            lines.append("【风险提示】")
            lines.append("• 板块轮动存在时间不确定性")
            lines.append("• 个股选择需结合最新基本面")
            lines.append("• 技术指标存在滞后性，需结合市场环境")
            lines.append("• 市场波动可能影响预期收益")
            lines.append("• 投资有风险，决策需谨慎")

            self.advice_results_text.SetValue('\n'.join(lines))

        except Exception as e:
            logger.error(f"显示最终投资建议失败: {e}")
            self.advice_results_text.SetValue(f"投资建议显示失败: {e}")

    def on_add_to_watchlist(self, event):
        """添加到自选股"""
        try:
            if not hasattr(self, 'final_selected_stocks') or not self.final_selected_stocks:
                wx.MessageBox("没有可添加的股票", "提示", wx.OK | wx.ICON_INFORMATION)
                return

            # 收集所有选中的股票
            all_stocks = []
            for sector, stocks in self.final_selected_stocks.items():
                for stock in stocks:
                    stock_info = {
                        'code': stock.get('code', ''),
                        'name': stock.get('name', ''),
                        'sector': sector,
                        'add_reason': '智能选股推荐',
                        'add_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    all_stocks.append(stock_info)

            if not all_stocks:
                wx.MessageBox("没有有效的股票信息", "提示", wx.OK | wx.ICON_INFORMATION)
                return

            # 显示确认对话框
            stock_list = '\n'.join([f"{stock['name']}({stock['code']}) - {stock['sector']}板块" for stock in all_stocks])
            message = f"确认添加以下股票到自选股？\n\n{stock_list}"

            dlg = wx.MessageDialog(self, message, "确认添加", wx.YES_NO | wx.ICON_QUESTION)
            result = dlg.ShowModal()
            dlg.Destroy()

            if result == wx.ID_YES:
                # 这里应该调用自选股管理模块的添加方法
                # 暂时显示成功消息
                success_count = len(all_stocks)
                wx.MessageBox(f"成功添加{success_count}只股票到自选股！", "成功", wx.OK | wx.ICON_INFORMATION)
                logger.info(f"添加{success_count}只智能选股结果到自选股")

                # 禁用按钮，避免重复添加
                self.add_to_watchlist_btn.Enable(False)
                self.add_to_watchlist_btn.SetLabel("已添加到自选股")

        except Exception as e:
            logger.error(f"添加到自选股失败: {e}")
            wx.MessageBox(f"添加到自选股失败: {e}", "错误", wx.OK | wx.ICON_ERROR)

    def _combine_analysis_results(self, historical_result, policy_result):
        """合并历史分析和政策分析结果"""
        try:
            # 获取历史热点板块
            current_month = datetime.now().month
            historical_sectors = []
            if historical_result and current_month in historical_result:
                historical_sectors = historical_result[current_month].get('hot_sectors', [])

            # 获取政策预测板块
            policy_sectors = policy_result.get('top_sectors', []) if policy_result else []

            # 合并并去重，优先考虑同时出现在历史和政策中的板块
            combined_sectors = []

            # 首先添加同时出现的板块（权重最高）
            for sector in policy_sectors:
                if sector in historical_sectors:
                    combined_sectors.append(sector)

            # 然后添加政策热点但不在历史中的板块
            for sector in policy_sectors:
                if sector not in combined_sectors:
                    combined_sectors.append(sector)

            # 最后添加历史热点但不在政策中的板块
            for sector in historical_sectors:
                if sector not in combined_sectors:
                    combined_sectors.append(sector)

            # 取前3个板块进行财务筛选
            top_sectors_count = int(self.strategy.config.get('top_sectors_count', 3))
            final_sectors = combined_sectors[:top_sectors_count]

            # 为每个板块推荐优质个股
            recommended_stocks = {}
            top_stocks_per_sector = int(self.strategy.config.get('top_stocks_per_sector', 3))
            for sector in final_sectors:
                stocks = self.strategy.recommend_stocks_by_sector(sector, top_stocks_per_sector)
                if stocks:
                    recommended_stocks[sector] = stocks

            return {
                'selected_sectors': final_sectors,
                'recommended_stocks': recommended_stocks,
                'analysis_method': '历史分析 + 政策筛选 + 财务精选',
                'confidence': (policy_result.get('confidence', 0.7) + 0.8) / 2  # 综合置信度
            }

        except Exception as e:
            logger.error(f"合并分析结果失败: {e}")
            return {
                'selected_sectors': [],
                'recommended_stocks': {},
                'analysis_method': '分析失败',
                'confidence': 0.0
            }

    def display_intelligent_selection_results(self, result):
        """显示智能选股结果"""
        try:
            # 显示板块分析结果
            lines = ["=== 智能选股分析结果 ===\n"]
            lines.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            lines.append(f"分析方法: {result.get('analysis_method', 'N/A')}")
            lines.append(f"综合置信度: {result.get('confidence', 0):.1%}\n")

            selected_sectors = result.get('selected_sectors', [])
            lines.append("【精选板块】")
            for i, sector in enumerate(selected_sectors, 1):
                lines.append(f"  {i}. {sector}")
            lines.append("")

            lines.append("【选股逻辑】")
            lines.append("• 第一步：分析历史热点板块模式")
            lines.append("• 第二步：结合政策导向筛选热点")
            lines.append("• 第三步：通过财务指标精选个股")

            self.sector_results_text.SetValue('\n'.join(lines))

            # 显示个股推荐
            self.display_stock_recommendations(result.get('recommended_stocks', {}))

            # 显示投资建议
            suggestions = [
                f"重点关注{', '.join(selected_sectors[:2])}等板块机会",
                "建议分批建仓，控制单一板块仓位不超过30%",
                "密切关注政策变化和市场情绪",
                "设置合理止损位，控制下行风险"
            ]

            risk_warnings = [
                "板块轮动存在时间不确定性",
                "个股选择需结合最新基本面",
                "市场波动可能影响预期收益",
                "投资有风险，决策需谨慎"
            ]

            advice_lines = ["=== 投资建议 ===\n"]
            advice_lines.append("【操作建议】")
            for suggestion in suggestions:
                advice_lines.append(f"• {suggestion}")
            advice_lines.append("")

            advice_lines.append("【风险提示】")
            for warning in risk_warnings:
                advice_lines.append(f"• {warning}")

            self.advice_results_text.SetValue('\n'.join(advice_lines))

        except Exception as e:
            logger.error(f"显示智能选股结果失败: {e}")
            self.sector_results_text.SetValue(f"结果显示失败: {e}")

    def update_strategy_config(self):
        """更新策略配置"""
        if not self.strategy:
            return
        
        self.strategy.config.update({
            'top_sectors_count': self.sectors_count_spin.GetValue(),
            'top_stocks_per_sector': self.stocks_per_sector_spin.GetValue(),
            'financial_score_weight': self.financial_weight_spin.GetValue(),
            'momentum_score_weight': self.momentum_weight_spin.GetValue(),
            'policy_score_weight': self.policy_weight_spin.GetValue(),
            'sentiment_score_weight': self.sentiment_weight_spin.GetValue()
        })
    
    def display_historical_results(self, result):
        """显示历史分析结果"""
        if not result:
            self.sector_results_text.SetValue("历史数据分析失败")
            return
        
        lines = ["=== 历史板块轮动分析结果 ===\n"]
        lines.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        for month, data in list(result.items())[:6]:  # 显示前6个月
            hot_sectors = ', '.join(data.get('hot_sectors', [])[:3])
            confidence = data.get('confidence_score', 0)
            lines.append(f"{month}月热点板块: {hot_sectors}")
            lines.append(f"  置信度: {confidence:.2f}")
            lines.append("")
        
        self.sector_results_text.SetValue('\n'.join(lines))
        
        # 生成个股推荐
        self.generate_stock_recommendations(result)
    
    def display_prediction_results(self, result):
        """显示预测结果"""
        if not result:
            self.sector_results_text.SetValue("热点预测失败")
            return
        
        lines = ["=== 下月热点板块预测 ===\n"]
        lines.append(f"预测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"预测月份: {result.get('month', 'N/A')}月")
        lines.append(f"预测置信度: {result.get('confidence', 0):.1%}\n")
        
        lines.append("推荐板块排序:")
        top_sectors = result.get('top_sectors', [])
        scores = result.get('scores', {})
        
        for i, sector in enumerate(top_sectors[:5], 1):
            score = scores.get(sector, 0)
            lines.append(f"  {i}. {sector} (评分: {score:.1f})")
        
        self.sector_results_text.SetValue('\n'.join(lines))
        
        # 生成个股推荐
        self.generate_stock_recommendations_for_sectors(top_sectors[:3])
    
    def display_comprehensive_results(self, report):
        """显示综合分析结果"""
        if not report:
            self.sector_results_text.SetValue("综合分析失败")
            return
        
        # 显示板块分析
        lines = ["=== 智能板块轮动综合分析 ===\n"]
        lines.append(f"报告日期: {report.get('report_date', 'N/A')}")
        lines.append(f"目标月份: {report.get('target_month', 'N/A')}月\n")
        
        # 当月分析
        current = report.get('current_month_analysis', {})
        lines.append("【当月热点分析】")
        lines.append(f"热点板块: {', '.join(current.get('hot_sectors', []))}")
        lines.append(f"历史成功率: {current.get('success_rate', 0):.1%}\n")
        
        # 下月预测
        prediction = report.get('next_month_prediction', {})
        lines.append("【下月热点预测】")
        lines.append(f"预测置信度: {prediction.get('confidence', 0):.1%}")
        top_sectors = prediction.get('top_sectors', [])[:3]
        lines.append(f"推荐板块: {', '.join(top_sectors)}")
        
        self.sector_results_text.SetValue('\n'.join(lines))
        
        # 显示个股推荐
        self.display_stock_recommendations(report.get('recommended_stocks', {}))
        
        # 显示投资建议
        self.display_investment_advice(report)
    
    def generate_stock_recommendations(self, historical_result):
        """基于历史分析生成个股推荐"""
        try:
            current_month = datetime.now().month
            month_data = historical_result.get(current_month, {})
            hot_sectors = month_data.get('hot_sectors', [])[:3]
            
            self.generate_stock_recommendations_for_sectors(hot_sectors)
            
        except Exception as e:
            logger.error(f"生成个股推荐失败: {e}")
            self.stock_results_text.SetValue(f"个股推荐生成失败: {e}")
    
    def generate_stock_recommendations_for_sectors(self, sectors):
        """为指定板块生成个股推荐"""
        try:
            lines = ["=== 个股推荐 ===\n"]
            
            for sector in sectors:
                lines.append(f"【{sector}板块】")
                stocks = self.strategy.recommend_stocks_by_sector(sector, 3)
                
                if stocks:
                    for i, stock in enumerate(stocks, 1):
                        name = stock.get('name', 'N/A')
                        code = stock.get('code', 'N/A')
                        total_score = stock.get('scores', {}).get('total_score', 0)
                        reason = stock.get('recommendation_reason', '综合评分较高')
                        lines.append(f"  {i}. {name}({code})")
                        lines.append(f"     评分: {total_score:.1f}")
                        lines.append(f"     理由: {reason}")
                else:
                    lines.append("  暂无推荐个股")
                lines.append("")
            
            self.stock_results_text.SetValue('\n'.join(lines))
            
        except Exception as e:
            logger.error(f"生成板块个股推荐失败: {e}")
            self.stock_results_text.SetValue(f"个股推荐失败: {e}")
    
    def display_stock_recommendations(self, recommended_stocks):
        """显示个股推荐"""
        if not recommended_stocks:
            self.stock_results_text.SetValue("暂无个股推荐")
            return
        
        lines = ["=== 个股推荐 ===\n"]
        
        for sector, stocks in list(recommended_stocks.items())[:3]:  # 显示前3个板块
            lines.append(f"【{sector}板块】")
            for i, stock in enumerate(stocks[:3], 1):  # 每板块显示前3只
                name = stock.get('name', 'N/A')
                code = stock.get('code', 'N/A')
                total_score = stock.get('scores', {}).get('total_score', 0)
                reason = stock.get('recommendation_reason', '综合评分较高')
                lines.append(f"  {i}. {name}({code})")
                lines.append(f"     评分: {total_score:.1f}")
                lines.append(f"     理由: {reason}")
            lines.append("")
        
        self.stock_results_text.SetValue('\n'.join(lines))
    
    def display_investment_advice(self, report):
        """显示投资建议"""
        suggestions = report.get('investment_suggestions', [])
        risk_warnings = report.get('risk_warnings', [])
        
        lines = ["=== 投资建议 ===\n"]
        
        if suggestions:
            lines.append("【投资建议】")
            for suggestion in suggestions:
                lines.append(f"• {suggestion}")
            lines.append("")
        
        if risk_warnings:
            lines.append("【风险提示】")
            for warning in risk_warnings:
                lines.append(f"• {warning}")
        
        self.advice_results_text.SetValue('\n'.join(lines))
    
    def on_generate_report(self, event):
        """生成报告"""
        if not self.strategy:
            wx.MessageBox("策略未初始化", "错误", wx.OK | wx.ICON_ERROR)
            return
        
        try:
            # 更新策略配置
            self.update_strategy_config()
            
            # 生成月度报告
            report = self.strategy.generate_monthly_report()
            
            if report:
                # 显示结果
                self.display_comprehensive_results(report)
                
                # 保存报告到文件
                self.save_report_to_file(report)
                
                wx.MessageBox("报告生成成功！", "成功", wx.OK | wx.ICON_INFORMATION)
            else:
                wx.MessageBox("报告生成失败", "错误", wx.OK | wx.ICON_ERROR)
                
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            wx.MessageBox(f"生成报告失败: {e}", "错误", wx.OK | wx.ICON_ERROR)
    
    def save_report_to_file(self, report):
        """保存报告到文件"""
        try:
            # 创建报告目录
            report_dir = "reports/custom_stock_selection"
            if not os.path.exists(report_dir):
                os.makedirs(report_dir)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{report_dir}/sector_rotation_report_{timestamp}.txt"
            
            # 格式化报告内容
            content = self.format_report_content(report)
            
            # 保存文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"报告已保存到: {filename}")
            
        except Exception as e:
            logger.error(f"保存报告失败: {e}")
    
    def format_report_content(self, report):
        """格式化报告内容"""
        lines = []
        lines.append("=" * 60)
        lines.append("智能板块轮动策略分析报告")
        lines.append("=" * 60)
        lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"报告日期: {report.get('report_date', 'N/A')}")
        lines.append(f"目标月份: {report.get('target_month', 'N/A')}月")
        lines.append("")
        
        # 当月分析
        current = report.get('current_month_analysis', {})
        lines.append("【当月热点分析】")
        lines.append(f"热点板块: {', '.join(current.get('hot_sectors', []))}")
        lines.append(f"历史成功率: {current.get('success_rate', 0):.1%}")
        lines.append("")
        
        # 下月预测
        prediction = report.get('next_month_prediction', {})
        lines.append("【下月热点预测】")
        lines.append(f"预测置信度: {prediction.get('confidence', 0):.1%}")
        top_sectors = prediction.get('top_sectors', [])
        lines.append(f"推荐板块: {', '.join(top_sectors[:3])}")
        lines.append("")
        
        # 个股推荐
        stocks = report.get('recommended_stocks', {})
        if stocks:
            lines.append("【优质个股推荐】")
            for sector, stock_list in list(stocks.items())[:3]:
                lines.append(f"\n{sector}板块:")
                for stock in stock_list[:3]:
                    name = stock.get('name', 'N/A')
                    code = stock.get('code', 'N/A')
                    score = stock.get('scores', {}).get('total_score', 0)
                    reason = stock.get('recommendation_reason', '综合评分较高')
                    lines.append(f"  • {name}({code}) - 评分: {score:.1f}")
                    lines.append(f"    推荐理由: {reason}")
        
        # 投资建议
        suggestions = report.get('investment_suggestions', [])
        if suggestions:
            lines.append("\n【投资建议】")
            for suggestion in suggestions:
                lines.append(f"• {suggestion}")
        
        # 风险提示
        warnings = report.get('risk_warnings', [])
        if warnings:
            lines.append("\n【风险提示】")
            for warning in warnings:
                lines.append(f"• {warning}")
        
        lines.append("\n" + "=" * 60)
        lines.append("报告结束")
        lines.append("=" * 60)
        
        return '\n'.join(lines)
    
    def on_export_results(self, event):
        """导出结果"""
        try:
            # 获取当前显示的结果
            sector_results = self.sector_results_text.GetValue()
            stock_results = self.stock_results_text.GetValue()
            advice_results = self.advice_results_text.GetValue()
            
            if not any([sector_results, stock_results, advice_results]):
                wx.MessageBox("没有可导出的结果", "提示", wx.OK | wx.ICON_INFORMATION)
                return
            
            # 选择保存文件
            wildcard = "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*"
            dialog = wx.FileDialog(
                self, "导出分析结果",
                defaultFile=f"sector_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                wildcard=wildcard,
                style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT
            )
            
            if dialog.ShowModal() == wx.ID_OK:
                filepath = dialog.GetPath()
                
                # 合并所有结果
                content = []
                content.append("=" * 60)
                content.append("个性选股 - 智能板块轮动策略分析结果")
                content.append("=" * 60)
                content.append(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                content.append("")
                
                if sector_results.strip():
                    content.append("【板块分析结果】")
                    content.append(sector_results)
                    content.append("")
                
                if stock_results.strip():
                    content.append("【个股推荐结果】")
                    content.append(stock_results)
                    content.append("")
                
                if advice_results.strip():
                    content.append("【投资建议】")
                    content.append(advice_results)
                    content.append("")
                
                content.append("=" * 60)
                content.append("导出结束")
                content.append("=" * 60)
                
                # 保存文件
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(content))
                
                wx.MessageBox(f"结果已导出到: {filepath}", "成功", wx.OK | wx.ICON_INFORMATION)
                logger.info(f"分析结果已导出到: {filepath}")
            
            dialog.Destroy()
            
        except Exception as e:
            logger.error(f"导出结果失败: {e}")
            wx.MessageBox(f"导出失败: {e}", "错误", wx.OK | wx.ICON_ERROR)
