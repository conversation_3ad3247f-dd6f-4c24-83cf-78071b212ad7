# 数据源配置文件
# 注意：API密钥应通过环境变量设置，不要在此文件中硬编码

data_sources:
  # 中国A股数据源
  sina:
    enabled: true
    base_url: "http://hq.sinajs.cn"
    rate_limit: 120
    timeout: 5
    retry_count: 3
    priority: 1
    markets:
      - "CN_A"
    extra_params:
      encoding: "gbk"
  
  tencent:
    enabled: true
    base_url: "http://qt.gtimg.cn"
    rate_limit: 100
    timeout: 5
    retry_count: 3
    priority: 2
    markets:
      - "CN_A"
    extra_params:
      encoding: "gbk"
  
  eastmoney:
    enabled: true
    base_url: "http://push2.eastmoney.com"
    rate_limit: 80
    timeout: 8
    retry_count: 3
    priority: 3
    markets:
      - "CN_A"
    extra_params:
      use_https: false
  
  # 国际市场数据源
  finnhub:
    enabled: false  # 需要API密钥，通过环境变量 QBOT_FINNHUB_API_KEY 设置
    base_url: "https://finnhub.io/api/v1"
    rate_limit: 60
    timeout: 10
    retry_count: 3
    priority: 1
    markets:
      - "US"
      - "HK"
      - "UK"
      - "JP"
    extra_params:
      sandbox: false
      include_metrics: true
  
  yahoo:
    enabled: true
    base_url: "https://query1.finance.yahoo.com"
    rate_limit: 100
    timeout: 10
    retry_count: 3
    priority: 2
    markets:
      - "US"
      - "HK"
      - "UK"
      - "JP"
    extra_params:
      user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  
  alpha_vantage:
    enabled: false  # 需要API密钥，通过环境变量 QBOT_ALPHA_VANTAGE_API_KEY 设置
    base_url: "https://www.alphavantage.co/query"
    rate_limit: 5  # 免费版限制
    timeout: 15
    retry_count: 2
    priority: 3
    markets:
      - "US"
    extra_params:
      function: "GLOBAL_QUOTE"
      datatype: "json"
  
  iex_cloud:
    enabled: false  # 需要API密钥，通过环境变量 QBOT_IEX_CLOUD_API_KEY 设置
    base_url: "https://cloud.iexapis.com/stable"
    rate_limit: 100
    timeout: 10
    retry_count: 3
    priority: 2
    markets:
      - "US"
    extra_params:
      version: "stable"
      format: "json"

# 全局配置
global_settings:
  # 默认数据源选择策略
  default_strategy: "priority"  # priority, fastest, most_reliable
  
  # 数据缓存设置
  cache_enabled: true
  cache_ttl_seconds: 60  # 缓存有效期
  
  # 重试设置
  max_retry_attempts: 3
  retry_delay_seconds: 1
  
  # 超时设置
  default_timeout: 10
  
  # 日志设置
  log_requests: false
  log_responses: false
  
  # 数据质量设置
  min_quality_score: 0.5  # 最低数据质量要求
  
  # 市场特定设置
  market_settings:
    CN_A:
      trading_hours:
        start: "09:30"
        end: "15:00"
        timezone: "Asia/Shanghai"
      preferred_sources: ["sina", "tencent", "eastmoney"]
    
    US:
      trading_hours:
        start: "09:30"
        end: "16:00"
        timezone: "America/New_York"
      preferred_sources: ["finnhub", "yahoo", "alpha_vantage"]
    
    HK:
      trading_hours:
        start: "09:30"
        end: "16:00"
        timezone: "Asia/Hong_Kong"
      preferred_sources: ["finnhub", "yahoo"]
    
    UK:
      trading_hours:
        start: "08:00"
        end: "16:30"
        timezone: "Europe/London"
      preferred_sources: ["finnhub", "yahoo"]
    
    JP:
      trading_hours:
        start: "09:00"
        end: "15:00"
        timezone: "Asia/Tokyo"
      preferred_sources: ["finnhub", "yahoo"]
