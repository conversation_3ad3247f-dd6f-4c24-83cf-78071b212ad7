#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强版数据管理器测试脚本
演示新的数据源管理和配置系统
"""

import asyncio
import os
from pathlib import Path
from qbot.data.enhanced_data_manager import enhanced_data_manager
from qbot.config.data_source_config import config_manager


async def test_configuration():
    """测试配置管理"""
    print("🔧 测试配置管理")
    print("=" * 50)
    
    # 获取配置摘要
    summary = config_manager.get_summary()
    print(f"📊 配置摘要:")
    print(f"  总数据源: {summary['total_sources']}")
    print(f"  启用数据源: {summary['enabled_sources']}")
    print(f"  有API密钥的数据源: {summary['sources_with_api_key']}")
    
    # 按市场显示数据源
    print(f"\n📈 各市场可用数据源:")
    for market, sources in summary['sources_by_market'].items():
        print(f"  {market}: {sources}")
    
    # 测试环境变量加载
    print(f"\n🔑 环境变量测试:")
    test_vars = [
        'QBOT_FINNHUB_API_KEY',
        'QBOT_ALPHA_VANTAGE_API_KEY',
        'QBOT_SINA_ENABLED'
    ]
    
    for var in test_vars:
        value = os.environ.get(var, '未设置')
        if 'API_KEY' in var and value != '未设置':
            value = value[:8] + '...' if len(value) > 8 else value  # 隐藏API密钥
        print(f"  {var}: {value}")


async def test_data_source_status():
    """测试数据源状态"""
    print("\n📡 测试数据源状态")
    print("=" * 50)
    
    # 初始化数据管理器
    await enhanced_data_manager.initialize()
    
    # 获取数据源状态
    status = enhanced_data_manager.get_data_source_status()
    
    if 'error' in status:
        print(f"❌ 获取状态失败: {status['error']}")
        return
    
    print(f"✅ 数据管理器已初始化: {status['initialized']}")
    print(f"📋 可用策略: {status['available_strategies']}")
    
    print(f"\n📊 数据源状态:")
    for source_name, source_status in status['source_status'].items():
        enabled = "✅" if source_status['enabled'] else "❌"
        available = "🟢" if source_status['available'] else "🔴"
        print(f"  {source_name}: {enabled} 启用 | {available} 可用 | 支持市场: {source_status['supported_markets']}")


async def test_single_stock_data():
    """测试单只股票数据获取"""
    print("\n📈 测试单只股票数据获取")
    print("=" * 50)
    
    test_cases = [
        ('000001', 'CN_A', '平安银行'),
        ('AAPL', 'US', '苹果公司'),
        ('0700', 'HK', '腾讯控股')
    ]
    
    for symbol, market, name in test_cases:
        print(f"\n🔍 测试 {name} ({symbol}, {market})")
        
        try:
            # 使用不同策略获取数据
            strategies = ['priority', 'fastest', 'most_reliable']
            
            for strategy in strategies:
                data = await enhanced_data_manager.get_stock_data(
                    symbol, market, strategy
                )
                
                if data:
                    print(f"  ✅ {strategy}策略: {data.source} | 价格: {data.current_price} | 质量: {data.extra_data.get('quality_score', 0):.2f}")
                else:
                    print(f"  ❌ {strategy}策略: 无数据")
                
        except Exception as e:
            print(f"  💥 测试失败: {e}")


async def test_batch_data():
    """测试批量数据获取"""
    print("\n📊 测试批量数据获取")
    print("=" * 50)
    
    # 测试中国A股批量获取
    cn_symbols = ['000001', '000002', '600000', '600036']
    print(f"🇨🇳 批量获取中国A股数据: {cn_symbols}")
    
    try:
        cn_data = await enhanced_data_manager.get_multiple_stocks(
            cn_symbols, 'CN_A', 'priority'
        )
        
        success_count = sum(1 for data in cn_data.values() if data is not None)
        print(f"  📈 成功获取: {success_count}/{len(cn_symbols)}")
        
        for symbol, data in cn_data.items():
            if data:
                print(f"    {symbol}: {data.name} | {data.current_price} | {data.source}")
            else:
                print(f"    {symbol}: 获取失败")
                
    except Exception as e:
        print(f"  💥 批量获取失败: {e}")


async def test_data_source_connectivity():
    """测试数据源连通性"""
    print("\n🌐 测试数据源连通性")
    print("=" * 50)
    
    # 测试中国A股数据源
    print("🇨🇳 测试中国A股数据源连通性:")
    cn_results = await enhanced_data_manager.test_data_sources('000001', 'CN_A')
    
    for source, result in cn_results.items():
        status = "✅" if result['success'] else "❌"
        response_time = result['response_time_ms']
        quality = result['data_quality']
        error = result['error'] or "无"
        
        print(f"  {source}: {status} | {response_time}ms | 质量:{quality:.2f} | 错误:{error}")
    
    # 如果有美股API密钥，测试美股数据源
    if os.environ.get('QBOT_FINNHUB_API_KEY'):
        print("\n🇺🇸 测试美股数据源连通性:")
        us_results = await enhanced_data_manager.test_data_sources('AAPL', 'US')
        
        for source, result in us_results.items():
            status = "✅" if result['success'] else "❌"
            response_time = result['response_time_ms']
            quality = result['data_quality']
            error = result['error'] or "无"
            
            print(f"  {source}: {status} | {response_time}ms | 质量:{quality:.2f} | 错误:{error}")


async def test_custom_data_source():
    """测试自定义数据源注册"""
    print("\n🔧 测试自定义数据源注册")
    print("=" * 50)
    
    # 这里可以实现一个简单的自定义数据源用于演示
    print("📝 自定义数据源功能已预留，可根据需要实现")


async def main():
    """主测试函数"""
    print("🚀 增强版数据管理器测试")
    print("=" * 60)
    
    try:
        # 1. 测试配置管理
        await test_configuration()
        
        # 2. 测试数据源状态
        await test_data_source_status()
        
        # 3. 测试单只股票数据获取
        await test_single_stock_data()
        
        # 4. 测试批量数据获取
        await test_batch_data()
        
        # 5. 测试数据源连通性
        await test_data_source_connectivity()
        
        # 6. 测试自定义数据源
        await test_custom_data_source()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成!")
        
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置一些测试用的环境变量（如果没有设置的话）
    if not os.environ.get('QBOT_SINA_ENABLED'):
        os.environ['QBOT_SINA_ENABLED'] = 'true'
    
    # 运行测试
    asyncio.run(main())
