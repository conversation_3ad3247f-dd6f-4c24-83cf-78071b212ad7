#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重构的股票筛选器
遵循单一职责原则，将复杂功能分解为独立的组件
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import asyncio
from abc import ABC, abstractmethod

from qbot.config.constants import (
    MarketType, NETWORK, SCORING, SCREENING, RECOMMENDATION, 
    DATA_QUALITY, PERFORMANCE, MARKET_CONFIGS
)
from qbot.data.stock_list_manager import stock_list_manager, StockInfo
from qbot.common.result import Result, ErrorCode, ErrorSeverity, success, failure
from qbot.common.enhanced_logger import get_logger


class IStockDataProvider(ABC):
    """股票数据提供者接口"""
    
    @abstractmethod
    async def get_stock_data(self, symbol: str, market: MarketType) -> Result[Dict[str, Any]]:
        """获取单只股票数据"""
        pass
    
    @abstractmethod
    async def get_batch_stock_data(self, symbols: List[str], market: MarketType) -> Result[List[Dict[str, Any]]]:
        """批量获取股票数据"""
        pass


class IStockFilter(ABC):
    """股票筛选器接口"""
    
    @abstractmethod
    def apply_filter(self, data: pd.DataFrame, criteria: Dict) -> pd.DataFrame:
        """应用筛选条件"""
        pass


class IStockScorer(ABC):
    """股票评分器接口"""
    
    @abstractmethod
    def calculate_score(self, data: pd.DataFrame) -> pd.Series:
        """计算股票评分"""
        pass


class StockListProvider:
    """股票列表提供者 - 单一职责：管理股票列表"""
    
    def __init__(self):
        self.logger = get_logger("stock_list_provider")
    
    async def get_stock_symbols(self, market: MarketType, 
                              limit: Optional[int] = None,
                              board_filter: Optional[List[str]] = None) -> Result[List[str]]:
        """获取股票代码列表"""
        operation_name = f"get_stock_symbols_{market.value}"
        self.logger.start_operation(operation_name, market=market.value, limit=limit)
        
        try:
            # 从股票列表管理器获取完整列表
            result = await stock_list_manager.get_stock_list(market)
            
            if not result.is_success():
                return result
            
            stock_list = result.data
            
            # 应用板块筛选
            if board_filter:
                stock_list = [s for s in stock_list if s.board in board_filter]
            
            # 提取股票代码
            symbols = [stock.symbol for stock in stock_list]
            
            # 应用数量限制
            if limit and limit > 0:
                symbols = symbols[:limit]
                self.logger.info(f"应用数量限制: {len(symbols)}/{len(stock_list)}")
            
            self.logger.end_operation(operation_name, success=True, count=len(symbols))
            return success(symbols)
            
        except Exception as e:
            self.logger.log_exception(e,
                context={'market': market.value, 'limit': limit},
                error_code=ErrorCode.DATA_SOURCE_UNAVAILABLE,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error=str(e))
            return failure(ErrorCode.DATA_SOURCE_UNAVAILABLE, 
                         f"获取{market.value}股票列表失败: {str(e)}")


class StockDataFetcher:
    """股票数据获取器 - 单一职责：获取股票数据"""
    
    def __init__(self, data_provider: IStockDataProvider):
        self.data_provider = data_provider
        self.logger = get_logger("stock_data_fetcher")
    
    async def fetch_single_stock(self, symbol: str, market: MarketType) -> Result[Dict[str, Any]]:
        """获取单只股票数据"""
        operation_name = f"fetch_single_stock_{symbol}"
        self.logger.start_operation(operation_name, symbol=symbol, market=market.value)
        
        try:
            result = await self.data_provider.get_stock_data(symbol, market)
            
            if result.is_success():
                # 数据验证
                validation_result = self._validate_stock_data(result.data, symbol)
                if not validation_result.is_success():
                    self.logger.end_operation(operation_name, success=False, 
                                            error="data_validation_failed")
                    return validation_result
                
                self.logger.end_operation(operation_name, success=True)
                return result
            else:
                self.logger.end_operation(operation_name, success=False, 
                                        error=result.get_error_message())
                return result
                
        except Exception as e:
            self.logger.log_exception(e,
                context={'symbol': symbol, 'market': market.value},
                error_code=ErrorCode.DATA_SOURCE_UNAVAILABLE,
                severity=ErrorSeverity.MEDIUM)
            
            self.logger.end_operation(operation_name, success=False, error=str(e))
            return failure(ErrorCode.DATA_SOURCE_UNAVAILABLE, 
                         f"获取{symbol}数据失败: {str(e)}")
    
    async def fetch_batch_stocks(self, symbols: List[str], market: MarketType,
                               batch_size: int = NETWORK.DEFAULT_BATCH_SIZE) -> Result[List[Dict[str, Any]]]:
        """批量获取股票数据"""
        operation_name = f"fetch_batch_stocks_{market.value}"
        self.logger.start_operation(operation_name, 
                                   symbol_count=len(symbols),
                                   batch_size=batch_size,
                                   market=market.value)
        
        try:
            all_data = []
            failed_count = 0
            
            # 分批处理
            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]
                
                try:
                    batch_result = await self.data_provider.get_batch_stock_data(batch_symbols, market)
                    
                    if batch_result.is_success():
                        # 验证批次数据
                        validated_data = []
                        for data in batch_result.data:
                            validation_result = self._validate_stock_data(data, data.get('symbol', ''))
                            if validation_result.is_success():
                                validated_data.append(data)
                            else:
                                failed_count += 1
                        
                        all_data.extend(validated_data)
                    else:
                        failed_count += len(batch_symbols)
                        self.logger.warning(f"批次{i//batch_size + 1}获取失败: {batch_result.get_error_message()}")
                
                except Exception as e:
                    failed_count += len(batch_symbols)
                    self.logger.warning(f"批次{i//batch_size + 1}异常: {e}")
                
                # 批次间休息
                if i + batch_size < len(symbols):
                    await asyncio.sleep(NETWORK.BATCH_INTERVAL)
            
            success_rate = len(all_data) / len(symbols) if symbols else 0
            
            self.logger.end_operation(operation_name, success=True,
                                    successful_count=len(all_data),
                                    failed_count=failed_count,
                                    success_rate=success_rate)
            
            result = success(all_data)
            result.add_metadata('success_rate', success_rate)
            result.add_metadata('failed_count', failed_count)
            return result
            
        except Exception as e:
            self.logger.log_exception(e,
                context={'symbol_count': len(symbols), 'market': market.value},
                error_code=ErrorCode.DATA_SOURCE_UNAVAILABLE,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error=str(e))
            return failure(ErrorCode.DATA_SOURCE_UNAVAILABLE, 
                         f"批量获取股票数据失败: {str(e)}")
    
    def _validate_stock_data(self, data: Dict[str, Any], symbol: str) -> Result[Dict[str, Any]]:
        """验证股票数据"""
        try:
            # 必需字段检查
            required_fields = ['symbol', 'current_price']
            missing_fields = [field for field in required_fields if field not in data or data[field] is None]
            
            if missing_fields:
                return failure(ErrorCode.DATA_INCOMPLETE,
                             f"股票{symbol}缺少必需字段: {missing_fields}")
            
            # 数据合理性检查
            price = data.get('current_price', 0)
            if price <= 0:
                return failure(ErrorCode.DATA_INCONSISTENT,
                             f"股票{symbol}价格异常: {price}")
            
            volume = data.get('volume', 0)
            if volume < 0:
                return failure(ErrorCode.DATA_INCONSISTENT,
                             f"股票{symbol}成交量异常: {volume}")
            
            return success(data)
            
        except Exception as e:
            return failure(ErrorCode.DATA_QUALITY_LOW,
                         f"股票{symbol}数据验证失败: {str(e)}")


class FundamentalFilter(IStockFilter):
    """基本面筛选器 - 单一职责：基本面筛选"""
    
    def __init__(self):
        self.logger = get_logger("fundamental_filter")
    
    def apply_filter(self, data: pd.DataFrame, criteria: Dict) -> pd.DataFrame:
        """应用基本面筛选条件"""
        if data.empty:
            return data
        
        mask = pd.Series(True, index=data.index)
        fundamental = criteria.get('fundamental', {})
        
        # PE比率筛选
        if 'pe_ratio' in data.columns:
            pe_min = fundamental.get('pe_ratio', {}).get('min', SCREENING.PE_RATIO_MIN)
            pe_max = fundamental.get('pe_ratio', {}).get('max', SCREENING.PE_RATIO_MAX)
            pe_condition = (data['pe_ratio'] >= pe_min) & (data['pe_ratio'] <= pe_max)
            mask = mask & pe_condition
            self.logger.debug(f"PE筛选: {pe_condition.sum()}/{len(data)} 通过")
        
        # PB比率筛选
        if 'pb_ratio' in data.columns:
            pb_min = fundamental.get('pb_ratio', {}).get('min', SCREENING.PB_RATIO_MIN)
            pb_max = fundamental.get('pb_ratio', {}).get('max', SCREENING.PB_RATIO_MAX)
            pb_condition = (data['pb_ratio'] >= pb_min) & (data['pb_ratio'] <= pb_max)
            mask = mask & pb_condition
            self.logger.debug(f"PB筛选: {pb_condition.sum()}/{len(data)} 通过")
        
        # ROE筛选
        if 'roe' in data.columns:
            roe_min = fundamental.get('roe', {}).get('min', SCREENING.ROE_MIN)
            roe_max = fundamental.get('roe', {}).get('max', SCREENING.ROE_MAX)
            roe_condition = (data['roe'] >= roe_min) & (data['roe'] <= roe_max)
            mask = mask & roe_condition
            self.logger.debug(f"ROE筛选: {roe_condition.sum()}/{len(data)} 通过")
        
        # 负债率筛选
        if 'debt_ratio' in data.columns:
            debt_min = fundamental.get('debt_ratio', {}).get('min', SCREENING.DEBT_RATIO_MIN)
            debt_max = fundamental.get('debt_ratio', {}).get('max', SCREENING.DEBT_RATIO_MAX)
            debt_condition = (data['debt_ratio'] >= debt_min) & (data['debt_ratio'] <= debt_max)
            mask = mask & debt_condition
            self.logger.debug(f"负债率筛选: {debt_condition.sum()}/{len(data)} 通过")
        
        return data[mask].copy()


class TechnicalFilter(IStockFilter):
    """技术面筛选器 - 单一职责：技术面筛选"""
    
    def __init__(self):
        self.logger = get_logger("technical_filter")
    
    def apply_filter(self, data: pd.DataFrame, criteria: Dict) -> pd.DataFrame:
        """应用技术面筛选条件"""
        if data.empty:
            return data
        
        mask = pd.Series(True, index=data.index)
        technical = criteria.get('technical', {})
        
        # RSI筛选
        if 'rsi' in data.columns:
            rsi_min = technical.get('rsi_min', SCREENING.RSI_OVERSOLD)
            rsi_max = technical.get('rsi_max', SCREENING.RSI_OVERBOUGHT)
            rsi_condition = (data['rsi'] >= rsi_min) & (data['rsi'] <= rsi_max)
            mask = mask & rsi_condition
            self.logger.debug(f"RSI筛选: {rsi_condition.sum()}/{len(data)} 通过")
        
        # 均线趋势筛选
        ma_trend = technical.get('ma_trend', '不限')
        if ma_trend != '不限' and 'ma_trend' in data.columns:
            trend_condition = data['ma_trend'] == ma_trend
            mask = mask & trend_condition
            self.logger.debug(f"均线趋势筛选: {trend_condition.sum()}/{len(data)} 通过")
        
        # 价格筛选
        if 'current_price' in data.columns:
            price_min = technical.get('price_min', SCREENING.MIN_PRICE)
            price_max = technical.get('price_max', SCREENING.MAX_PRICE)
            price_condition = (data['current_price'] >= price_min) & (data['current_price'] <= price_max)
            mask = mask & price_condition
            self.logger.debug(f"价格筛选: {price_condition.sum()}/{len(data)} 通过")
        
        return data[mask].copy()


class LiquidityFilter(IStockFilter):
    """流动性筛选器 - 单一职责：流动性筛选"""
    
    def __init__(self):
        self.logger = get_logger("liquidity_filter")
    
    def apply_filter(self, data: pd.DataFrame, criteria: Dict) -> pd.DataFrame:
        """应用流动性筛选条件"""
        if data.empty:
            return data
        
        mask = pd.Series(True, index=data.index)
        liquidity = criteria.get('liquidity', {})
        
        # 成交量筛选
        if 'volume' in data.columns:
            volume_min = liquidity.get('volume_min', SCREENING.MIN_VOLUME)
            volume_condition = data['volume'] >= volume_min
            mask = mask & volume_condition
            self.logger.debug(f"成交量筛选: {volume_condition.sum()}/{len(data)} 通过")
        
        # 流动性比率筛选
        if 'liquidity_ratio' in data.columns:
            liquidity_min = liquidity.get('liquidity_min', SCREENING.MIN_LIQUIDITY_RATIO)
            liquidity_condition = data['liquidity_ratio'] >= liquidity_min
            mask = mask & liquidity_condition
            self.logger.debug(f"流动性比率筛选: {liquidity_condition.sum()}/{len(data)} 通过")
        
        # 市值筛选
        if 'market_cap' in data.columns:
            market_cap_min = liquidity.get('market_cap_min', SCREENING.MIN_MARKET_CAP)
            market_cap_condition = data['market_cap'] >= market_cap_min
            mask = mask & market_cap_condition
            self.logger.debug(f"市值筛选: {market_cap_condition.sum()}/{len(data)} 通过")
        
        return data[mask].copy()


class CompositeStockFilter:
    """复合股票筛选器 - 单一职责：组合多个筛选器"""
    
    def __init__(self):
        self.logger = get_logger("composite_filter")
        self.filters = {
            'fundamental': FundamentalFilter(),
            'technical': TechnicalFilter(),
            'liquidity': LiquidityFilter()
        }
    
    def apply_all_filters(self, data: pd.DataFrame, criteria: Dict) -> pd.DataFrame:
        """应用所有筛选条件"""
        operation_name = "apply_all_filters"
        self.logger.start_operation(operation_name, input_rows=len(data))
        
        try:
            filtered_data = data.copy()
            
            # 依次应用各个筛选器
            for filter_name, filter_instance in self.filters.items():
                if filter_name in criteria:
                    before_count = len(filtered_data)
                    filtered_data = filter_instance.apply_filter(filtered_data, criteria)
                    after_count = len(filtered_data)
                    
                    self.logger.debug(f"{filter_name}筛选: {before_count} -> {after_count}")
            
            filter_ratio = len(filtered_data) / len(data) if len(data) > 0 else 0
            
            self.logger.end_operation(operation_name, success=True,
                                    input_rows=len(data),
                                    output_rows=len(filtered_data),
                                    filter_ratio=filter_ratio)
            
            return filtered_data
            
        except Exception as e:
            self.logger.log_exception(e,
                context={'input_rows': len(data)},
                error_code=ErrorCode.UNKNOWN_ERROR,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error=str(e))
            return data  # 返回原始数据


class FundamentalScorer(IStockScorer):
    """基本面评分器 - 单一职责：基本面评分"""

    def __init__(self):
        self.logger = get_logger("fundamental_scorer")

    def calculate_score(self, data: pd.DataFrame) -> pd.Series:
        """计算基本面评分"""
        if data.empty:
            return pd.Series(dtype=float)

        scores = pd.Series(0.0, index=data.index)

        # PE比率评分
        if 'pe_ratio' in data.columns:
            pe_condition = (data['pe_ratio'] > 0) & (data['pe_ratio'] < SCREENING.PE_RATIO_GOOD_MAX)
            scores += np.where(pe_condition, SCORING.PE_RATIO_WEIGHT, 0.0)

        # PB比率评分
        if 'pb_ratio' in data.columns:
            pb_condition = (data['pb_ratio'] > 0) & (data['pb_ratio'] < SCREENING.PB_RATIO_GOOD_MAX)
            scores += np.where(pb_condition, SCORING.PB_RATIO_WEIGHT, 0.0)

        # ROE评分
        if 'roe' in data.columns:
            roe_condition = data['roe'] > SCREENING.ROE_GOOD_MIN
            scores += np.where(roe_condition, SCORING.ROE_WEIGHT, 0.0)

        # 负债率评分
        if 'debt_ratio' in data.columns:
            debt_condition = data['debt_ratio'] < SCREENING.DEBT_RATIO_GOOD_MAX
            scores += np.where(debt_condition, SCORING.DEBT_RATIO_WEIGHT, 0.0)

        return scores


class TechnicalScorer(IStockScorer):
    """技术面评分器 - 单一职责：技术面评分"""

    def __init__(self):
        self.logger = get_logger("technical_scorer")

    def calculate_score(self, data: pd.DataFrame) -> pd.Series:
        """计算技术面评分"""
        if data.empty:
            return pd.Series(dtype=float)

        scores = pd.Series(0.0, index=data.index)

        # RSI评分
        if 'rsi' in data.columns:
            rsi_condition = (data['rsi'] >= SCREENING.RSI_OVERSOLD) & (data['rsi'] <= SCREENING.RSI_OVERBOUGHT)
            scores += np.where(rsi_condition, SCORING.RSI_WEIGHT, 0.0)

        # 均线趋势评分
        if 'ma_trend' in data.columns:
            trend_condition = data['ma_trend'] == '上升'
            scores += np.where(trend_condition, SCORING.MA_TREND_WEIGHT, 0.0)

        return scores


class LiquidityScorer(IStockScorer):
    """流动性评分器 - 单一职责：流动性评分"""

    def __init__(self):
        self.logger = get_logger("liquidity_scorer")

    def calculate_score(self, data: pd.DataFrame) -> pd.Series:
        """计算流动性评分"""
        if data.empty:
            return pd.Series(dtype=float)

        scores = pd.Series(0.0, index=data.index)

        # 流动性比率评分
        if 'liquidity_ratio' in data.columns:
            # 标准化流动性比率到0-1范围
            normalized_liquidity = np.minimum(data['liquidity_ratio'] / SCREENING.GOOD_LIQUIDITY_RATIO, 1.0)
            scores += normalized_liquidity

        return scores


class CompositeStockScorer:
    """复合股票评分器 - 单一职责：组合多个评分器"""

    def __init__(self):
        self.logger = get_logger("composite_scorer")
        self.scorers = {
            'fundamental': FundamentalScorer(),
            'technical': TechnicalScorer(),
            'liquidity': LiquidityScorer()
        }

    def calculate_total_score(self, data: pd.DataFrame) -> pd.Series:
        """计算综合评分"""
        operation_name = "calculate_total_score"
        self.logger.start_operation(operation_name, rows=len(data))

        try:
            if data.empty:
                return pd.Series(dtype=float)

            # 计算各项评分
            fundamental_scores = self.scorers['fundamental'].calculate_score(data)
            technical_scores = self.scorers['technical'].calculate_score(data)
            liquidity_scores = self.scorers['liquidity'].calculate_score(data)

            # 加权合并
            total_scores = (
                fundamental_scores * SCORING.FUNDAMENTAL_WEIGHT +
                technical_scores * SCORING.TECHNICAL_WEIGHT +
                liquidity_scores * SCORING.LIQUIDITY_WEIGHT
            ) * 100  # 转换为百分制

            # 确保评分在合理范围内
            total_scores = total_scores.clip(0, 100)

            self.logger.end_operation(operation_name, success=True,
                                    rows=len(data),
                                    avg_score=total_scores.mean(),
                                    max_score=total_scores.max(),
                                    min_score=total_scores.min())

            return total_scores

        except Exception as e:
            self.logger.log_exception(e,
                context={'data_shape': data.shape},
                error_code=ErrorCode.UNKNOWN_ERROR,
                severity=ErrorSeverity.HIGH)

            self.logger.end_operation(operation_name, success=False, error=str(e))
            return pd.Series([50.0] * len(data), index=data.index)


class StockRecommendationEngine:
    """股票推荐引擎 - 单一职责：生成投资建议和风险评估"""

    def __init__(self):
        self.logger = get_logger("recommendation_engine")

    def generate_recommendation(self, score: float) -> str:
        """生成投资建议"""
        if score >= RECOMMENDATION.STRONG_BUY_THRESHOLD:
            return '强烈买入'
        elif score >= RECOMMENDATION.BUY_THRESHOLD:
            return '买入'
        elif score >= RECOMMENDATION.HOLD_THRESHOLD:
            return '持有'
        elif score >= RECOMMENDATION.SELL_THRESHOLD:
            return '卖出'
        else:
            return '强烈卖出'

    def assess_risk_level(self, volatility: float, debt_ratio: float) -> str:
        """评估风险等级"""
        try:
            # 使用配置的权重计算风险评分
            risk_score = volatility * 0.6 + debt_ratio * 0.4

            if risk_score >= RECOMMENDATION.HIGH_RISK_THRESHOLD:
                return '高风险'
            elif risk_score >= RECOMMENDATION.LOW_RISK_THRESHOLD:
                return '中等风险'
            else:
                return '低风险'

        except Exception:
            return '中等风险'  # 默认风险等级

    def generate_stock_recommendations(self, data: pd.DataFrame, scores: pd.Series) -> List[Dict[str, Any]]:
        """生成股票推荐列表"""
        operation_name = "generate_recommendations"
        self.logger.start_operation(operation_name, rows=len(data))

        try:
            if data.empty or scores.empty:
                return []

            # 添加评分到数据中
            data_with_scores = data.copy()
            data_with_scores['total_score'] = scores

            # 按评分排序
            ranked_data = data_with_scores.sort_values('total_score', ascending=False).reset_index(drop=True)

            # 生成推荐列表
            recommendations = []
            for idx, row in ranked_data.iterrows():
                try:
                    recommendation = {
                        'rank': idx + 1,
                        'symbol': row.get('symbol', ''),
                        'name': row.get('name', ''),
                        'market': row.get('market', ''),
                        'current_price': round(row.get('current_price', 0), 2),
                        'market_cap': row.get('market_cap', 0),
                        'pe_ratio': round(row.get('pe_ratio', 0), 2),
                        'pb_ratio': round(row.get('pb_ratio', 0), 2),
                        'roe': round(row.get('roe', 0), 4),
                        'total_score': round(row.get('total_score', 50), 2),
                        'recommendation': self.generate_recommendation(row.get('total_score', 50)),
                        'risk_level': self.assess_risk_level(
                            row.get('volatility', 0.3),
                            row.get('debt_ratio', 0.5)
                        )
                    }
                    recommendations.append(recommendation)

                except Exception as e:
                    self.logger.warning(f"生成第{idx+1}个推荐失败: {e}")
                    continue

            self.logger.end_operation(operation_name, success=True,
                                    input_rows=len(data),
                                    output_recommendations=len(recommendations))

            return recommendations

        except Exception as e:
            self.logger.log_exception(e,
                context={'data_shape': data.shape, 'scores_shape': scores.shape},
                error_code=ErrorCode.UNKNOWN_ERROR,
                severity=ErrorSeverity.HIGH)

            self.logger.end_operation(operation_name, success=False, error=str(e))
            return []


class RefactoredStockScreener:
    """重构的股票筛选器主控制器 - 单一职责：协调各个组件"""

    def __init__(self, data_provider: IStockDataProvider):
        self.logger = get_logger("refactored_stock_screener")

        # 组件依赖注入
        self.stock_list_provider = StockListProvider()
        self.data_fetcher = StockDataFetcher(data_provider)
        self.filter_engine = CompositeStockFilter()
        self.scoring_engine = CompositeStockScorer()
        self.recommendation_engine = StockRecommendationEngine()

    async def screen_stocks(self, market: MarketType, criteria: Dict,
                          limit: Optional[int] = None) -> Result[List[Dict[str, Any]]]:
        """执行股票筛选 - 主要业务流程"""
        operation_name = f"screen_stocks_{market.value}"
        self.logger.start_operation(operation_name, market=market.value, limit=limit)

        try:
            # 1. 获取股票列表
            symbols_result = await self.stock_list_provider.get_stock_symbols(market, limit)
            if not symbols_result.is_success():
                return symbols_result

            symbols = symbols_result.data
            self.logger.info(f"获取到{len(symbols)}只{market.value}股票")

            # 2. 获取股票数据
            data_result = await self.data_fetcher.fetch_batch_stocks(symbols, market)
            if not data_result.is_success():
                return data_result

            stock_data_list = data_result.data
            self.logger.info(f"成功获取{len(stock_data_list)}只股票数据")

            # 3. 转换为DataFrame
            if not stock_data_list:
                return success([])

            df = pd.DataFrame(stock_data_list)

            # 4. 应用筛选条件
            filtered_df = self.filter_engine.apply_all_filters(df, criteria)
            self.logger.info(f"筛选后剩余{len(filtered_df)}只股票")

            # 5. 计算评分
            scores = self.scoring_engine.calculate_total_score(filtered_df)

            # 6. 生成推荐
            recommendations = self.recommendation_engine.generate_stock_recommendations(filtered_df, scores)

            self.logger.end_operation(operation_name, success=True,
                                    input_symbols=len(symbols),
                                    filtered_stocks=len(filtered_df),
                                    recommendations=len(recommendations))

            result = success(recommendations)
            result.add_metadata('market', market.value)
            result.add_metadata('total_symbols', len(symbols))
            result.add_metadata('filtered_count', len(filtered_df))
            result.add_metadata('success_rate', data_result.metadata.get('success_rate', 0))

            return result

        except Exception as e:
            self.logger.log_exception(e,
                context={'market': market.value, 'limit': limit},
                error_code=ErrorCode.UNKNOWN_ERROR,
                severity=ErrorSeverity.HIGH)

            self.logger.end_operation(operation_name, success=False, error=str(e))
            return failure(ErrorCode.UNKNOWN_ERROR,
                         f"股票筛选失败: {str(e)}")
