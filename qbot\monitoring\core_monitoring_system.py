#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心监控指标体系
三层监控架构：市场状态层、策略执行层、资金防护层
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod
import warnings
warnings.filterwarnings('ignore')

try:
    from scipy import stats
    from sklearn.preprocessing import StandardScaler
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("⚠️ SciPy未安装，将使用简化版本")

logger = logging.getLogger(__name__)

@dataclass
class MonitoringAlert:
    """监控告警"""
    timestamp: datetime
    level: str              # INFO, WARNING, CRITICAL
    layer: str              # MARKET, STRATEGY, CAPITAL
    indicator: str          # 指标名称
    current_value: float    # 当前值
    threshold: float        # 阈值
    message: str           # 告警信息
    metadata: Dict[str, Any] # 附加信息

@dataclass
class MarketState:
    """市场状态"""
    timestamp: datetime
    volatility_regime: str      # LOW, NORMAL, HIGH, EXTREME
    liquidity_level: str        # DEEP, NORMAL, SHALLOW, DRIED
    risk_level: str            # LOW, MEDIUM, HIGH, CRITICAL
    market_stress: float       # 市场压力指数 0-1
    confidence: float          # 状态置信度 0-1

class MarketStateMonitor:
    """市场状态层监控"""
    
    def __init__(self, params: Dict[str, Any] = None):
        self.params = params or {
            'volatility_window': 20,
            'volatility_thresholds': [0.01, 0.02, 0.04],  # 1%, 2%, 4%
            'liquidity_window': 10,
            'liquidity_thresholds': [0.5, 1.0, 2.0],      # 流动性深度倍数
            'extreme_risk_threshold': 0.05,                # 5%极端风险
            'stress_lookback': 252                         # 压力测试回看期
        }
        
        self.alerts = []
        self.market_history = []
        
    def analyze_volatility_structure(self, price_data: pd.DataFrame) -> Dict[str, Any]:
        """波动率结构分析"""
        try:
            returns = price_data['close'].pct_change().dropna()
            
            # 实现波动率
            realized_vol = returns.rolling(self.params['volatility_window']).std() * np.sqrt(252)
            current_vol = realized_vol.iloc[-1] if len(realized_vol) > 0 else 0
            
            # 波动率分位数
            vol_percentile = stats.percentileofscore(realized_vol.dropna(), current_vol) / 100
            
            # GARCH波动率预测（简化版）
            garch_vol = self._estimate_garch_volatility(returns)
            
            # 波动率偏斜度
            vol_skew = returns.rolling(self.params['volatility_window']).skew().iloc[-1]
            
            # 波动率峰度
            vol_kurtosis = returns.rolling(self.params['volatility_window']).kurtosis().iloc[-1]
            
            # 波动率状态分类
            vol_regime = self._classify_volatility_regime(current_vol)
            
            # 波动率聚集性检测
            vol_clustering = self._detect_volatility_clustering(realized_vol)
            
            result = {
                'realized_volatility': current_vol,
                'volatility_percentile': vol_percentile,
                'garch_volatility': garch_vol,
                'volatility_skew': vol_skew,
                'volatility_kurtosis': vol_kurtosis,
                'volatility_regime': vol_regime,
                'volatility_clustering': vol_clustering,
                'vol_of_vol': realized_vol.rolling(20).std().iloc[-1] if len(realized_vol) > 20 else 0
            }
            
            # 生成告警
            if current_vol > self.params['volatility_thresholds'][2]:
                self._generate_alert('CRITICAL', 'MARKET', 'volatility_extreme',
                                   current_vol, self.params['volatility_thresholds'][2],
                                   f"极端波动率: {current_vol:.4f}")
                                   
            return result
            
        except Exception as e:
            logger.error(f"波动率结构分析失败: {e}")
            return {}
            
    def analyze_liquidity_depth(self, orderbook_data: pd.DataFrame, 
                               trade_data: pd.DataFrame) -> Dict[str, Any]:
        """流动性深度分析"""
        try:
            # 买卖盘深度
            if 'bid_volume' in orderbook_data.columns and 'ask_volume' in orderbook_data.columns:
                bid_depth = orderbook_data['bid_volume'].sum()
                ask_depth = orderbook_data['ask_volume'].sum()
                total_depth = bid_depth + ask_depth
                depth_imbalance = abs(bid_depth - ask_depth) / total_depth if total_depth > 0 else 0
            else:
                # 使用成交量作为流动性代理
                total_depth = orderbook_data['volume'].rolling(self.params['liquidity_window']).mean().iloc[-1]
                depth_imbalance = 0
                
            # 价差分析
            if 'bid_price' in orderbook_data.columns and 'ask_price' in orderbook_data.columns:
                spread = orderbook_data['ask_price'] - orderbook_data['bid_price']
                relative_spread = spread / orderbook_data['close']
                avg_spread = relative_spread.rolling(self.params['liquidity_window']).mean().iloc[-1]
            else:
                avg_spread = 0.001  # 默认价差
                
            # 市场冲击成本
            market_impact = self._calculate_market_impact(trade_data)
            
            # 流动性弹性
            liquidity_resilience = self._calculate_liquidity_resilience(orderbook_data)
            
            # Kyle's Lambda (价格冲击系数)
            kyles_lambda = self._calculate_kyles_lambda(trade_data)
            
            # 流动性状态分类
            liquidity_level = self._classify_liquidity_level(total_depth, avg_spread, market_impact)
            
            # Amihud非流动性指标
            amihud_illiquidity = self._calculate_amihud_illiquidity(trade_data)
            
            result = {
                'total_depth': total_depth,
                'depth_imbalance': depth_imbalance,
                'average_spread': avg_spread,
                'market_impact': market_impact,
                'liquidity_resilience': liquidity_resilience,
                'kyles_lambda': kyles_lambda,
                'liquidity_level': liquidity_level,
                'amihud_illiquidity': amihud_illiquidity,
                'bid_ask_ratio': bid_depth / ask_depth if 'bid_volume' in orderbook_data.columns and ask_depth > 0 else 1.0
            }
            
            # 生成告警
            if avg_spread > 0.005:  # 价差超过0.5%
                self._generate_alert('WARNING', 'MARKET', 'liquidity_shallow',
                                   avg_spread, 0.005,
                                   f"流动性不足，价差过大: {avg_spread:.4f}")
                                   
            return result
            
        except Exception as e:
            logger.error(f"流动性深度分析失败: {e}")
            return {}
            
    def detect_extreme_risk(self, price_data: pd.DataFrame, 
                          volume_data: pd.DataFrame = None) -> Dict[str, Any]:
        """极端风险检测"""
        try:
            returns = price_data['close'].pct_change().dropna()
            
            # 尾部风险测量
            var_95 = np.percentile(returns, 5)  # 5% VaR
            var_99 = np.percentile(returns, 1)  # 1% VaR
            expected_shortfall = returns[returns <= var_95].mean()  # 期望损失
            
            # 极值理论分析
            extreme_returns = returns[abs(returns) > np.percentile(abs(returns), 95)]
            tail_index = self._estimate_tail_index(extreme_returns)
            
            # 跳跃检测
            jump_intensity = self._detect_price_jumps(returns)
            
            # 系统性风险指标
            if volume_data is not None:
                volume_shock = self._detect_volume_shock(volume_data)
            else:
                volume_shock = 0
                
            # 市场崩盘概率
            crash_probability = self._estimate_crash_probability(returns)
            
            # 流动性风险
            liquidity_risk = self._assess_liquidity_risk(returns, volume_data)
            
            # 极端风险综合评分
            extreme_risk_score = self._calculate_extreme_risk_score(
                var_99, expected_shortfall, jump_intensity, crash_probability
            )
            
            result = {
                'var_95': var_95,
                'var_99': var_99,
                'expected_shortfall': expected_shortfall,
                'tail_index': tail_index,
                'jump_intensity': jump_intensity,
                'volume_shock': volume_shock,
                'crash_probability': crash_probability,
                'liquidity_risk': liquidity_risk,
                'extreme_risk_score': extreme_risk_score,
                'max_drawdown': self._calculate_max_drawdown(price_data['close'])
            }
            
            # 生成告警
            if extreme_risk_score > self.params['extreme_risk_threshold']:
                self._generate_alert('CRITICAL', 'MARKET', 'extreme_risk',
                                   extreme_risk_score, self.params['extreme_risk_threshold'],
                                   f"检测到极端风险: {extreme_risk_score:.4f}")
                                   
            return result
            
        except Exception as e:
            logger.error(f"极端风险检测失败: {e}")
            return {}
            
    def get_market_state(self, price_data: pd.DataFrame, 
                        orderbook_data: pd.DataFrame = None,
                        trade_data: pd.DataFrame = None) -> MarketState:
        """获取综合市场状态"""
        try:
            # 分析各个维度
            vol_analysis = self.analyze_volatility_structure(price_data)
            
            if orderbook_data is not None and trade_data is not None:
                liquidity_analysis = self.analyze_liquidity_depth(orderbook_data, trade_data)
            else:
                liquidity_analysis = self.analyze_liquidity_depth(price_data, price_data)
                
            risk_analysis = self.detect_extreme_risk(price_data)
            
            # 综合评估
            volatility_regime = vol_analysis.get('volatility_regime', 'NORMAL')
            liquidity_level = liquidity_analysis.get('liquidity_level', 'NORMAL')
            
            # 市场压力指数
            market_stress = self._calculate_market_stress(vol_analysis, liquidity_analysis, risk_analysis)
            
            # 风险等级
            risk_level = self._assess_overall_risk(volatility_regime, liquidity_level, market_stress)
            
            # 状态置信度
            confidence = self._calculate_state_confidence(vol_analysis, liquidity_analysis, risk_analysis)
            
            market_state = MarketState(
                timestamp=datetime.now(),
                volatility_regime=volatility_regime,
                liquidity_level=liquidity_level,
                risk_level=risk_level,
                market_stress=market_stress,
                confidence=confidence
            )
            
            self.market_history.append(market_state)
            
            return market_state
            
        except Exception as e:
            logger.error(f"市场状态评估失败: {e}")
            return MarketState(
                timestamp=datetime.now(),
                volatility_regime='UNKNOWN',
                liquidity_level='UNKNOWN',
                risk_level='UNKNOWN',
                market_stress=0.5,
                confidence=0.0
            )
            
    def _estimate_garch_volatility(self, returns: pd.Series) -> float:
        """GARCH波动率估计（简化版）"""
        try:
            # 简化的GARCH(1,1)模型
            alpha = 0.1  # ARCH参数
            beta = 0.85  # GARCH参数
            omega = 0.00001  # 常数项
            
            squared_returns = returns ** 2
            garch_vol = omega
            
            for i in range(1, len(squared_returns)):
                garch_vol = omega + alpha * squared_returns.iloc[i-1] + beta * garch_vol
                
            return np.sqrt(garch_vol * 252)  # 年化
            
        except Exception as e:
            logger.error(f"GARCH波动率估计失败: {e}")
            return returns.std() * np.sqrt(252)
            
    def _classify_volatility_regime(self, volatility: float) -> str:
        """波动率状态分类"""
        thresholds = self.params['volatility_thresholds']
        
        if volatility < thresholds[0]:
            return 'LOW'
        elif volatility < thresholds[1]:
            return 'NORMAL'
        elif volatility < thresholds[2]:
            return 'HIGH'
        else:
            return 'EXTREME'
            
    def _detect_volatility_clustering(self, volatility_series: pd.Series) -> float:
        """波动率聚集性检测"""
        try:
            # 计算波动率的自相关性
            autocorr = volatility_series.autocorr(lag=1)
            return autocorr if not np.isnan(autocorr) else 0
        except:
            return 0
            
    def _calculate_market_impact(self, trade_data: pd.DataFrame) -> float:
        """计算市场冲击成本"""
        try:
            if 'volume' in trade_data.columns and 'close' in trade_data.columns:
                # 简化的市场冲击模型
                volume_ratio = trade_data['volume'] / trade_data['volume'].rolling(20).mean()
                price_change = trade_data['close'].pct_change().abs()
                
                # 冲击成本 = 价格变化 / 成交量比率
                impact = (price_change * volume_ratio).rolling(10).mean().iloc[-1]
                return impact if not np.isnan(impact) else 0.001
            else:
                return 0.001
        except:
            return 0.001

    def _calculate_liquidity_resilience(self, orderbook_data: pd.DataFrame) -> float:
        """计算流动性弹性"""
        try:
            # 简化版本：价格恢复速度
            if 'close' in orderbook_data.columns:
                price_changes = orderbook_data['close'].pct_change()
                recovery_speed = abs(price_changes.rolling(5).mean().iloc[-1])
                return 1 / (1 + recovery_speed) if recovery_speed > 0 else 1.0
            return 1.0
        except:
            return 1.0

    def _calculate_kyles_lambda(self, trade_data: pd.DataFrame) -> float:
        """计算Kyle's Lambda价格冲击系数"""
        try:
            if 'volume' in trade_data.columns and 'close' in trade_data.columns:
                volume = trade_data['volume']
                price_change = trade_data['close'].pct_change().abs()

                # Lambda = 价格变化 / 成交量
                lambda_values = price_change / volume
                return lambda_values.rolling(20).mean().iloc[-1] if len(lambda_values) > 0 else 0
            return 0
        except:
            return 0

    def _classify_liquidity_level(self, depth: float, spread: float, impact: float) -> str:
        """流动性水平分类"""
        # 综合评分
        depth_score = 1 if depth > 1000000 else 0.5 if depth > 500000 else 0
        spread_score = 1 if spread < 0.001 else 0.5 if spread < 0.003 else 0
        impact_score = 1 if impact < 0.001 else 0.5 if impact < 0.005 else 0

        total_score = (depth_score + spread_score + impact_score) / 3

        if total_score > 0.8:
            return 'DEEP'
        elif total_score > 0.5:
            return 'NORMAL'
        elif total_score > 0.2:
            return 'SHALLOW'
        else:
            return 'DRIED'

    def _calculate_amihud_illiquidity(self, trade_data: pd.DataFrame) -> float:
        """计算Amihud非流动性指标"""
        try:
            if 'volume' in trade_data.columns and 'close' in trade_data.columns:
                returns = trade_data['close'].pct_change().abs()
                volume = trade_data['volume']
                dollar_volume = volume * trade_data['close']

                # Amihud = |return| / dollar_volume
                amihud = returns / dollar_volume
                return amihud.rolling(20).mean().iloc[-1] if len(amihud) > 0 else 0
            return 0
        except:
            return 0

    def _estimate_tail_index(self, extreme_returns: pd.Series) -> float:
        """估计尾部指数"""
        try:
            if len(extreme_returns) > 10:
                # Hill估计量
                sorted_returns = np.sort(np.abs(extreme_returns))[::-1]
                n = len(sorted_returns)
                k = min(n // 4, 50)  # 选择前25%或50个观测值

                if k > 1:
                    hill_estimator = np.mean(np.log(sorted_returns[:k])) - np.log(sorted_returns[k])
                    return 1 / hill_estimator if hill_estimator > 0 else 3.0
            return 3.0  # 默认值
        except (ValueError, ZeroDivisionError, IndexError) as e:
            self.logger.warning(f"估计尾部指数失败: {e}")
            return 3.0
        except Exception as e:
            self.logger.error(f"估计尾部指数异常: {e}")
            return 3.0

    def _detect_price_jumps(self, returns: pd.Series) -> float:
        """检测价格跳跃"""
        try:
            # 使用Barndorff-Nielsen和Shephard跳跃检测
            threshold = 3 * returns.std()  # 3倍标准差
            jumps = abs(returns) > threshold
            jump_intensity = jumps.rolling(20).mean().iloc[-1] if len(jumps) > 0 else 0
            return jump_intensity
        except (ValueError, IndexError, AttributeError) as e:
            self.logger.warning(f"检测价格跳跃失败: {e}")
            return 0
        except Exception as e:
            self.logger.error(f"检测价格跳跃异常: {e}")
            return 0

    def _detect_volume_shock(self, volume_data: pd.DataFrame) -> float:
        """检测成交量冲击"""
        try:
            volume = volume_data['volume'] if 'volume' in volume_data.columns else volume_data.iloc[:, 0]
            volume_ma = volume.rolling(20).mean()
            volume_ratio = volume / volume_ma

            # 成交量异常倍数
            shock_threshold = 3.0
            shocks = volume_ratio > shock_threshold
            shock_intensity = shocks.rolling(10).mean().iloc[-1] if len(shocks) > 0 else 0
            return shock_intensity
        except:
            return 0

    def _estimate_crash_probability(self, returns: pd.Series) -> float:
        """估计市场崩盘概率"""
        try:
            # 基于极值理论的崩盘概率
            threshold = np.percentile(returns, 5)  # 5%分位数
            exceedances = returns[returns < threshold]

            if len(exceedances) > 5:
                # 广义帕累托分布参数估计
                scale = np.std(exceedances)
                shape = -0.1  # 简化假设

                # 崩盘概率（超过-10%的概率）
                crash_threshold = -0.1
                if crash_threshold < threshold:
                    prob = len(exceedances) / len(returns) * (1 + shape * (crash_threshold - threshold) / scale) ** (-1/shape)
                    return min(prob, 1.0)
            return 0.01  # 默认1%概率
        except (ValueError, ZeroDivisionError, OverflowError) as e:
            self.logger.warning(f"估计崩盘概率失败: {e}")
            return 0.01
        except Exception as e:
            self.logger.error(f"估计崩盘概率异常: {e}")
            return 0.01

    def _assess_liquidity_risk(self, returns: pd.Series, volume_data: pd.DataFrame = None) -> float:
        """评估流动性风险"""
        try:
            # 流动性风险 = 价格冲击 + 成交量风险
            price_volatility = returns.std()

            if volume_data is not None and 'volume' in volume_data.columns:
                volume_volatility = volume_data['volume'].pct_change().std()
                liquidity_risk = price_volatility + 0.5 * volume_volatility
            else:
                liquidity_risk = price_volatility * 1.5  # 无成交量数据时的惩罚

            return min(liquidity_risk, 1.0)
        except:
            return 0.1

    def _calculate_extreme_risk_score(self, var_99: float, expected_shortfall: float,
                                    jump_intensity: float, crash_probability: float) -> float:
        """计算极端风险综合评分"""
        try:
            # 标准化各项指标
            var_score = min(abs(var_99) / 0.1, 1.0)  # VaR标准化到10%
            es_score = min(abs(expected_shortfall) / 0.15, 1.0)  # ES标准化到15%
            jump_score = min(jump_intensity, 1.0)
            crash_score = min(crash_probability / 0.1, 1.0)  # 崩盘概率标准化到10%

            # 加权平均
            weights = [0.3, 0.3, 0.2, 0.2]
            scores = [var_score, es_score, jump_score, crash_score]

            extreme_risk_score = sum(w * s for w, s in zip(weights, scores))
            return extreme_risk_score
        except (ValueError, ZeroDivisionError, TypeError) as e:
            self.logger.warning(f"计算极端风险评分失败: {e}")
            return 0.5
        except Exception as e:
            self.logger.error(f"计算极端风险评分异常: {e}")
            return 0.5

    def _calculate_max_drawdown(self, prices: pd.Series) -> float:
        """计算最大回撤"""
        try:
            cumulative = (1 + prices.pct_change()).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            return abs(drawdown.min())
        except:
            return 0

    def _calculate_market_stress(self, vol_analysis: Dict, liquidity_analysis: Dict,
                               risk_analysis: Dict) -> float:
        """计算市场压力指数"""
        try:
            # 波动率压力
            vol_stress = min(vol_analysis.get('realized_volatility', 0) / 0.5, 1.0)

            # 流动性压力
            liquidity_stress = min(liquidity_analysis.get('average_spread', 0) / 0.01, 1.0)

            # 极端风险压力
            risk_stress = risk_analysis.get('extreme_risk_score', 0)

            # 综合压力指数
            stress_weights = [0.4, 0.3, 0.3]
            stress_components = [vol_stress, liquidity_stress, risk_stress]

            market_stress = sum(w * s for w, s in zip(stress_weights, stress_components))
            return min(market_stress, 1.0)
        except:
            return 0.5

    def _assess_overall_risk(self, volatility_regime: str, liquidity_level: str,
                           market_stress: float) -> str:
        """评估整体风险等级"""
        risk_score = 0

        # 波动率风险
        vol_risk_map = {'LOW': 0, 'NORMAL': 1, 'HIGH': 2, 'EXTREME': 3}
        risk_score += vol_risk_map.get(volatility_regime, 1)

        # 流动性风险
        liq_risk_map = {'DEEP': 0, 'NORMAL': 1, 'SHALLOW': 2, 'DRIED': 3}
        risk_score += liq_risk_map.get(liquidity_level, 1)

        # 市场压力风险
        if market_stress > 0.8:
            risk_score += 3
        elif market_stress > 0.6:
            risk_score += 2
        elif market_stress > 0.4:
            risk_score += 1

        # 风险等级映射
        if risk_score <= 2:
            return 'LOW'
        elif risk_score <= 4:
            return 'MEDIUM'
        elif risk_score <= 6:
            return 'HIGH'
        else:
            return 'CRITICAL'

    def _calculate_state_confidence(self, vol_analysis: Dict, liquidity_analysis: Dict,
                                  risk_analysis: Dict) -> float:
        """计算状态置信度"""
        try:
            # 数据完整性评分
            vol_completeness = 1.0 if vol_analysis else 0.0
            liq_completeness = 1.0 if liquidity_analysis else 0.0
            risk_completeness = 1.0 if risk_analysis else 0.0

            # 指标一致性评分
            consistency_score = 0.8  # 简化假设

            # 历史稳定性评分
            stability_score = 0.9  # 简化假设

            # 综合置信度
            confidence = (vol_completeness + liq_completeness + risk_completeness) / 3 * consistency_score * stability_score
            return confidence
        except:
            return 0.5

    def _generate_alert(self, level: str, layer: str, indicator: str,
                       current_value: float, threshold: float, message: str):
        """生成监控告警"""
        alert = MonitoringAlert(
            timestamp=datetime.now(),
            level=level,
            layer=layer,
            indicator=indicator,
            current_value=current_value,
            threshold=threshold,
            message=message,
            metadata={}
        )

        self.alerts.append(alert)
        logger.warning(f"监控告警 [{level}] {layer}.{indicator}: {message}")

    def get_recent_alerts(self, hours: int = 24) -> List[MonitoringAlert]:
        """获取最近的告警"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alerts if alert.timestamp > cutoff_time]

class StrategyExecutionMonitor:
    """策略执行层监控"""

    def __init__(self, params: Dict[str, Any] = None):
        self.params = params or {
            'slippage_threshold': 0.002,      # 滑点阈值 0.2%
            'capacity_warning_ratio': 0.8,    # 容量预警比例
            'capacity_critical_ratio': 0.95,  # 容量临界比例
            'position_size_limit': 0.1,       # 单一持仓限制 10%
            'correlation_threshold': 0.7,      # 相关性阈值
            'drawdown_warning': 0.05,         # 回撤预警 5%
            'drawdown_critical': 0.1          # 回撤临界 10%
        }

        self.alerts = []
        self.execution_history = []

    def monitor_slippage(self, executed_trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """滑点监控"""
        try:
            slippage_data = []

            for trade in executed_trades:
                expected_price = trade.get('expected_price', 0)
                executed_price = trade.get('executed_price', 0)
                volume = trade.get('volume', 0)

                if expected_price > 0:
                    slippage = abs(executed_price - expected_price) / expected_price
                    slippage_data.append({
                        'symbol': trade.get('symbol', ''),
                        'slippage': slippage,
                        'volume': volume,
                        'timestamp': trade.get('timestamp', datetime.now())
                    })

            if not slippage_data:
                return {}

            # 计算滑点统计
            slippages = [s['slippage'] for s in slippage_data]
            avg_slippage = np.mean(slippages)
            max_slippage = np.max(slippages)
            slippage_volatility = np.std(slippages)

            # 成交量加权滑点
            total_volume = sum(s['volume'] for s in slippage_data)
            if total_volume > 0:
                volume_weighted_slippage = sum(s['slippage'] * s['volume'] for s in slippage_data) / total_volume
            else:
                volume_weighted_slippage = avg_slippage

            # 滑点趋势分析
            recent_slippages = slippages[-10:] if len(slippages) >= 10 else slippages
            slippage_trend = np.polyfit(range(len(recent_slippages)), recent_slippages, 1)[0] if len(recent_slippages) > 1 else 0

            result = {
                'average_slippage': avg_slippage,
                'max_slippage': max_slippage,
                'slippage_volatility': slippage_volatility,
                'volume_weighted_slippage': volume_weighted_slippage,
                'slippage_trend': slippage_trend,
                'total_trades': len(slippage_data),
                'high_slippage_trades': len([s for s in slippages if s > self.params['slippage_threshold']])
            }

            # 生成告警
            if avg_slippage > self.params['slippage_threshold']:
                self._generate_alert('WARNING', 'STRATEGY', 'high_slippage',
                                   avg_slippage, self.params['slippage_threshold'],
                                   f"平均滑点过高: {avg_slippage:.4f}")

            if max_slippage > self.params['slippage_threshold'] * 3:
                self._generate_alert('CRITICAL', 'STRATEGY', 'extreme_slippage',
                                   max_slippage, self.params['slippage_threshold'] * 3,
                                   f"极端滑点: {max_slippage:.4f}")

            return result

        except Exception as e:
            logger.error(f"滑点监控失败: {e}")
            return {}

    def monitor_capacity_radar(self, strategy_positions: Dict[str, Dict[str, Any]],
                             market_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """容量雷达监控"""
        try:
            capacity_analysis = {}

            for strategy_name, positions in strategy_positions.items():
                strategy_capacity = self._analyze_strategy_capacity(strategy_name, positions, market_data)
                capacity_analysis[strategy_name] = strategy_capacity

                # 容量预警
                if strategy_capacity['capacity_utilization'] > self.params['capacity_warning_ratio']:
                    level = 'CRITICAL' if strategy_capacity['capacity_utilization'] > self.params['capacity_critical_ratio'] else 'WARNING'
                    self._generate_alert(level, 'STRATEGY', 'capacity_limit',
                                       strategy_capacity['capacity_utilization'],
                                       self.params['capacity_warning_ratio'],
                                       f"{strategy_name} 容量利用率过高: {strategy_capacity['capacity_utilization']:.2%}")

            # 整体容量分析
            total_capacity = self._calculate_total_capacity(capacity_analysis)

            result = {
                'strategy_capacities': capacity_analysis,
                'total_capacity_utilization': total_capacity['utilization'],
                'capacity_bottlenecks': total_capacity['bottlenecks'],
                'capacity_recommendations': total_capacity['recommendations']
            }

            return result

        except Exception as e:
            logger.error(f"容量雷达监控失败: {e}")
            return {}

    def detect_position_sizing_issues(self, portfolio_positions: Dict[str, float],
                                    total_capital: float) -> Dict[str, Any]:
        """仓位检测"""
        try:
            position_analysis = {}

            # 单一持仓分析
            for symbol, position_value in portfolio_positions.items():
                position_ratio = abs(position_value) / total_capital
                position_analysis[symbol] = {
                    'position_value': position_value,
                    'position_ratio': position_ratio,
                    'risk_level': self._assess_position_risk(position_ratio)
                }

                # 单一持仓过大告警
                if position_ratio > self.params['position_size_limit']:
                    self._generate_alert('WARNING', 'STRATEGY', 'oversized_position',
                                       position_ratio, self.params['position_size_limit'],
                                       f"{symbol} 持仓过大: {position_ratio:.2%}")

            # 持仓集中度分析
            concentration_metrics = self._analyze_position_concentration(portfolio_positions, total_capital)

            # 相关性风险分析
            correlation_risk = self._analyze_correlation_risk(portfolio_positions)

            result = {
                'position_analysis': position_analysis,
                'concentration_metrics': concentration_metrics,
                'correlation_risk': correlation_risk,
                'total_positions': len(portfolio_positions),
                'net_exposure': sum(portfolio_positions.values()) / total_capital,
                'gross_exposure': sum(abs(v) for v in portfolio_positions.values()) / total_capital
            }

            return result

        except Exception as e:
            logger.error(f"仓位检测失败: {e}")
            return {}

    def _analyze_strategy_capacity(self, strategy_name: str, positions: Dict[str, Any],
                                 market_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """分析策略容量"""
        try:
            total_position_value = sum(abs(pos.get('value', 0)) for pos in positions.values())

            # 估算市场容量
            market_capacity = 0
            for symbol, position in positions.items():
                if symbol in market_data:
                    # 基于成交量估算容量
                    avg_volume = market_data[symbol]['volume'].rolling(20).mean().iloc[-1]
                    avg_price = market_data[symbol]['close'].rolling(20).mean().iloc[-1]
                    daily_capacity = avg_volume * avg_price * 0.1  # 假设可以占用10%的日成交量
                    market_capacity += daily_capacity

            capacity_utilization = total_position_value / market_capacity if market_capacity > 0 else 1.0

            # 流动性约束分析
            liquidity_constraints = self._analyze_liquidity_constraints(positions, market_data)

            return {
                'strategy_name': strategy_name,
                'total_position_value': total_position_value,
                'estimated_market_capacity': market_capacity,
                'capacity_utilization': capacity_utilization,
                'liquidity_constraints': liquidity_constraints,
                'capacity_status': 'CRITICAL' if capacity_utilization > 0.9 else 'WARNING' if capacity_utilization > 0.7 else 'NORMAL'
            }

        except Exception as e:
            logger.error(f"策略容量分析失败: {e}")
            return {}

    def _calculate_total_capacity(self, capacity_analysis: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """计算总体容量"""
        try:
            total_utilization = np.mean([cap['capacity_utilization'] for cap in capacity_analysis.values()])

            # 识别容量瓶颈
            bottlenecks = [name for name, cap in capacity_analysis.items()
                          if cap['capacity_utilization'] > self.params['capacity_warning_ratio']]

            # 生成建议
            recommendations = []
            if total_utilization > 0.8:
                recommendations.append("考虑减少整体仓位规模")
            if len(bottlenecks) > 0:
                recommendations.append(f"关注策略容量瓶颈: {', '.join(bottlenecks)}")

            return {
                'utilization': total_utilization,
                'bottlenecks': bottlenecks,
                'recommendations': recommendations
            }

        except Exception as e:
            logger.error(f"总体容量计算失败: {e}")
            return {'utilization': 0, 'bottlenecks': [], 'recommendations': []}

    def _assess_position_risk(self, position_ratio: float) -> str:
        """评估持仓风险"""
        if position_ratio > self.params['position_size_limit'] * 1.5:
            return 'HIGH'
        elif position_ratio > self.params['position_size_limit']:
            return 'MEDIUM'
        else:
            return 'LOW'

    def _analyze_position_concentration(self, positions: Dict[str, float],
                                      total_capital: float) -> Dict[str, Any]:
        """分析持仓集中度"""
        try:
            position_ratios = [abs(v) / total_capital for v in positions.values()]

            # 赫芬达尔指数
            herfindahl_index = sum(ratio ** 2 for ratio in position_ratios)

            # 前N大持仓集中度
            sorted_ratios = sorted(position_ratios, reverse=True)
            top_3_concentration = sum(sorted_ratios[:3]) if len(sorted_ratios) >= 3 else sum(sorted_ratios)
            top_5_concentration = sum(sorted_ratios[:5]) if len(sorted_ratios) >= 5 else sum(sorted_ratios)

            return {
                'herfindahl_index': herfindahl_index,
                'top_3_concentration': top_3_concentration,
                'top_5_concentration': top_5_concentration,
                'concentration_level': 'HIGH' if herfindahl_index > 0.2 else 'MEDIUM' if herfindahl_index > 0.1 else 'LOW'
            }

        except Exception as e:
            logger.error(f"持仓集中度分析失败: {e}")
            return {}

    def _analyze_correlation_risk(self, positions: Dict[str, float]) -> Dict[str, Any]:
        """分析相关性风险"""
        try:
            # 简化版本：基于行业/板块相关性
            # 实际应该使用历史价格数据计算相关性矩阵

            symbols = list(positions.keys())
            correlation_risk_score = 0

            # 模拟相关性分析
            for i, symbol1 in enumerate(symbols):
                for j, symbol2 in enumerate(symbols[i+1:], i+1):
                    # 简化假设：同行业股票相关性高
                    simulated_correlation = np.random.uniform(0.3, 0.8)
                    if simulated_correlation > self.params['correlation_threshold']:
                        weight1 = abs(positions[symbol1])
                        weight2 = abs(positions[symbol2])
                        correlation_risk_score += simulated_correlation * weight1 * weight2

            return {
                'correlation_risk_score': correlation_risk_score,
                'high_correlation_pairs': [],  # 实际应该包含高相关性股票对
                'diversification_ratio': len(symbols) / max(correlation_risk_score, 1),
                'correlation_risk_level': 'HIGH' if correlation_risk_score > 0.5 else 'MEDIUM' if correlation_risk_score > 0.2 else 'LOW'
            }

        except Exception as e:
            logger.error(f"相关性风险分析失败: {e}")
            return {}

    def _analyze_liquidity_constraints(self, positions: Dict[str, Any],
                                     market_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """分析流动性约束"""
        try:
            liquidity_metrics = {}

            for symbol, position in positions.items():
                if symbol in market_data:
                    volume_data = market_data[symbol]['volume']
                    position_size = abs(position.get('value', 0))

                    # 日均成交量
                    avg_daily_volume = volume_data.rolling(20).mean().iloc[-1]

                    # 流动性比率
                    liquidity_ratio = position_size / (avg_daily_volume * market_data[symbol]['close'].iloc[-1])

                    liquidity_metrics[symbol] = {
                        'liquidity_ratio': liquidity_ratio,
                        'avg_daily_volume': avg_daily_volume,
                        'liquidity_risk': 'HIGH' if liquidity_ratio > 0.2 else 'MEDIUM' if liquidity_ratio > 0.1 else 'LOW'
                    }

            return liquidity_metrics

        except Exception as e:
            logger.error(f"流动性约束分析失败: {e}")
            return {}

    def _generate_alert(self, level: str, layer: str, indicator: str,
                       current_value: float, threshold: float, message: str):
        """生成监控告警"""
        alert = MonitoringAlert(
            timestamp=datetime.now(),
            level=level,
            layer=layer,
            indicator=indicator,
            current_value=current_value,
            threshold=threshold,
            message=message,
            metadata={}
        )

        self.alerts.append(alert)
        logger.warning(f"策略执行告警 [{level}] {layer}.{indicator}: {message}")

    def get_recent_alerts(self, hours: int = 24) -> List[MonitoringAlert]:
        """获取最近的告警"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alerts if alert.timestamp > cutoff_time]

class CapitalProtectionMonitor:
    """资金防护层监控"""

    def __init__(self, params: Dict[str, Any] = None):
        self.params = params or {
            'var_confidence_level': 0.95,     # VaR置信水平
            'var_holding_period': 1,          # VaR持有期（天）
            'stress_scenarios': 5,            # 压力测试场景数
            'circuit_breaker_loss': 0.02,    # 熔断损失阈值 2%
            'circuit_breaker_drawdown': 0.05, # 熔断回撤阈值 5%
            'leverage_limit': 3.0,            # 杠杆限制
            'concentration_limit': 0.2        # 集中度限制 20%
        }

        self.alerts = []
        self.var_history = []
        self.stress_test_results = []

    def calculate_dynamic_var(self, portfolio_returns: pd.Series,
                            portfolio_weights: Dict[str, float] = None) -> Dict[str, Any]:
        """动态VaR计算"""
        try:
            if len(portfolio_returns) < 30:
                logger.warning("历史数据不足，VaR计算可能不准确")

            # 历史模拟法VaR
            historical_var = self._calculate_historical_var(portfolio_returns)

            # 参数化VaR（正态分布假设）
            parametric_var = self._calculate_parametric_var(portfolio_returns)

            # GARCH-VaR
            garch_var = self._calculate_garch_var(portfolio_returns)

            # 蒙特卡洛VaR
            monte_carlo_var = self._calculate_monte_carlo_var(portfolio_returns)

            # 条件VaR (Expected Shortfall)
            conditional_var = self._calculate_conditional_var(portfolio_returns)

            # 动态权重调整
            if portfolio_weights:
                weighted_var = self._calculate_weighted_var(portfolio_returns, portfolio_weights)
            else:
                weighted_var = historical_var

            # VaR模型置信度评估
            var_confidence = self._assess_var_confidence(portfolio_returns, historical_var)

            result = {
                'historical_var': historical_var,
                'parametric_var': parametric_var,
                'garch_var': garch_var,
                'monte_carlo_var': monte_carlo_var,
                'conditional_var': conditional_var,
                'weighted_var': weighted_var,
                'var_confidence': var_confidence,
                'var_date': datetime.now(),
                'data_points': len(portfolio_returns)
            }

            # 记录VaR历史
            self.var_history.append(result)

            # VaR告警
            max_var = max(historical_var, parametric_var, garch_var)
            if abs(max_var) > 0.05:  # 5%的VaR阈值
                self._generate_alert('WARNING', 'CAPITAL', 'high_var',
                                   abs(max_var), 0.05,
                                   f"VaR风险过高: {max_var:.4f}")

            return result

        except Exception as e:
            logger.error(f"动态VaR计算失败: {e}")
            return {}

    def conduct_stress_testing(self, portfolio_data: Dict[str, pd.DataFrame],
                             portfolio_weights: Dict[str, float]) -> Dict[str, Any]:
        """压力测试"""
        try:
            stress_scenarios = self._generate_stress_scenarios()
            stress_results = []

            for scenario_name, scenario in stress_scenarios.items():
                scenario_result = self._apply_stress_scenario(portfolio_data, portfolio_weights, scenario)
                scenario_result['scenario_name'] = scenario_name
                stress_results.append(scenario_result)

            # 综合压力测试结果
            worst_case_loss = min([result['portfolio_loss'] for result in stress_results])
            best_case_loss = max([result['portfolio_loss'] for result in stress_results])
            average_loss = np.mean([result['portfolio_loss'] for result in stress_results])

            # 压力测试通过率
            pass_rate = len([r for r in stress_results if r['portfolio_loss'] > -0.1]) / len(stress_results)

            # 尾部风险评估
            tail_risk = self._assess_tail_risk(stress_results)

            result = {
                'stress_scenarios': stress_results,
                'worst_case_loss': worst_case_loss,
                'best_case_loss': best_case_loss,
                'average_loss': average_loss,
                'pass_rate': pass_rate,
                'tail_risk': tail_risk,
                'stress_test_date': datetime.now()
            }

            # 记录压力测试结果
            self.stress_test_results.append(result)

            # 压力测试告警
            if worst_case_loss < -0.2:  # 最坏情况损失超过20%
                self._generate_alert('CRITICAL', 'CAPITAL', 'stress_test_failure',
                                   abs(worst_case_loss), 0.2,
                                   f"压力测试显示极端风险: {worst_case_loss:.2%}")

            return result

        except Exception as e:
            logger.error(f"压力测试失败: {e}")
            return {}

    def monitor_circuit_breaker(self, current_portfolio_value: float,
                              initial_portfolio_value: float,
                              daily_pnl: float) -> Dict[str, Any]:
        """熔断机制监控"""
        try:
            # 当日损失率
            daily_loss_rate = daily_pnl / initial_portfolio_value if initial_portfolio_value > 0 else 0

            # 累计回撤
            total_return = (current_portfolio_value - initial_portfolio_value) / initial_portfolio_value
            max_portfolio_value = max(current_portfolio_value, initial_portfolio_value)  # 简化版本
            current_drawdown = (max_portfolio_value - current_portfolio_value) / max_portfolio_value

            # 熔断触发条件
            circuit_breaker_triggered = False
            trigger_reasons = []

            # 单日损失熔断
            if daily_loss_rate < -self.params['circuit_breaker_loss']:
                circuit_breaker_triggered = True
                trigger_reasons.append(f"单日损失超限: {daily_loss_rate:.2%}")

            # 回撤熔断
            if current_drawdown > self.params['circuit_breaker_drawdown']:
                circuit_breaker_triggered = True
                trigger_reasons.append(f"回撤超限: {current_drawdown:.2%}")

            # 风险预警级别
            if daily_loss_rate < -self.params['circuit_breaker_loss'] * 0.5:
                risk_level = 'WARNING'
            elif daily_loss_rate < -self.params['circuit_breaker_loss'] * 0.8:
                risk_level = 'HIGH'
            elif circuit_breaker_triggered:
                risk_level = 'CRITICAL'
            else:
                risk_level = 'NORMAL'

            result = {
                'daily_loss_rate': daily_loss_rate,
                'current_drawdown': current_drawdown,
                'total_return': total_return,
                'circuit_breaker_triggered': circuit_breaker_triggered,
                'trigger_reasons': trigger_reasons,
                'risk_level': risk_level,
                'timestamp': datetime.now()
            }

            # 熔断告警
            if circuit_breaker_triggered:
                self._generate_alert('CRITICAL', 'CAPITAL', 'circuit_breaker',
                                   max(abs(daily_loss_rate), current_drawdown),
                                   max(self.params['circuit_breaker_loss'], self.params['circuit_breaker_drawdown']),
                                   f"熔断触发: {'; '.join(trigger_reasons)}")

            return result

        except Exception as e:
            logger.error(f"熔断机制监控失败: {e}")
            return {}

    def _calculate_historical_var(self, returns: pd.Series) -> float:
        """历史模拟法VaR"""
        try:
            confidence_level = self.params['var_confidence_level']
            percentile = (1 - confidence_level) * 100
            var = np.percentile(returns.dropna(), percentile)
            return var
        except:
            return 0.0

    def _calculate_parametric_var(self, returns: pd.Series) -> float:
        """参数化VaR（正态分布）"""
        try:
            from scipy.stats import norm
            confidence_level = self.params['var_confidence_level']
            mean_return = returns.mean()
            std_return = returns.std()
            var = norm.ppf(1 - confidence_level, mean_return, std_return)
            return var
        except:
            # 简化版本
            confidence_level = self.params['var_confidence_level']
            mean_return = returns.mean()
            std_return = returns.std()
            # 使用正态分布的近似值
            z_score = 1.645 if confidence_level == 0.95 else 2.326  # 95%或99%
            var = mean_return - z_score * std_return
            return var

    def _calculate_garch_var(self, returns: pd.Series) -> float:
        """GARCH-VaR（简化版）"""
        try:
            # 简化的GARCH(1,1)波动率预测
            alpha = 0.1
            beta = 0.85
            omega = 0.00001

            squared_returns = returns ** 2
            garch_variance = omega

            for i in range(1, min(len(squared_returns), 100)):  # 限制计算量
                garch_variance = omega + alpha * squared_returns.iloc[i-1] + beta * garch_variance

            garch_volatility = np.sqrt(garch_variance)

            # 使用GARCH波动率计算VaR
            confidence_level = self.params['var_confidence_level']
            z_score = 1.645 if confidence_level == 0.95 else 2.326
            var = returns.mean() - z_score * garch_volatility

            return var
        except:
            return self._calculate_parametric_var(returns)

    def _calculate_monte_carlo_var(self, returns: pd.Series, simulations: int = 1000) -> float:
        """蒙特卡洛VaR"""
        try:
            mean_return = returns.mean()
            std_return = returns.std()

            # 蒙特卡洛模拟
            simulated_returns = np.random.normal(mean_return, std_return, simulations)

            confidence_level = self.params['var_confidence_level']
            percentile = (1 - confidence_level) * 100
            var = np.percentile(simulated_returns, percentile)

            return var
        except:
            return self._calculate_parametric_var(returns)

    def _calculate_conditional_var(self, returns: pd.Series) -> float:
        """条件VaR (Expected Shortfall)"""
        try:
            var = self._calculate_historical_var(returns)
            # 计算超过VaR的损失的期望值
            tail_losses = returns[returns <= var]
            conditional_var = tail_losses.mean() if len(tail_losses) > 0 else var
            return conditional_var
        except:
            return 0.0

    def _calculate_weighted_var(self, returns: pd.Series, weights: Dict[str, float]) -> float:
        """加权VaR"""
        try:
            # 简化版本：使用权重调整VaR
            base_var = self._calculate_historical_var(returns)

            # 根据权重集中度调整VaR
            max_weight = max(weights.values()) if weights else 0
            concentration_adjustment = 1 + max_weight  # 集中度越高，VaR越大

            weighted_var = base_var * concentration_adjustment
            return weighted_var
        except:
            return self._calculate_historical_var(returns)

    def _assess_var_confidence(self, returns: pd.Series, var_estimate: float) -> float:
        """评估VaR模型置信度"""
        try:
            # 回测检验：计算VaR违约率
            violations = (returns <= var_estimate).sum()
            total_observations = len(returns)
            violation_rate = violations / total_observations

            # 期望违约率
            expected_violation_rate = 1 - self.params['var_confidence_level']

            # 置信度评分（违约率越接近期望值，置信度越高）
            confidence = 1 - abs(violation_rate - expected_violation_rate) / expected_violation_rate
            return max(0, min(1, confidence))
        except:
            return 0.5

    def _generate_stress_scenarios(self) -> Dict[str, Dict[str, Any]]:
        """生成压力测试场景"""
        scenarios = {
            'market_crash': {
                'description': '市场崩盘场景',
                'equity_shock': -0.3,      # 股票下跌30%
                'bond_shock': -0.1,        # 债券下跌10%
                'volatility_multiplier': 3.0,
                'correlation_increase': 0.3
            },
            'interest_rate_shock': {
                'description': '利率冲击场景',
                'rate_increase': 0.02,     # 利率上升200bp
                'equity_shock': -0.15,
                'bond_shock': -0.2,
                'volatility_multiplier': 2.0,
                'correlation_increase': 0.2
            },
            'liquidity_crisis': {
                'description': '流动性危机场景',
                'liquidity_reduction': 0.5, # 流动性减少50%
                'spread_widening': 3.0,     # 价差扩大3倍
                'equity_shock': -0.2,
                'volatility_multiplier': 2.5,
                'correlation_increase': 0.4
            },
            'sector_rotation': {
                'description': '板块轮动场景',
                'sector_shocks': {
                    'technology': -0.25,
                    'finance': 0.1,
                    'healthcare': -0.1,
                    'energy': 0.15
                },
                'volatility_multiplier': 1.5,
                'correlation_change': -0.1
            },
            'black_swan': {
                'description': '黑天鹅事件',
                'extreme_shock': -0.5,     # 极端冲击50%
                'volatility_multiplier': 5.0,
                'correlation_increase': 0.8,
                'liquidity_reduction': 0.8
            }
        }
        return scenarios

    def _apply_stress_scenario(self, portfolio_data: Dict[str, pd.DataFrame],
                             portfolio_weights: Dict[str, float],
                             scenario: Dict[str, Any]) -> Dict[str, Any]:
        """应用压力测试场景"""
        try:
            total_portfolio_loss = 0
            asset_losses = {}

            for symbol, weight in portfolio_weights.items():
                if symbol in portfolio_data:
                    current_price = portfolio_data[symbol]['close'].iloc[-1]

                    # 根据场景计算冲击
                    if 'equity_shock' in scenario:
                        shock = scenario['equity_shock']
                    elif 'sector_shocks' in scenario:
                        # 简化：随机分配板块
                        sectors = list(scenario['sector_shocks'].keys())
                        sector = np.random.choice(sectors)
                        shock = scenario['sector_shocks'][sector]
                    elif 'extreme_shock' in scenario:
                        shock = scenario['extreme_shock']
                    else:
                        shock = -0.1  # 默认10%下跌

                    # 计算资产损失
                    asset_loss = weight * shock
                    asset_losses[symbol] = asset_loss
                    total_portfolio_loss += asset_loss

            # 计算风险指标
            var_under_stress = total_portfolio_loss * 1.5  # 压力下的VaR
            max_drawdown_stress = abs(total_portfolio_loss) * 1.2

            return {
                'portfolio_loss': total_portfolio_loss,
                'asset_losses': asset_losses,
                'var_under_stress': var_under_stress,
                'max_drawdown_stress': max_drawdown_stress,
                'scenario_severity': abs(total_portfolio_loss)
            }

        except Exception as e:
            logger.error(f"压力场景应用失败: {e}")
            return {'portfolio_loss': -0.1, 'asset_losses': {}, 'var_under_stress': -0.15, 'max_drawdown_stress': 0.12, 'scenario_severity': 0.1}

    def _assess_tail_risk(self, stress_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """评估尾部风险"""
        try:
            losses = [result['portfolio_loss'] for result in stress_results]

            # 尾部风险指标
            worst_1_percent = np.percentile(losses, 1)
            worst_5_percent = np.percentile(losses, 5)
            tail_expectation = np.mean([loss for loss in losses if loss <= worst_5_percent])

            # 尾部依赖性
            extreme_losses = [loss for loss in losses if loss <= worst_5_percent]
            tail_correlation = len(extreme_losses) / len(losses)

            return {
                'worst_1_percent': worst_1_percent,
                'worst_5_percent': worst_5_percent,
                'tail_expectation': tail_expectation,
                'tail_correlation': tail_correlation,
                'tail_risk_level': 'HIGH' if abs(worst_1_percent) > 0.3 else 'MEDIUM' if abs(worst_1_percent) > 0.2 else 'LOW'
            }

        except Exception as e:
            logger.error(f"尾部风险评估失败: {e}")
            return {'worst_1_percent': -0.2, 'worst_5_percent': -0.15, 'tail_expectation': -0.18, 'tail_correlation': 0.2, 'tail_risk_level': 'MEDIUM'}

    def _generate_alert(self, level: str, layer: str, indicator: str,
                       current_value: float, threshold: float, message: str):
        """生成监控告警"""
        alert = MonitoringAlert(
            timestamp=datetime.now(),
            level=level,
            layer=layer,
            indicator=indicator,
            current_value=current_value,
            threshold=threshold,
            message=message,
            metadata={}
        )

        self.alerts.append(alert)
        logger.warning(f"资金防护告警 [{level}] {layer}.{indicator}: {message}")

    def get_recent_alerts(self, hours: int = 24) -> List[MonitoringAlert]:
        """获取最近的告警"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alerts if alert.timestamp > cutoff_time]

class CoreMonitoringSystem:
    """核心监控指标体系统一管理"""

    def __init__(self, params: Dict[str, Any] = None):
        self.market_monitor = MarketStateMonitor(params)
        self.strategy_monitor = StrategyExecutionMonitor(params)
        self.capital_monitor = CapitalProtectionMonitor(params)

        self.system_alerts = []
        self.monitoring_history = []

    def comprehensive_monitoring(self,
                               price_data: Dict[str, pd.DataFrame],
                               portfolio_data: Dict[str, Any],
                               execution_data: Dict[str, Any]) -> Dict[str, Any]:
        """综合监控分析"""
        try:
            monitoring_result = {
                'timestamp': datetime.now(),
                'market_state': {},
                'strategy_execution': {},
                'capital_protection': {},
                'system_alerts': [],
                'risk_summary': {}
            }

            # 市场状态层监控
            if price_data:
                main_symbol = list(price_data.keys())[0]
                market_state = self.market_monitor.get_market_state(
                    price_data[main_symbol],
                    price_data.get(main_symbol),
                    price_data.get(main_symbol)
                )
                monitoring_result['market_state'] = {
                    'volatility_regime': market_state.volatility_regime,
                    'liquidity_level': market_state.liquidity_level,
                    'risk_level': market_state.risk_level,
                    'market_stress': market_state.market_stress,
                    'confidence': market_state.confidence
                }

            # 策略执行层监控
            if execution_data:
                # 滑点监控
                if 'executed_trades' in execution_data:
                    slippage_analysis = self.strategy_monitor.monitor_slippage(
                        execution_data['executed_trades']
                    )
                    monitoring_result['strategy_execution']['slippage'] = slippage_analysis

                # 容量监控
                if 'strategy_positions' in execution_data:
                    capacity_analysis = self.strategy_monitor.monitor_capacity_radar(
                        execution_data['strategy_positions'], price_data
                    )
                    monitoring_result['strategy_execution']['capacity'] = capacity_analysis

                # 仓位监控
                if 'portfolio_positions' in execution_data and 'total_capital' in execution_data:
                    position_analysis = self.strategy_monitor.detect_position_sizing_issues(
                        execution_data['portfolio_positions'],
                        execution_data['total_capital']
                    )
                    monitoring_result['strategy_execution']['position'] = position_analysis

            # 资金防护层监控
            if portfolio_data:
                # VaR计算
                if 'portfolio_returns' in portfolio_data:
                    var_analysis = self.capital_monitor.calculate_dynamic_var(
                        portfolio_data['portfolio_returns'],
                        portfolio_data.get('portfolio_weights')
                    )
                    monitoring_result['capital_protection']['var'] = var_analysis

                # 压力测试
                if 'portfolio_weights' in portfolio_data and price_data:
                    stress_analysis = self.capital_monitor.conduct_stress_testing(
                        price_data, portfolio_data['portfolio_weights']
                    )
                    monitoring_result['capital_protection']['stress_test'] = stress_analysis

                # 熔断监控
                if all(key in portfolio_data for key in ['current_value', 'initial_value', 'daily_pnl']):
                    circuit_analysis = self.capital_monitor.monitor_circuit_breaker(
                        portfolio_data['current_value'],
                        portfolio_data['initial_value'],
                        portfolio_data['daily_pnl']
                    )
                    monitoring_result['capital_protection']['circuit_breaker'] = circuit_analysis

            # 收集所有告警
            all_alerts = []
            all_alerts.extend(self.market_monitor.get_recent_alerts(1))
            all_alerts.extend(self.strategy_monitor.get_recent_alerts(1))
            all_alerts.extend(self.capital_monitor.get_recent_alerts(1))

            # 按严重程度排序
            all_alerts.sort(key=lambda x: {'CRITICAL': 3, 'WARNING': 2, 'INFO': 1}.get(x.level, 0), reverse=True)
            monitoring_result['system_alerts'] = all_alerts[:10]  # 最多显示10个告警

            # 风险总结
            risk_summary = self._generate_risk_summary(monitoring_result)
            monitoring_result['risk_summary'] = risk_summary

            # 记录监控历史
            self.monitoring_history.append(monitoring_result)

            return monitoring_result

        except Exception as e:
            logger.error(f"综合监控分析失败: {e}")
            return {'timestamp': datetime.now(), 'error': str(e)}

    def _generate_risk_summary(self, monitoring_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成风险总结"""
        try:
            risk_levels = []

            # 市场风险
            market_risk = monitoring_result.get('market_state', {}).get('risk_level', 'UNKNOWN')
            risk_levels.append(market_risk)

            # 策略风险
            strategy_data = monitoring_result.get('strategy_execution', {})
            if strategy_data:
                # 基于滑点、容量、仓位等判断策略风险
                strategy_risk = 'MEDIUM'  # 简化
                risk_levels.append(strategy_risk)

            # 资金风险
            capital_data = monitoring_result.get('capital_protection', {})
            if capital_data:
                circuit_data = capital_data.get('circuit_breaker', {})
                if circuit_data.get('circuit_breaker_triggered', False):
                    capital_risk = 'CRITICAL'
                else:
                    capital_risk = circuit_data.get('risk_level', 'NORMAL')
                risk_levels.append(capital_risk)

            # 综合风险评级
            risk_scores = {'LOW': 1, 'NORMAL': 1, 'MEDIUM': 2, 'WARNING': 2, 'HIGH': 3, 'CRITICAL': 4, 'UNKNOWN': 2}
            max_risk_score = max([risk_scores.get(risk, 2) for risk in risk_levels])

            overall_risk = 'LOW'
            for risk, score in risk_scores.items():
                if score == max_risk_score:
                    overall_risk = risk
                    break

            # 告警统计
            alerts = monitoring_result.get('system_alerts', [])
            alert_counts = {'CRITICAL': 0, 'WARNING': 0, 'INFO': 0}
            for alert in alerts:
                alert_counts[alert.level] = alert_counts.get(alert.level, 0) + 1

            return {
                'overall_risk_level': overall_risk,
                'market_risk': market_risk,
                'strategy_risk': strategy_data.get('risk_level', 'NORMAL') if strategy_data else 'NORMAL',
                'capital_risk': capital_data.get('circuit_breaker', {}).get('risk_level', 'NORMAL') if capital_data else 'NORMAL',
                'alert_counts': alert_counts,
                'total_alerts': len(alerts),
                'risk_trend': 'STABLE',  # 简化，实际应该基于历史数据
                'recommendations': self._generate_recommendations(overall_risk, monitoring_result)
            }

        except Exception as e:
            logger.error(f"风险总结生成失败: {e}")
            return {'overall_risk_level': 'UNKNOWN', 'error': str(e)}

    def _generate_recommendations(self, overall_risk: str, monitoring_result: Dict[str, Any]) -> List[str]:
        """生成风险管理建议"""
        recommendations = []

        try:
            if overall_risk in ['HIGH', 'CRITICAL']:
                recommendations.append("立即检查所有持仓，考虑减仓操作")
                recommendations.append("暂停新增投资，等待市场稳定")

            # 基于具体监控结果的建议
            market_data = monitoring_result.get('market_state', {})
            if market_data.get('volatility_regime') == 'EXTREME':
                recommendations.append("市场波动极端，建议降低杠杆")

            if market_data.get('liquidity_level') in ['SHALLOW', 'DRIED']:
                recommendations.append("流动性不足，避免大额交易")

            strategy_data = monitoring_result.get('strategy_execution', {})
            if strategy_data.get('slippage', {}).get('average_slippage', 0) > 0.005:
                recommendations.append("滑点过高，优化交易执行算法")

            capital_data = monitoring_result.get('capital_protection', {})
            if capital_data.get('circuit_breaker', {}).get('circuit_breaker_triggered', False):
                recommendations.append("触发熔断机制，立即停止交易")

            if not recommendations:
                recommendations.append("当前风险可控，继续监控市场变化")

        except Exception as e:
            logger.error(f"建议生成失败: {e}")
            recommendations.append("系统异常，请人工检查")

        return recommendations

    def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """获取监控仪表板数据"""
        try:
            if not self.monitoring_history:
                return {'message': '暂无监控数据'}

            latest_result = self.monitoring_history[-1]

            dashboard = {
                'last_update': latest_result['timestamp'],
                'market_status': latest_result.get('market_state', {}),
                'strategy_status': latest_result.get('strategy_execution', {}),
                'capital_status': latest_result.get('capital_protection', {}),
                'risk_summary': latest_result.get('risk_summary', {}),
                'recent_alerts': latest_result.get('system_alerts', [])[:5],
                'monitoring_stats': {
                    'total_monitoring_sessions': len(self.monitoring_history),
                    'avg_risk_level': self._calculate_avg_risk_level(),
                    'alert_frequency': self._calculate_alert_frequency()
                }
            }

            return dashboard

        except Exception as e:
            logger.error(f"监控仪表板生成失败: {e}")
            return {'error': str(e)}

    def _calculate_avg_risk_level(self) -> str:
        """计算平均风险水平"""
        try:
            risk_scores = {'LOW': 1, 'NORMAL': 1, 'MEDIUM': 2, 'WARNING': 2, 'HIGH': 3, 'CRITICAL': 4}

            scores = []
            for result in self.monitoring_history[-10:]:  # 最近10次
                risk_level = result.get('risk_summary', {}).get('overall_risk_level', 'NORMAL')
                scores.append(risk_scores.get(risk_level, 1))

            if scores:
                avg_score = sum(scores) / len(scores)
                for risk, score in risk_scores.items():
                    if abs(score - avg_score) < 0.5:
                        return risk

            return 'NORMAL'

        except:
            return 'NORMAL'

    def _calculate_alert_frequency(self) -> float:
        """计算告警频率"""
        try:
            total_alerts = 0
            for result in self.monitoring_history[-24:]:  # 最近24次（假设每小时一次）
                total_alerts += len(result.get('system_alerts', []))

            return total_alerts / min(len(self.monitoring_history), 24)

        except:
            return 0.0
