#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Finnhub数据源实现
"""

import aiohttp
import asyncio
from typing import Optional
from qbot.data.data_source_interface import IDataSource, StockData
from qbot.common.logging.logger import LOGGER as logger


class FinnhubDataSource(IDataSource):
    """Finnhub数据源"""
    
    def __init__(self, name: str, config: dict):
        super().__init__(name, config)
        self.api_key = config.get('api_key')
        self.base_url = config.get('base_url', 'https://finnhub.io/api/v1')
        self.sandbox = config.get('sandbox', False)
        
        if self.sandbox:
            self.base_url = 'https://finnhub.io/api/v1'  # 沙盒环境
    
    def supports_market(self, market: str) -> bool:
        """检查是否支持指定市场"""
        return market in ['US', 'HK', 'UK', 'JP']
    
    def is_available(self) -> bool:
        """检查数据源是否可用"""
        return self.enabled and self.api_key is not None
    
    async def get_stock_data(self, symbol: str, market: str) -> Optional[StockData]:
        """获取股票数据"""
        if not self.supports_market(market) or not self.api_key:
            return None
        
        try:
            return await self._make_request_with_retry(self._fetch_finnhub_data, symbol, market)
        except Exception as e:
            logger.error(f"Finnhub获取{symbol}数据失败: {e}")
            return None
    
    async def _fetch_finnhub_data(self, symbol: str, market: str) -> Optional[StockData]:
        """从Finnhub获取数据"""
        try:
            # 转换股票代码格式
            finnhub_symbol = self._convert_symbol(symbol, market)
            if not finnhub_symbol:
                return None
            
            # 并发获取多个数据
            tasks = [
                self._get_quote(finnhub_symbol),
                self._get_profile(finnhub_symbol),
                self._get_metrics(finnhub_symbol)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            quote_data, profile_data, metrics_data = results
            
            # 合并数据
            if isinstance(quote_data, dict) and quote_data.get('c', 0) > 0:
                stock_data = StockData(
                    symbol=symbol,
                    market=market,
                    current_price=float(quote_data.get('c', 0)),
                    open_price=float(quote_data.get('o', 0)),
                    high_price=float(quote_data.get('h', 0)),
                    low_price=float(quote_data.get('l', 0)),
                    close_prev=float(quote_data.get('pc', 0)),
                    timestamp=self.last_request_time,
                    source='finnhub'
                )
                
                # 添加公司信息
                if isinstance(profile_data, dict):
                    stock_data.name = profile_data.get('name', '')
                    stock_data.market_cap = float(profile_data.get('marketCapitalization', 0)) * 1000000  # 转换为实际值
                
                # 添加财务指标
                if isinstance(metrics_data, dict) and 'metric' in metrics_data:
                    metrics = metrics_data['metric']
                    stock_data.pe_ratio = float(metrics.get('peBasicExclExtraTTM', 0))
                    stock_data.pb_ratio = float(metrics.get('pbQuarterly', 0))
                    stock_data.roe = float(metrics.get('roeTTM', 0))
                
                return stock_data
            
            return None
            
        except Exception as e:
            logger.error(f"Finnhub数据解析失败: {e}")
            return None
    
    def _convert_symbol(self, symbol: str, market: str) -> Optional[str]:
        """转换股票代码为Finnhub格式"""
        try:
            if market == 'US':
                return symbol.upper()
            elif market == 'HK':
                # 港股代码转换
                if symbol.isdigit():
                    return f"{int(symbol):04d}.HK"
                return symbol
            elif market == 'UK':
                return f"{symbol}.L"
            elif market == 'JP':
                return f"{symbol}.T"
            else:
                return symbol
        except:
            return None
    
    async def _get_quote(self, symbol: str) -> Optional[dict]:
        """获取实时报价"""
        try:
            url = f"{self.base_url}/quote"
            params = {
                'symbol': symbol,
                'token': self.api_key
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        return await response.json()
            return None
        except Exception as e:
            logger.debug(f"Finnhub获取报价失败: {e}")
            return None
    
    async def _get_profile(self, symbol: str) -> Optional[dict]:
        """获取公司信息"""
        try:
            url = f"{self.base_url}/stock/profile2"
            params = {
                'symbol': symbol,
                'token': self.api_key
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        return await response.json()
            return None
        except Exception as e:
            logger.debug(f"Finnhub获取公司信息失败: {e}")
            return None
    
    async def _get_metrics(self, symbol: str) -> Optional[dict]:
        """获取财务指标"""
        try:
            url = f"{self.base_url}/stock/metric"
            params = {
                'symbol': symbol,
                'metric': 'all',
                'token': self.api_key
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        return await response.json()
            return None
        except Exception as e:
            logger.debug(f"Finnhub获取财务指标失败: {e}")
            return None
