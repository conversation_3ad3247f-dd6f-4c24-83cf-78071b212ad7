#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
通达信财务数据读取器
支持中国、美国、香港市场的财务数据解析
"""

import os
import struct
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, List
from qbot.common.logging.logger import LOGGER as logger


class TdxFinancialReader:
    """通达信财务数据读取器"""
    
    def __init__(self, tdx_path: str = "D:/new_tdx"):
        """
        初始化财务数据读取器
        
        Args:
            tdx_path: 通达信安装路径
        """
        self.tdx_path = Path(tdx_path)
        self.logger = logger
        
        # 财务数据文件路径
        self.financial_paths = {
            'CN_A': {
                'gpcw': self.tdx_path / "T0002/hq_cache/gpcw.dat",  # 个股财务数据
                'cwfx': self.tdx_path / "T0002/hq_cache/cwfx.dat",  # 财务分析数据
            },
            'HK': {
                'gpcw': self.tdx_path / "T0002/hq_cache/hkgpcw.dat",  # 港股财务数据
            },
            'US': {
                'gpcw': self.tdx_path / "T0002/hq_cache/usgpcw.dat",  # 美股财务数据
            }
        }
        
        # 财务数据字段定义（基于通达信格式）
        self.financial_fields = {
            'basic': [
                'code',           # 股票代码
                'report_date',    # 报告期
                'total_assets',   # 总资产
                'total_equity',   # 股东权益
                'revenue',        # 营业收入
                'net_profit',     # 净利润
                'eps',           # 每股收益
                'roe',           # 净资产收益率
                'roa',           # 总资产收益率
                'debt_ratio',    # 资产负债率
                'current_ratio', # 流动比率
                'quick_ratio',   # 速动比率
                'pe_ratio',      # 市盈率
                'pb_ratio',      # 市净率
                'market_cap',    # 总市值
                'circulating_cap' # 流通市值
            ]
        }
    
    def is_available(self, market: str = 'CN_A') -> bool:
        """检查财务数据是否可用"""
        try:
            paths = self.financial_paths.get(market, {})
            if not paths:
                return False
            
            # 检查至少有一个财务数据文件存在
            for file_path in paths.values():
                if file_path.exists() and file_path.stat().st_size > 0:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查{market}财务数据可用性失败: {e}")
            return False
    
    def read_financial_data(self, code: str, market: str = 'CN_A') -> Optional[pd.DataFrame]:
        """
        读取股票财务数据
        
        Args:
            code: 股票代码（不含后缀）
            market: 市场类型 ('CN_A', 'HK', 'US')
            
        Returns:
            财务数据DataFrame或None
        """
        try:
            if market == 'CN_A':
                return self._read_china_financial(code)
            elif market == 'HK':
                return self._read_hk_financial(code)
            elif market == 'US':
                return self._read_us_financial(code)
            else:
                self.logger.warning(f"不支持的市场类型: {market}")
                return None
                
        except Exception as e:
            self.logger.error(f"读取{market}市场{code}财务数据失败: {e}")
            return None
    
    def _read_china_financial(self, code: str) -> Optional[pd.DataFrame]:
        """读取中国A股财务数据"""
        try:
            gpcw_file = self.financial_paths['CN_A']['gpcw']
            
            if not gpcw_file.exists():
                self.logger.warning(f"财务数据文件不存在: {gpcw_file}")
                return None
            
            # 读取gpcw.dat文件
            with open(gpcw_file, 'rb') as f:
                data = f.read()
            
            # 解析财务数据（通达信gpcw格式）
            records = self._parse_gpcw_data(data, code)
            
            if not records:
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(records)
            if not df.empty:
                df = df.sort_values('report_date').reset_index(drop=True)
                return df
            
            return None
            
        except Exception as e:
            self.logger.error(f"读取中国A股{code}财务数据失败: {e}")
            return None
    
    def _parse_gpcw_data(self, data: bytes, target_code: str) -> List[Dict]:
        """
        解析gpcw.dat财务数据文件
        
        Args:
            data: 二进制数据
            target_code: 目标股票代码
            
        Returns:
            财务记录列表
        """
        try:
            records = []
            
            # 通达信财务数据格式（每条记录的字节数可能不同）
            # 这里需要根据实际的通达信财务数据格式进行解析
            # 由于格式复杂，这里提供一个基础框架
            
            # 假设每条记录固定长度（实际需要根据通达信格式调整）
            record_size = 200  # 假设值，需要根据实际格式确定
            record_count = len(data) // record_size
            
            for i in range(record_count):
                offset = i * record_size
                record_data = data[offset:offset + record_size]
                
                if len(record_data) < record_size:
                    break
                
                try:
                    # 解析股票代码（前6字节，假设）
                    code_bytes = record_data[:6]
                    code = code_bytes.decode('ascii', errors='ignore').strip('\x00')
                    
                    if code != target_code:
                        continue
                    
                    # 解析其他财务字段（需要根据实际格式调整）
                    # 这里提供一个示例结构
                    unpacked = struct.unpack('<6s8I16f', record_data[:70])  # 示例格式
                    
                    # 构建财务记录
                    record = {
                        'code': code,
                        'report_date': self._parse_date(unpacked[1]),  # 报告期
                        'total_assets': unpacked[2] / 10000.0,        # 总资产（万元）
                        'total_equity': unpacked[3] / 10000.0,        # 股东权益（万元）
                        'revenue': unpacked[4] / 10000.0,             # 营业收入（万元）
                        'net_profit': unpacked[5] / 10000.0,          # 净利润（万元）
                        'eps': unpacked[6] / 100.0,                   # 每股收益（元）
                        'roe': unpacked[7] / 100.0,                   # ROE（%）
                        'roa': unpacked[8] / 100.0,                   # ROA（%）
                        'debt_ratio': unpacked[9] / 100.0,            # 资产负债率（%）
                        'current_ratio': unpacked[10] / 100.0,        # 流动比率
                        'quick_ratio': unpacked[11] / 100.0,          # 速动比率
                        'pe_ratio': unpacked[12] / 100.0,             # 市盈率
                        'pb_ratio': unpacked[13] / 100.0,             # 市净率
                        'market_cap': unpacked[14] / 10000.0,         # 总市值（万元）
                        'circulating_cap': unpacked[15] / 10000.0     # 流通市值（万元）
                    }
                    
                    records.append(record)
                    
                except Exception as e:
                    self.logger.debug(f"解析财务记录{i}失败: {e}")
                    continue
            
            return records
            
        except Exception as e:
            self.logger.error(f"解析gpcw数据失败: {e}")
            return []
    
    def _parse_date(self, date_int: int) -> datetime:
        """解析日期整数为datetime对象"""
        try:
            date_str = str(date_int)
            if len(date_str) == 8:
                year = int(date_str[:4])
                month = int(date_str[4:6])
                day = int(date_str[6:8])
                return datetime(year, month, day)
            else:
                return datetime(1900, 1, 1)  # 默认日期
        except:
            return datetime(1900, 1, 1)
    
    def _read_hk_financial(self, code: str) -> Optional[pd.DataFrame]:
        """读取港股财务数据"""
        try:
            # 港股财务数据解析（格式可能与A股不同）
            hk_file = self.financial_paths['HK']['gpcw']
            
            if not hk_file.exists():
                self.logger.warning(f"港股财务数据文件不存在: {hk_file}")
                return None
            
            # 这里需要根据港股财务数据的实际格式进行解析
            # 暂时返回None，等待实际格式确认
            self.logger.info(f"港股财务数据解析功能待完善")
            return None
            
        except Exception as e:
            self.logger.error(f"读取港股{code}财务数据失败: {e}")
            return None
    
    def _read_us_financial(self, code: str) -> Optional[pd.DataFrame]:
        """读取美股财务数据"""
        try:
            # 美股财务数据解析（格式可能与A股不同）
            us_file = self.financial_paths['US']['gpcw']
            
            if not us_file.exists():
                self.logger.warning(f"美股财务数据文件不存在: {us_file}")
                return None
            
            # 这里需要根据美股财务数据的实际格式进行解析
            # 暂时返回None，等待实际格式确认
            self.logger.info(f"美股财务数据解析功能待完善")
            return None
            
        except Exception as e:
            self.logger.error(f"读取美股{code}财务数据失败: {e}")
            return None
    
    def get_latest_financial_data(self, code: str, market: str = 'CN_A') -> Optional[Dict]:
        """获取最新财务数据"""
        try:
            df = self.read_financial_data(code, market)
            if df is not None and len(df) > 0:
                latest = df.iloc[-1]
                return latest.to_dict()
            return None
            
        except Exception as e:
            self.logger.error(f"获取{market}市场{code}最新财务数据失败: {e}")
            return None


# 全局财务数据读取器实例
tdx_financial_reader = TdxFinancialReader()
