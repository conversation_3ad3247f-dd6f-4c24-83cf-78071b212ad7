#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票列表管理器
从外部文件、数据库或API加载股票列表，避免硬编码
"""

import json
import csv
import sqlite3
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import aiohttp
import asyncio

from qbot.config.constants import MarketType, StockBoard, STOCK_CODES
from qbot.common.result import Result, ErrorCode, ErrorSeverity, success, failure
from qbot.common.enhanced_logger import get_logger


@dataclass
class StockInfo:
    """股票信息数据类"""
    symbol: str
    name: str
    market: str
    board: str
    industry: str = ""
    sector: str = ""
    listing_date: Optional[str] = None
    is_active: bool = True
    last_updated: Optional[str] = None
    
    def to_dict(self) -> Dict:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'StockInfo':
        return cls(**data)


class StockListManager:
    """股票列表管理器"""
    
    def __init__(self, data_dir: str = "data/stocks"):
        self.logger = get_logger("stock_list_manager")
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 缓存
        self._stock_cache: Dict[str, Dict[str, StockInfo]] = {}
        self._cache_timestamp: Dict[str, datetime] = {}
        self._cache_ttl = timedelta(hours=24)  # 缓存24小时
        
        # 数据文件路径
        self.stock_files = {
            MarketType.CN_A: {
                'json': self.data_dir / "cn_a_stocks.json",
                'csv': self.data_dir / "cn_a_stocks.csv",
                'db': self.data_dir / "cn_a_stocks.db"
            },
            MarketType.US: {
                'json': self.data_dir / "us_stocks.json",
                'csv': self.data_dir / "us_stocks.csv",
                'db': self.data_dir / "us_stocks.db"
            },
            MarketType.HK: {
                'json': self.data_dir / "hk_stocks.json",
                'csv': self.data_dir / "hk_stocks.csv",
                'db': self.data_dir / "hk_stocks.db"
            }
        }
    
    async def get_stock_list(self, market: MarketType, 
                           force_refresh: bool = False,
                           include_inactive: bool = False) -> Result[List[StockInfo]]:
        """获取股票列表"""
        operation_name = f"get_stock_list_{market.value}"
        self.logger.start_operation(operation_name, market=market.value)
        
        try:
            # 检查缓存
            if not force_refresh and self._is_cache_valid(market):
                cached_stocks = list(self._stock_cache[market.value].values())
                if not include_inactive:
                    cached_stocks = [s for s in cached_stocks if s.is_active]
                
                self.logger.end_operation(operation_name, success=True, 
                                        source="cache", count=len(cached_stocks))
                return success(cached_stocks)
            
            # 尝试从多个数据源加载
            stock_list = await self._load_from_multiple_sources(market)
            
            if not stock_list:
                # 生成默认股票列表
                stock_list = self._generate_default_stock_list(market)
                self.logger.warning(f"使用生成的默认{market.value}股票列表")
            
            # 过滤非活跃股票
            if not include_inactive:
                stock_list = [s for s in stock_list if s.is_active]
            
            # 更新缓存
            self._update_cache(market, stock_list)
            
            self.logger.end_operation(operation_name, success=True, 
                                    count=len(stock_list))
            return success(stock_list)
            
        except Exception as e:
            self.logger.log_exception(e,
                context={'market': market.value},
                error_code=ErrorCode.DATA_SOURCE_UNAVAILABLE,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error=str(e))
            return failure(ErrorCode.DATA_SOURCE_UNAVAILABLE, 
                         f"获取{market.value}股票列表失败: {str(e)}")
    
    def _is_cache_valid(self, market: MarketType) -> bool:
        """检查缓存是否有效"""
        market_key = market.value
        if market_key not in self._stock_cache:
            return False
        
        if market_key not in self._cache_timestamp:
            return False
        
        return datetime.now() - self._cache_timestamp[market_key] < self._cache_ttl
    
    def _update_cache(self, market: MarketType, stock_list: List[StockInfo]):
        """更新缓存"""
        market_key = market.value
        self._stock_cache[market_key] = {stock.symbol: stock for stock in stock_list}
        self._cache_timestamp[market_key] = datetime.now()
    
    async def _load_from_multiple_sources(self, market: MarketType) -> List[StockInfo]:
        """从多个数据源加载股票列表"""
        # 数据源优先级：数据库 > JSON文件 > CSV文件 > 在线API
        
        # 1. 尝试从数据库加载
        try:
            stocks = self._load_from_database(market)
            if stocks:
                self.logger.info(f"从数据库加载{market.value}股票列表: {len(stocks)}只")
                return stocks
        except Exception as e:
            self.logger.debug(f"数据库加载失败: {e}")
        
        # 2. 尝试从JSON文件加载
        try:
            stocks = self._load_from_json(market)
            if stocks:
                self.logger.info(f"从JSON文件加载{market.value}股票列表: {len(stocks)}只")
                return stocks
        except Exception as e:
            self.logger.debug(f"JSON文件加载失败: {e}")
        
        # 3. 尝试从CSV文件加载
        try:
            stocks = self._load_from_csv(market)
            if stocks:
                self.logger.info(f"从CSV文件加载{market.value}股票列表: {len(stocks)}只")
                return stocks
        except Exception as e:
            self.logger.debug(f"CSV文件加载失败: {e}")
        
        # 4. 尝试从在线API加载
        try:
            stocks = await self._load_from_api(market)
            if stocks:
                self.logger.info(f"从在线API加载{market.value}股票列表: {len(stocks)}只")
                # 保存到本地文件
                await self._save_to_files(market, stocks)
                return stocks
        except Exception as e:
            self.logger.debug(f"在线API加载失败: {e}")
        
        return []
    
    def _load_from_database(self, market: MarketType) -> List[StockInfo]:
        """从SQLite数据库加载股票列表"""
        db_file = self.stock_files[market]['db']
        if not db_file.exists():
            return []
        
        stocks = []
        with sqlite3.connect(db_file) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT symbol, name, market, board, industry, sector, 
                       listing_date, is_active, last_updated
                FROM stocks 
                WHERE is_active = 1
                ORDER BY symbol
            """)
            
            for row in cursor.fetchall():
                stock = StockInfo(
                    symbol=row['symbol'],
                    name=row['name'],
                    market=row['market'],
                    board=row['board'],
                    industry=row['industry'] or "",
                    sector=row['sector'] or "",
                    listing_date=row['listing_date'],
                    is_active=bool(row['is_active']),
                    last_updated=row['last_updated']
                )
                stocks.append(stock)
        
        return stocks
    
    def _load_from_json(self, market: MarketType) -> List[StockInfo]:
        """从JSON文件加载股票列表"""
        json_file = self.stock_files[market]['json']
        if not json_file.exists():
            return []
        
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        stocks = []
        for item in data.get('stocks', []):
            stock = StockInfo.from_dict(item)
            if stock.is_active:
                stocks.append(stock)
        
        return stocks
    
    def _load_from_csv(self, market: MarketType) -> List[StockInfo]:
        """从CSV文件加载股票列表"""
        csv_file = self.stock_files[market]['csv']
        if not csv_file.exists():
            return []
        
        stocks = []
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                stock = StockInfo(
                    symbol=row['symbol'],
                    name=row['name'],
                    market=row['market'],
                    board=row.get('board', ''),
                    industry=row.get('industry', ''),
                    sector=row.get('sector', ''),
                    listing_date=row.get('listing_date'),
                    is_active=row.get('is_active', 'true').lower() == 'true',
                    last_updated=row.get('last_updated')
                )
                if stock.is_active:
                    stocks.append(stock)
        
        return stocks
    
    async def _load_from_api(self, market: MarketType) -> List[StockInfo]:
        """从在线API加载股票列表"""
        # 这里可以集成各种在线API
        if market == MarketType.CN_A:
            return await self._load_cn_stocks_from_api()
        elif market == MarketType.US:
            return await self._load_us_stocks_from_api()
        elif market == MarketType.HK:
            return await self._load_hk_stocks_from_api()
        
        return []
    
    async def _load_cn_stocks_from_api(self) -> List[StockInfo]:
        """从API加载中国A股列表"""
        # 示例：从东方财富API获取
        try:
            url = "http://push2.eastmoney.com/api/qt/clist/get"
            params = {
                'pn': 1,
                'pz': 5000,
                'po': 1,
                'np': 1,
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': 2,
                'invt': 2,
                'fid': 'f3',
                'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_eastmoney_response(data)
        except Exception as e:
            self.logger.debug(f"东方财富API调用失败: {e}")
        
        return []
    
    async def _load_us_stocks_from_api(self) -> List[StockInfo]:
        """从API加载美股列表"""
        # 可以集成如Alpha Vantage, IEX Cloud等API
        return []
    
    async def _load_hk_stocks_from_api(self) -> List[StockInfo]:
        """从API加载港股列表"""
        # 可以集成港交所或其他API
        return []
    
    def _parse_eastmoney_response(self, data: Dict) -> List[StockInfo]:
        """解析东方财富API响应"""
        stocks = []
        
        if 'data' in data and 'diff' in data['data']:
            for item in data['data']['diff']:
                try:
                    symbol = item.get('f12', '')
                    name = item.get('f14', '')
                    
                    if symbol and name:
                        # 判断板块
                        board = self._determine_board_from_symbol(symbol)
                        
                        stock = StockInfo(
                            symbol=symbol,
                            name=name,
                            market=MarketType.CN_A.value,
                            board=board,
                            is_active=True,
                            last_updated=datetime.now().isoformat()
                        )
                        stocks.append(stock)
                        
                except Exception as e:
                    self.logger.debug(f"解析股票数据失败: {e}")
                    continue
        
        return stocks
    
    def _determine_board_from_symbol(self, symbol: str) -> str:
        """根据股票代码判断板块"""
        if symbol.startswith(STOCK_CODES.SH_MAIN_PREFIXES):
            return StockBoard.MAIN_BOARD.value
        elif symbol.startswith(STOCK_CODES.SZ_MAIN_PREFIXES):
            return StockBoard.MAIN_BOARD.value
        elif symbol.startswith(STOCK_CODES.SME_PREFIX):
            return StockBoard.SME_BOARD.value
        elif symbol.startswith(STOCK_CODES.GROWTH_PREFIXES):
            return StockBoard.GROWTH_BOARD.value
        elif symbol.startswith(STOCK_CODES.STAR_PREFIX):
            return StockBoard.STAR_BOARD.value
        elif symbol.startswith(STOCK_CODES.BEIJING_PREFIXES):
            return StockBoard.BEIJING_BOARD.value
        else:
            return "未知板块"
    
    def _generate_default_stock_list(self, market: MarketType) -> List[StockInfo]:
        """生成默认股票列表（基于代码范围）"""
        stocks = []
        
        if market == MarketType.CN_A:
            stocks.extend(self._generate_cn_stock_codes())
        elif market == MarketType.US:
            stocks.extend(self._generate_us_stock_codes())
        elif market == MarketType.HK:
            stocks.extend(self._generate_hk_stock_codes())
        
        return stocks
    
    def _generate_cn_stock_codes(self) -> List[StockInfo]:
        """生成中国A股代码"""
        stocks = []
        
        # 上海主板
        for prefix in STOCK_CODES.SH_MAIN_PREFIXES:
            for i in range(0, 1000, STOCK_CODES.MAIN_BOARD_STEP):
                symbol = f"{prefix}{i:03d}"
                stock = StockInfo(
                    symbol=symbol,
                    name=f"股票{symbol}",
                    market=MarketType.CN_A.value,
                    board=StockBoard.MAIN_BOARD.value,
                    is_active=True
                )
                stocks.append(stock)
        
        # 深圳主板
        for prefix in STOCK_CODES.SZ_MAIN_PREFIXES:
            for i in range(0, 1000, STOCK_CODES.MAIN_BOARD_STEP):
                symbol = f"{prefix}{i:03d}"
                stock = StockInfo(
                    symbol=symbol,
                    name=f"股票{symbol}",
                    market=MarketType.CN_A.value,
                    board=StockBoard.MAIN_BOARD.value,
                    is_active=True
                )
                stocks.append(stock)
        
        # 中小板
        for i in range(0, 1000, STOCK_CODES.MAIN_BOARD_STEP):
            symbol = f"{STOCK_CODES.SME_PREFIX}{i:03d}"
            stock = StockInfo(
                symbol=symbol,
                name=f"股票{symbol}",
                market=MarketType.CN_A.value,
                board=StockBoard.SME_BOARD.value,
                is_active=True
            )
            stocks.append(stock)
        
        # 创业板
        for prefix in STOCK_CODES.GROWTH_PREFIXES:
            for i in range(0, 1000, STOCK_CODES.GROWTH_BOARD_STEP):
                symbol = f"{prefix}{i:03d}"
                stock = StockInfo(
                    symbol=symbol,
                    name=f"股票{symbol}",
                    market=MarketType.CN_A.value,
                    board=StockBoard.GROWTH_BOARD.value,
                    is_active=True
                )
                stocks.append(stock)
        
        # 科创板
        for i in range(0, 1000, STOCK_CODES.GROWTH_BOARD_STEP):
            symbol = f"{STOCK_CODES.STAR_PREFIX}{i:03d}"
            stock = StockInfo(
                symbol=symbol,
                name=f"股票{symbol}",
                market=MarketType.CN_A.value,
                board=StockBoard.STAR_BOARD.value,
                is_active=True
            )
            stocks.append(stock)
        
        return stocks
    
    def _generate_us_stock_codes(self) -> List[StockInfo]:
        """生成美股代码（示例）"""
        # 这里可以添加常见的美股代码
        famous_us_stocks = [
            ('AAPL', 'Apple Inc.'),
            ('MSFT', 'Microsoft Corporation'),
            ('GOOGL', 'Alphabet Inc.'),
            ('AMZN', 'Amazon.com Inc.'),
            ('TSLA', 'Tesla Inc.'),
            ('META', 'Meta Platforms Inc.'),
            ('NVDA', 'NVIDIA Corporation'),
            ('NFLX', 'Netflix Inc.'),
        ]
        
        stocks = []
        for symbol, name in famous_us_stocks:
            stock = StockInfo(
                symbol=symbol,
                name=name,
                market=MarketType.US.value,
                board="NASDAQ/NYSE",
                is_active=True
            )
            stocks.append(stock)
        
        return stocks
    
    def _generate_hk_stock_codes(self) -> List[StockInfo]:
        """生成港股代码（示例）"""
        # 港股代码通常是4位数字
        stocks = []
        for i in range(1, 10000, 100):  # 每100个取一个样本
            symbol = f"{i:04d}"
            stock = StockInfo(
                symbol=symbol,
                name=f"港股{symbol}",
                market=MarketType.HK.value,
                board="主板",
                is_active=True
            )
            stocks.append(stock)
        
        return stocks
    
    async def _save_to_files(self, market: MarketType, stocks: List[StockInfo]):
        """保存股票列表到文件"""
        try:
            # 保存到JSON
            json_data = {
                'market': market.value,
                'last_updated': datetime.now().isoformat(),
                'count': len(stocks),
                'stocks': [stock.to_dict() for stock in stocks]
            }
            
            json_file = self.stock_files[market]['json']
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            
            # 保存到CSV
            csv_file = self.stock_files[market]['csv']
            with open(csv_file, 'w', encoding='utf-8', newline='') as f:
                if stocks:
                    fieldnames = list(stocks[0].to_dict().keys())
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    for stock in stocks:
                        writer.writerow(stock.to_dict())
            
            self.logger.info(f"股票列表已保存到文件: {len(stocks)}只股票")
            
        except Exception as e:
            self.logger.error(f"保存股票列表到文件失败: {e}")


# 全局股票列表管理器实例
stock_list_manager = StockListManager()
