#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能优化使用示例
演示如何使用优化后的股票筛选器和内存管理工具
"""

import asyncio
import time
import pandas as pd
import numpy as np
from typing import Dict, List
from qbot.agents.optimized_stock_screener import OptimizedStockScreener
from qbot.utils.memory_optimizer import memory_optimizer, dataframe_processor
from qbot.common.enhanced_logger import get_logger


class PerformanceDemo:
    """性能优化演示"""
    
    def __init__(self):
        self.logger = get_logger("performance_demo")
        self.screener = OptimizedStockScreener(batch_size=1000, max_workers=20)
    
    def generate_large_dataset(self, size: int = 100000) -> pd.DataFrame:
        """生成大型测试数据集"""
        self.logger.info(f"生成 {size:,} 行测试数据...")
        
        np.random.seed(42)
        
        # 生成股票代码
        symbols = [f'STOCK{i:06d}' for i in range(size)]
        
        # 生成各种数据类型的列
        data = {
            # 字符串列
            'symbol': symbols,
            'market': np.random.choice(['CN_A', 'US', 'HK'], size),
            'sector': np.random.choice(['Technology', 'Finance', 'Healthcare', 'Energy', 'Consumer'], size),
            'ma_trend': np.random.choice(['上升', '下降', '震荡'], size),
            
            # 数值列
            'current_price': np.random.uniform(1, 1000, size),
            'volume': np.random.randint(1000, 100000000, size),
            'market_cap': np.random.uniform(1e6, 1e12, size),
            'pe_ratio': np.random.uniform(1, 100, size),
            'pb_ratio': np.random.uniform(0.1, 20, size),
            'roe': np.random.uniform(-0.5, 0.5, size),
            'debt_ratio': np.random.uniform(0, 1, size),
            'rsi': np.random.uniform(0, 100, size),
            'liquidity_ratio': np.random.uniform(0.001, 0.2, size),
            'volatility': np.random.uniform(0.05, 1.0, size),
            'daily_volume': np.random.uniform(1e5, 1e10, size),
            'short_interest': np.random.uniform(0, 0.3, size),
            
            # 布尔列
            'is_dividend_stock': np.random.choice([True, False], size),
            'is_growth_stock': np.random.choice([True, False], size),
        }
        
        df = pd.DataFrame(data)
        
        # 显示原始内存使用
        original_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
        self.logger.info(f"原始数据内存使用: {original_memory:.1f}MB")
        
        return df
    
    def demonstrate_memory_optimization(self, df: pd.DataFrame) -> pd.DataFrame:
        """演示内存优化"""
        print("\n🧠 内存优化演示")
        print("-" * 40)
        
        # 显示优化前的内存使用
        memory_before = memory_optimizer.get_memory_usage()
        print(f"优化前进程内存: {memory_before['rss_mb']:.1f}MB")
        
        # 显示DataFrame内存使用详情
        memory_usage = df.memory_usage(deep=True)
        print(f"DataFrame总内存: {memory_usage.sum() / 1024 / 1024:.1f}MB")
        print("各列内存使用:")
        for col, usage in memory_usage.items():
            if usage > 0:
                print(f"  {col}: {usage / 1024 / 1024:.2f}MB ({df[col].dtype})")
        
        # 执行内存优化
        print("\n🔧 执行数据类型优化...")
        optimized_df = memory_optimizer.optimize_dataframe_dtypes(df)
        
        # 显示优化后的内存使用
        memory_after = memory_optimizer.get_memory_usage()
        optimized_memory_usage = optimized_df.memory_usage(deep=True)
        
        print(f"优化后DataFrame内存: {optimized_memory_usage.sum() / 1024 / 1024:.1f}MB")
        print(f"内存节省: {(memory_usage.sum() - optimized_memory_usage.sum()) / memory_usage.sum() * 100:.1f}%")
        
        # 显示数据类型变化
        print("\n📊 数据类型优化结果:")
        for col in df.columns:
            if df[col].dtype != optimized_df[col].dtype:
                print(f"  {col}: {df[col].dtype} -> {optimized_df[col].dtype}")
        
        return optimized_df
    
    def demonstrate_vectorized_operations(self, df: pd.DataFrame):
        """演示向量化操作"""
        print("\n⚡ 向量化操作演示")
        print("-" * 40)
        
        # 定义筛选条件
        criteria = {
            'fundamental': {
                'pe_ratio': {'min': 5, 'max': 30},
                'pb_ratio': {'min': 0.5, 'max': 5},
                'roe': {'min': 0.05, 'max': 1.0},
                'debt_ratio': {'min': 0, 'max': 0.6}
            },
            'technical': {
                'rsi_min': 30,
                'rsi_max': 70,
                'ma_trend': '上升'
            },
            'leverage': {
                'short_interest_max': 0.1,
                'liquidity_min': 0.02
            }
        }
        
        print(f"测试数据: {len(df):,} 行")
        
        # 测试向量化筛选
        start_time = time.time()
        filtered_df = self.screener.apply_screening_criteria_vectorized(df, criteria)
        vectorized_time = time.time() - start_time
        
        print(f"向量化筛选结果: {len(filtered_df):,} 行通过筛选")
        print(f"筛选时间: {vectorized_time:.4f}秒")
        print(f"处理速度: {len(df)/vectorized_time:,.0f} 行/秒")
        
        # 测试向量化评分
        if len(filtered_df) > 0:
            margin_criteria = {
                'min_market_cap': 1e9,
                'min_daily_volume': 1e6,
                'min_price': 2.0,
                'max_volatility': 0.6,
                'min_liquidity_ratio': 0.02
            }
            
            start_time = time.time()
            margin_scores = self.screener.calculate_margin_score_vectorized(filtered_df, margin_criteria)
            scoring_time = time.time() - start_time
            
            print(f"评分计算时间: {scoring_time:.4f}秒")
            print(f"平均评分: {margin_scores.mean():.2f}")
            print(f"最高评分: {margin_scores.max():.2f}")
    
    def demonstrate_chunk_processing(self, df: pd.DataFrame):
        """演示分块处理"""
        print("\n📦 分块处理演示")
        print("-" * 40)
        
        # 建议分块大小
        suggested_chunk_size = memory_optimizer.suggest_chunk_size(df, target_memory_mb=50)
        print(f"建议分块大小: {suggested_chunk_size:,} 行")
        
        # 定义一个复杂的处理函数
        def complex_calculation(chunk_df):
            """复杂计算示例"""
            # 计算多个技术指标
            chunk_df = chunk_df.copy()
            chunk_df['price_ma_5'] = chunk_df.groupby('symbol')['current_price'].transform(
                lambda x: x.rolling(min(5, len(x))).mean()
            )
            chunk_df['volume_ma_10'] = chunk_df.groupby('symbol')['volume'].transform(
                lambda x: x.rolling(min(10, len(x))).mean()
            )
            chunk_df['volatility_rank'] = chunk_df['volatility'].rank(pct=True)
            
            return chunk_df
        
        # 分块处理
        start_time = time.time()
        processed_df = memory_optimizer.process_in_chunks(
            df, complex_calculation, chunk_size=suggested_chunk_size
        )
        chunk_time = time.time() - start_time
        
        print(f"分块处理完成: {len(processed_df):,} 行")
        print(f"处理时间: {chunk_time:.2f}秒")
        print(f"处理速度: {len(df)/chunk_time:,.0f} 行/秒")
        
        # 内存使用情况
        memory_usage = memory_optimizer.get_memory_usage()
        print(f"当前内存使用: {memory_usage['rss_mb']:.1f}MB")
    
    async def demonstrate_batch_processing(self):
        """演示批处理"""
        print("\n🔄 批处理演示")
        print("-" * 40)
        
        # 生成大量股票代码
        stock_list = [f'BATCH{i:06d}' for i in range(10000)]
        print(f"批处理股票数量: {len(stock_list):,}")
        
        # 测试不同批次大小的性能
        batch_sizes = [100, 500, 1000, 2000]
        
        for batch_size in batch_sizes:
            self.screener.batch_size = batch_size
            
            start_time = time.time()
            results = await self.screener.batch_process_stocks(
                stock_list[:5000],  # 测试前5000只
                'CN_A',
                self.screener.get_stock_data_batch
            )
            processing_time = time.time() - start_time
            
            print(f"批次大小 {batch_size}: {len(results)} 结果, "
                  f"{processing_time:.2f}秒, "
                  f"{len(results)/processing_time:.1f} 股票/秒")
    
    def demonstrate_performance_monitoring(self, df: pd.DataFrame):
        """演示性能监控"""
        print("\n📊 性能监控演示")
        print("-" * 40)
        
        # 使用内存监控装饰器
        @memory_optimizer.monitor_memory_usage(threshold_mb=500)
        def memory_intensive_operation(data):
            """内存密集型操作"""
            # 创建多个数据副本
            copies = []
            for i in range(5):
                copy_df = data.copy()
                copy_df[f'calculated_col_{i}'] = copy_df['current_price'] * np.random.random(len(copy_df))
                copies.append(copy_df)
            
            # 合并所有副本
            result = pd.concat(copies, ignore_index=True)
            return result
        
        # 执行内存密集型操作
        print("执行内存密集型操作...")
        result = memory_intensive_operation(df.head(10000))
        print(f"操作完成，结果大小: {len(result):,} 行")
        
        # 显示性能指标
        metrics = self.screener.get_performance_metrics()
        print("\n📈 性能指标:")
        for key, value in metrics.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
            else:
                print(f"  {key}: {value}")
        
        # 清理内存
        memory_optimizer.cleanup_memory()


async def main():
    """主演示函数"""
    print("🚀 股票筛选器性能优化演示")
    print("=" * 60)
    
    demo = PerformanceDemo()
    
    # 1. 生成大型数据集
    print("\n📊 生成测试数据...")
    large_df = demo.generate_large_dataset(50000)  # 5万行数据
    
    # 2. 内存优化演示
    optimized_df = demo.demonstrate_memory_optimization(large_df)
    
    # 3. 向量化操作演示
    demo.demonstrate_vectorized_operations(optimized_df)
    
    # 4. 分块处理演示
    demo.demonstrate_chunk_processing(optimized_df)
    
    # 5. 批处理演示
    await demo.demonstrate_batch_processing()
    
    # 6. 性能监控演示
    demo.demonstrate_performance_monitoring(optimized_df)
    
    print("\n" + "=" * 60)
    print("🎉 性能优化演示完成！")
    print("\n💡 关键优化技术:")
    print("  1. 向量化操作替代循环")
    print("  2. 数据类型优化减少内存使用")
    print("  3. 分块处理大数据集")
    print("  4. 批处理提高并发效率")
    print("  5. 内存监控和自动清理")
    print("  6. 性能指标监控和优化")


if __name__ == "__main__":
    asyncio.run(main())
