#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI选股选基面板
"""

import wx
import json
import requests
import threading
from datetime import datetime
from qbot.gui.common.SysFile import Base_File_Oper
from qbot.common.logging.logger import LOGGER as logger


class AIStockFundSelectionPanel(wx.Panel):
    """AI选股选基面板"""
    
    def __init__(self, parent):
        super().__init__(parent)
        self.ai_config = None
        self.init_ui()
        self.load_ai_config()
    
    def init_ui(self):
        """初始化界面"""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        self.SetSizer(main_sizer)
        
        # 标题区域
        title_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        title_label = wx.StaticText(self, -1, "AI智能选股选基")
        title_font = wx.Font(20, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
        title_label.SetFont(title_font)
        title_label.SetForegroundColour(wx.Colour(52, 152, 219))
        title_sizer.Add(title_label, 0, wx.ALL | wx.CENTER, 10)
        
        # 状态指示器
        self.status_indicator = wx.StaticText(self, -1, "●")
        self.status_indicator.SetFont(wx.Font(16, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        self.status_indicator.SetForegroundColour(wx.Colour(46, 204, 113))
        title_sizer.Add(self.status_indicator, 0, wx.ALL | wx.CENTER, 5)
        
        main_sizer.Add(title_sizer, 0, wx.CENTER | wx.ALL, 10)
        
        # 功能描述
        desc_text = wx.StaticText(self, -1, 
            "基于AI算法的智能选股选基系统，提供专业的投资标的筛选和分析服务")
        desc_text.SetForegroundColour(wx.Colour(100, 100, 100))
        main_sizer.Add(desc_text, 0, wx.ALL | wx.CENTER, 5)
        
        # 主要内容区域
        content_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # 左侧选择区域
        left_panel = wx.Panel(self)
        left_sizer = wx.BoxSizer(wx.VERTICAL)
        left_panel.SetSizer(left_sizer)
        
        # 选股选基类型选择
        type_box = wx.StaticBox(left_panel, -1, "选择类型")
        type_sizer = wx.StaticBoxSizer(type_box, wx.VERTICAL)
        
        self.selection_type = wx.Choice(left_panel, choices=[
            "智能选股", "智能选基", "行业轮动", "主题投资", "价值投资", "成长投资"
        ])
        self.selection_type.SetSelection(0)
        type_sizer.Add(self.selection_type, 0, wx.EXPAND | wx.ALL, 5)
        
        left_sizer.Add(type_sizer, 0, wx.EXPAND | wx.ALL, 10)
        
        # 筛选条件
        filter_box = wx.StaticBox(left_panel, -1, "筛选条件")
        filter_sizer = wx.StaticBoxSizer(filter_box, wx.VERTICAL)

        # 创建筛选条件的Notebook
        self.filter_notebook = wx.Notebook(left_panel)

        # 基础筛选页面
        basic_panel = wx.Panel(self.filter_notebook)
        basic_sizer = wx.BoxSizer(wx.VERTICAL)
        basic_panel.SetSizer(basic_sizer)

        # 市值范围
        market_cap_sizer = wx.BoxSizer(wx.HORIZONTAL)
        market_cap_sizer.Add(wx.StaticText(basic_panel, -1, "市值范围:"), 0, wx.ALL | wx.CENTER, 5)
        self.market_cap_choice = wx.Choice(basic_panel, choices=[
            "不限", "超大盘股(>2000亿)", "大盘股(500-2000亿)", "中盘股(100-500亿)", "小盘股(50-100亿)", "微盘股(<50亿)"
        ])
        self.market_cap_choice.SetSelection(0)
        market_cap_sizer.Add(self.market_cap_choice, 1, wx.EXPAND | wx.ALL, 5)
        basic_sizer.Add(market_cap_sizer, 0, wx.EXPAND | wx.ALL, 5)

        # 行业选择
        industry_sizer = wx.BoxSizer(wx.HORIZONTAL)
        industry_sizer.Add(wx.StaticText(basic_panel, -1, "行业板块:"), 0, wx.ALL | wx.CENTER, 5)
        self.industry_choice = wx.Choice(basic_panel, choices=[
            "不限", "人工智能", "新能源", "医药生物", "半导体", "消费电子", "新材料",
            "军工", "环保", "5G通信", "云计算", "新基建", "消费升级", "金融科技"
        ])
        self.industry_choice.SetSelection(0)
        industry_sizer.Add(self.industry_choice, 1, wx.EXPAND | wx.ALL, 5)
        basic_sizer.Add(industry_sizer, 0, wx.EXPAND | wx.ALL, 5)

        # 风险等级
        risk_sizer = wx.BoxSizer(wx.HORIZONTAL)
        risk_sizer.Add(wx.StaticText(basic_panel, -1, "风险等级:"), 0, wx.ALL | wx.CENTER, 5)
        self.risk_choice = wx.Choice(basic_panel, choices=[
            "不限", "低风险", "中低风险", "中风险", "中高风险", "高风险"
        ])
        self.risk_choice.SetSelection(0)
        risk_sizer.Add(self.risk_choice, 1, wx.EXPAND | wx.ALL, 5)
        basic_sizer.Add(risk_sizer, 0, wx.EXPAND | wx.ALL, 5)

        self.filter_notebook.AddPage(basic_panel, "基础筛选")

        # 财务指标页面
        financial_panel = wx.Panel(self.filter_notebook)
        financial_sizer = wx.BoxSizer(wx.VERTICAL)
        financial_panel.SetSizer(financial_sizer)

        # PE比率
        pe_sizer = wx.BoxSizer(wx.HORIZONTAL)
        pe_sizer.Add(wx.StaticText(financial_panel, -1, "PE比率:"), 0, wx.ALL | wx.CENTER, 5)
        self.pe_choice = wx.Choice(financial_panel, choices=[
            "不限", "低估值(PE<15)", "合理估值(15≤PE<25)", "高估值(25≤PE<50)", "超高估值(PE≥50)"
        ])
        self.pe_choice.SetSelection(0)
        pe_sizer.Add(self.pe_choice, 1, wx.EXPAND | wx.ALL, 5)
        financial_sizer.Add(pe_sizer, 0, wx.EXPAND | wx.ALL, 5)

        # PB比率
        pb_sizer = wx.BoxSizer(wx.HORIZONTAL)
        pb_sizer.Add(wx.StaticText(financial_panel, -1, "PB比率:"), 0, wx.ALL | wx.CENTER, 5)
        self.pb_choice = wx.Choice(financial_panel, choices=[
            "不限", "破净股(PB<1)", "低PB(1≤PB<2)", "合理PB(2≤PB<5)", "高PB(PB≥5)"
        ])
        self.pb_choice.SetSelection(0)
        pb_sizer.Add(self.pb_choice, 1, wx.EXPAND | wx.ALL, 5)
        financial_sizer.Add(pb_sizer, 0, wx.EXPAND | wx.ALL, 5)

        # ROE净资产收益率
        roe_sizer = wx.BoxSizer(wx.HORIZONTAL)
        roe_sizer.Add(wx.StaticText(financial_panel, -1, "ROE净资产收益率:"), 0, wx.ALL | wx.CENTER, 5)
        self.roe_choice = wx.Choice(financial_panel, choices=[
            "不限", "优秀(ROE≥20%)", "良好(15%≤ROE<20%)", "一般(10%≤ROE<15%)", "较差(ROE<10%)"
        ])
        self.roe_choice.SetSelection(0)
        roe_sizer.Add(self.roe_choice, 1, wx.EXPAND | wx.ALL, 5)
        financial_sizer.Add(roe_sizer, 0, wx.EXPAND | wx.ALL, 5)

        # 营收增长率
        growth_sizer = wx.BoxSizer(wx.HORIZONTAL)
        growth_sizer.Add(wx.StaticText(financial_panel, -1, "营收增长率:"), 0, wx.ALL | wx.CENTER, 5)
        self.growth_choice = wx.Choice(financial_panel, choices=[
            "不限", "高增长(≥30%)", "中增长(15%-30%)", "稳增长(5%-15%)", "低增长(<5%)", "负增长(<0%)"
        ])
        self.growth_choice.SetSelection(0)
        growth_sizer.Add(self.growth_choice, 1, wx.EXPAND | wx.ALL, 5)
        financial_sizer.Add(growth_sizer, 0, wx.EXPAND | wx.ALL, 5)

        self.filter_notebook.AddPage(financial_panel, "财务指标")

        # 技术指标页面
        technical_panel = wx.Panel(self.filter_notebook)
        technical_sizer = wx.BoxSizer(wx.VERTICAL)
        technical_panel.SetSizer(technical_sizer)

        # MACD指标
        macd_sizer = wx.BoxSizer(wx.HORIZONTAL)
        macd_sizer.Add(wx.StaticText(technical_panel, -1, "MACD信号:"), 0, wx.ALL | wx.CENTER, 5)
        self.macd_choice = wx.Choice(technical_panel, choices=[
            "不限", "金叉买入", "死叉卖出", "零轴上方", "零轴下方", "背离信号"
        ])
        self.macd_choice.SetSelection(0)
        macd_sizer.Add(self.macd_choice, 1, wx.EXPAND | wx.ALL, 5)
        technical_sizer.Add(macd_sizer, 0, wx.EXPAND | wx.ALL, 5)

        # RSI指标
        rsi_sizer = wx.BoxSizer(wx.HORIZONTAL)
        rsi_sizer.Add(wx.StaticText(technical_panel, -1, "RSI相对强弱:"), 0, wx.ALL | wx.CENTER, 5)
        self.rsi_choice = wx.Choice(technical_panel, choices=[
            "不限", "超买区(RSI>70)", "强势区(50<RSI≤70)", "弱势区(30≤RSI≤50)", "超卖区(RSI<30)"
        ])
        self.rsi_choice.SetSelection(0)
        rsi_sizer.Add(self.rsi_choice, 1, wx.EXPAND | wx.ALL, 5)
        technical_sizer.Add(rsi_sizer, 0, wx.EXPAND | wx.ALL, 5)

        # 均线系统
        ma_sizer = wx.BoxSizer(wx.HORIZONTAL)
        ma_sizer.Add(wx.StaticText(technical_panel, -1, "均线系统:"), 0, wx.ALL | wx.CENTER, 5)
        self.ma_choice = wx.Choice(technical_panel, choices=[
            "不限", "多头排列", "空头排列", "突破20日线", "突破60日线", "突破年线"
        ])
        self.ma_choice.SetSelection(0)
        ma_sizer.Add(self.ma_choice, 1, wx.EXPAND | wx.ALL, 5)
        technical_sizer.Add(ma_sizer, 0, wx.EXPAND | wx.ALL, 5)

        # 成交量指标
        volume_sizer = wx.BoxSizer(wx.HORIZONTAL)
        volume_sizer.Add(wx.StaticText(technical_panel, -1, "成交量:"), 0, wx.ALL | wx.CENTER, 5)
        self.volume_choice = wx.Choice(technical_panel, choices=[
            "不限", "放量上涨", "缩量上涨", "放量下跌", "缩量下跌", "量价齐升"
        ])
        self.volume_choice.SetSelection(0)
        volume_sizer.Add(self.volume_choice, 1, wx.EXPAND | wx.ALL, 5)
        technical_sizer.Add(volume_sizer, 0, wx.EXPAND | wx.ALL, 5)

        self.filter_notebook.AddPage(technical_panel, "技术指标")

        # 组合策略页面
        strategy_panel = wx.Panel(self.filter_notebook)
        strategy_sizer = wx.BoxSizer(wx.VERTICAL)
        strategy_panel.SetSizer(strategy_sizer)

        # 预设策略组合
        preset_label = wx.StaticText(strategy_panel, -1, "预设策略组合:")
        preset_font = preset_label.GetFont()
        preset_font.SetWeight(wx.FONTWEIGHT_BOLD)
        preset_label.SetFont(preset_font)
        strategy_sizer.Add(preset_label, 0, wx.ALL, 5)

        # 策略选择列表
        self.strategy_list = wx.CheckListBox(strategy_panel, choices=[
            "价值投资策略 (低PE+高ROE+稳定增长)",
            "成长投资策略 (高增长+新兴行业+技术突破)",
            "趋势跟踪策略 (多头排列+放量上涨+MACD金叉)",
            "逆向投资策略 (超卖+低估值+基本面良好)",
            "动量投资策略 (强势突破+高成交量+技术指标向好)",
            "防御性投资策略 (低波动+高分红+稳定行业)",
            "小盘成长策略 (小市值+高增长+新概念)",
            "大盘价值策略 (大市值+低估值+分红稳定)"
        ])
        strategy_sizer.Add(self.strategy_list, 1, wx.EXPAND | wx.ALL, 5)

        # 自定义权重
        weight_label = wx.StaticText(strategy_panel, -1, "自定义权重分配:")
        weight_font = weight_label.GetFont()
        weight_font.SetWeight(wx.FONTWEIGHT_BOLD)
        weight_label.SetFont(weight_font)
        strategy_sizer.Add(weight_label, 0, wx.ALL, 5)

        weight_grid = wx.FlexGridSizer(2, 4, 5, 10)
        weight_grid.AddGrowableCol(1)
        weight_grid.AddGrowableCol(3)

        # 基本面权重
        weight_grid.Add(wx.StaticText(strategy_panel, -1, "基本面:"), 0, wx.ALIGN_CENTER_VERTICAL)
        self.fundamental_weight = wx.SpinCtrlDouble(strategy_panel, value="40", min=0, max=100, inc=5)
        weight_grid.Add(self.fundamental_weight, 0, wx.EXPAND)

        # 技术面权重
        weight_grid.Add(wx.StaticText(strategy_panel, -1, "技术面:"), 0, wx.ALIGN_CENTER_VERTICAL)
        self.technical_weight = wx.SpinCtrlDouble(strategy_panel, value="30", min=0, max=100, inc=5)
        weight_grid.Add(self.technical_weight, 0, wx.EXPAND)

        # 市场情绪权重
        weight_grid.Add(wx.StaticText(strategy_panel, -1, "市场情绪:"), 0, wx.ALIGN_CENTER_VERTICAL)
        self.sentiment_weight = wx.SpinCtrlDouble(strategy_panel, value="20", min=0, max=100, inc=5)
        weight_grid.Add(self.sentiment_weight, 0, wx.EXPAND)

        # 宏观环境权重
        weight_grid.Add(wx.StaticText(strategy_panel, -1, "宏观环境:"), 0, wx.ALIGN_CENTER_VERTICAL)
        self.macro_weight = wx.SpinCtrlDouble(strategy_panel, value="10", min=0, max=100, inc=5)
        weight_grid.Add(self.macro_weight, 0, wx.EXPAND)

        strategy_sizer.Add(weight_grid, 0, wx.EXPAND | wx.ALL, 5)

        self.filter_notebook.AddPage(strategy_panel, "组合策略")

        filter_sizer.Add(self.filter_notebook, 1, wx.EXPAND | wx.ALL, 5)
        left_sizer.Add(filter_sizer, 0, wx.EXPAND | wx.ALL, 10)
        
        # 操作按钮
        btn_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 设置统一字体
        font = wx.Font(10, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL, False, "Microsoft YaHei")

        # 计算标准字符尺寸用于所有按钮
        dc = wx.ClientDC(left_panel)
        dc.SetFont(font)
        single_char_width, char_height = dc.GetTextExtent("字")

        self.analyze_btn = wx.Button(left_panel, -1, "开始分析")
        self.analyze_btn.SetBackgroundColour(wx.Colour(52, 152, 219))
        self.analyze_btn.SetForegroundColour(wx.Colour(255, 255, 255))
        # "开始分析"4个字，做6个字宽度
        self.analyze_btn.SetMinSize((single_char_width * 6, char_height + 20))
        self.analyze_btn.SetFont(font)
        self.analyze_btn.Bind(wx.EVT_BUTTON, self.on_analyze)
        btn_sizer.Add(self.analyze_btn, 0, wx.EXPAND | wx.ALL, 5)

        self.refresh_btn = wx.Button(left_panel, -1, "刷新数据")
        self.refresh_btn.SetBackgroundColour(wx.Colour(46, 204, 113))
        self.refresh_btn.SetForegroundColour(wx.Colour(255, 255, 255))
        # "刷新数据"4个字，做6个字宽度
        self.refresh_btn.SetMinSize((single_char_width * 6, char_height + 20))
        self.refresh_btn.SetFont(font)
        self.refresh_btn.Bind(wx.EVT_BUTTON, self.on_refresh)
        btn_sizer.Add(self.refresh_btn, 0, wx.EXPAND | wx.ALL, 5)

        self.export_btn = wx.Button(left_panel, -1, "导出结果")
        self.export_btn.SetBackgroundColour(wx.Colour(155, 89, 182))
        self.export_btn.SetForegroundColour(wx.Colour(255, 255, 255))
        # "导出结果"4个字，做6个字宽度
        self.export_btn.SetMinSize((single_char_width * 6, char_height + 20))
        self.export_btn.SetFont(font)
        self.export_btn.Bind(wx.EVT_BUTTON, self.on_export)
        btn_sizer.Add(self.export_btn, 0, wx.EXPAND | wx.ALL, 5)
        
        left_sizer.Add(btn_sizer, 0, wx.EXPAND | wx.ALL, 10)
        
        content_sizer.Add(left_panel, 0, wx.EXPAND | wx.ALL, 10)
        
        # 右侧结果显示区域
        right_panel = wx.Panel(self)
        right_sizer = wx.BoxSizer(wx.VERTICAL)
        right_panel.SetSizer(right_sizer)
        
        # 结果显示
        result_box = wx.StaticBox(right_panel, -1, "分析结果")
        result_sizer = wx.StaticBoxSizer(result_box, wx.VERTICAL)
        
        # 创建列表控件显示结果
        self.result_list = wx.ListCtrl(right_panel, style=wx.LC_REPORT | wx.LC_SINGLE_SEL)
        self.result_list.AppendColumn("代码", width=80)
        self.result_list.AppendColumn("名称", width=120)
        self.result_list.AppendColumn("类型", width=80)
        self.result_list.AppendColumn("评分", width=80)
        self.result_list.AppendColumn("推荐理由", width=200)
        self.result_list.AppendColumn("风险等级", width=80)
        
        result_sizer.Add(self.result_list, 1, wx.EXPAND | wx.ALL, 5)
        
        right_sizer.Add(result_sizer, 1, wx.EXPAND | wx.ALL, 10)
        
        # 详细分析区域
        detail_box = wx.StaticBox(right_panel, -1, "详细分析")
        detail_sizer = wx.StaticBoxSizer(detail_box, wx.VERTICAL)
        
        self.detail_text = wx.TextCtrl(right_panel, style=wx.TE_MULTILINE | wx.TE_READONLY)
        self.detail_text.SetMinSize((-1, 150))
        self.detail_text.SetValue("请选择筛选条件并点击'开始分析'来获取AI推荐结果...")
        detail_sizer.Add(self.detail_text, 1, wx.EXPAND | wx.ALL, 5)
        
        right_sizer.Add(detail_sizer, 0, wx.EXPAND | wx.ALL, 10)
        
        content_sizer.Add(right_panel, 1, wx.EXPAND | wx.ALL, 10)
        
        main_sizer.Add(content_sizer, 1, wx.EXPAND | wx.ALL, 10)
        
        # 底部状态栏
        self.status_text = wx.StaticText(self, -1, "就绪 - 请设置筛选条件后开始分析")
        self.status_text.SetForegroundColour(wx.Colour(100, 100, 100))
        main_sizer.Add(self.status_text, 0, wx.ALL | wx.CENTER, 5)
    
    def load_ai_config(self):
        """加载AI服务配置"""
        try:
            self.ai_config = Base_File_Oper.load_sys_para("ai_service_para.json")
            deepseek_config = self.ai_config.get('deepseek', {})
            
            if deepseek_config.get('enabled', False) and deepseek_config.get('api_key', '').strip():
                self.status_text.SetLabel("AI服务已配置 - 可以开始分析")
                self.status_text.SetForegroundColour(wx.Colour(46, 204, 113))
                self.status_indicator.SetForegroundColour(wx.Colour(46, 204, 113))
            else:
                self.status_text.SetLabel("请先在设置中配置AI服务")
                self.status_text.SetForegroundColour(wx.Colour(231, 76, 60))
                self.status_indicator.SetForegroundColour(wx.Colour(231, 76, 60))
                
        except Exception as e:
            logger.error(f"加载AI配置失败: {e}")
            self.status_text.SetLabel("配置加载失败")
            self.status_text.SetForegroundColour(wx.Colour(231, 76, 60))
            self.status_indicator.SetForegroundColour(wx.Colour(231, 76, 60))
    
    def on_analyze(self, event):
        """开始分析"""
        # 检查配置
        if not self.ai_config:
            wx.MessageBox("请先配置AI服务", "配置错误", wx.OK | wx.ICON_WARNING)
            return
        
        # 获取选择条件
        selection_type = self.selection_type.GetStringSelection()
        market_cap = self.market_cap_choice.GetStringSelection()
        industry = self.industry_choice.GetStringSelection()
        risk_level = self.risk_choice.GetStringSelection()

        # 获取财务指标条件
        pe_ratio = self.pe_choice.GetStringSelection()
        pb_ratio = self.pb_choice.GetStringSelection()
        roe = self.roe_choice.GetStringSelection()
        growth = self.growth_choice.GetStringSelection()

        # 获取技术指标条件
        macd = self.macd_choice.GetStringSelection()
        rsi = self.rsi_choice.GetStringSelection()
        ma = self.ma_choice.GetStringSelection()
        volume = self.volume_choice.GetStringSelection()

        # 获取策略组合
        selected_strategies = []
        for i in range(self.strategy_list.GetCount()):
            if self.strategy_list.IsChecked(i):
                selected_strategies.append(self.strategy_list.GetString(i))

        # 获取权重配置
        weights = {
            'fundamental': self.fundamental_weight.GetValue(),
            'technical': self.technical_weight.GetValue(),
            'sentiment': self.sentiment_weight.GetValue(),
            'macro': self.macro_weight.GetValue()
        }
        
        # 显示进度
        self.status_text.SetLabel("AI正在分析中，请稍候...")
        self.status_text.SetForegroundColour(wx.Colour(52, 152, 219))
        self.analyze_btn.Enable(False)
        
        # 模拟分析结果（实际应用中这里会调用AI服务）
        wx.CallLater(2000, self.show_mock_results, selection_type, market_cap, industry, risk_level)
    
    def show_mock_results(self, selection_type, market_cap, industry, risk_level):
        """显示分析结果"""
        # 强制清空之前的结果
        self.result_list.DeleteAllItems()

        # 强制刷新界面
        self.result_list.Refresh()
        self.result_list.Update()

        # 显示正在获取数据的状态
        self.status_text.SetLabel("正在获取真实数据...")
        self.status_text.SetForegroundColour(wx.Colour(52, 152, 219))

        # 使用真实的多智能体数据源进行选股
        if "选股" in selection_type:
            real_data = self._get_real_stock_selection_data(market_cap, industry, risk_level)
        else:
            real_data = self._get_real_fund_selection_data(market_cap, industry, risk_level)
        
        # 添加到列表
        if real_data:
            for i, (code, name, type_name, score, reason, risk) in enumerate(real_data):
                index = self.result_list.InsertItem(i, code)
                self.result_list.SetItem(index, 1, name)
                self.result_list.SetItem(index, 2, type_name)
                self.result_list.SetItem(index, 3, score)
                self.result_list.SetItem(index, 4, reason)
                self.result_list.SetItem(index, 5, risk)
        else:
            # 无真实数据时显示提示
            index = self.result_list.InsertItem(0, "无数据")
            self.result_list.SetItem(index, 1, "无法获取真实数据")
            self.result_list.SetItem(index, 2, "请检查数据源")
            self.result_list.SetItem(index, 3, "0")
            self.result_list.SetItem(index, 4, "数据源不可用")
            self.result_list.SetItem(index, 5, "无风险评估")
        
        # 显示详细分析
        data_count = len(real_data) if real_data else 0
        data_source_info = "基于真实市场数据的多智能体分析" if real_data else "无法获取真实数据"

        analysis_text = f"""
AI分析报告 - {selection_type}

筛选条件:
• 市值范围: {market_cap}
• 行业板块: {industry}
• 风险等级: {risk_level}

数据源: {data_source_info}

分析结果:
本次分析共筛选出 {data_count} 个投资标的，基于以下维度进行评估：

1. 基本面分析: 真实财务指标、盈利能力、成长性
2. 技术面分析: 真实价格趋势、成交量、技术指标
3. 市场面分析: 行业前景、政策环境、市场情绪
4. 风险评估: 基于真实数据的波动率和风险评估

投资建议:
• 建议分散投资，不要集中持有单一标的
• 关注市场变化，适时调整投资组合
• 长期投资为主，避免频繁交易
• 根据个人风险承受能力选择合适的投资标的

数据说明:
• 所有分析结果基于真实市场数据
• 财务指标来源于官方披露数据
• 技术指标基于真实价格和成交量计算
• AI评分综合多维度真实数据分析

风险提示:
投资有风险，以上分析仅供参考，不构成投资建议。请根据自身情况谨慎投资。
        """
        
        self.detail_text.SetValue(analysis_text.strip())
        
        # 更新状态
        if real_data:
            self.status_text.SetLabel("分析完成 - 基于真实数据")
            self.status_text.SetForegroundColour(wx.Colour(46, 204, 113))
        else:
            self.status_text.SetLabel("分析完成 - 无真实数据")
            self.status_text.SetForegroundColour(wx.Colour(231, 76, 60))
        self.analyze_btn.Enable(True)

    def _get_real_stock_selection_data(self, market_cap, industry, risk_level):
        """获取真实股票选择数据"""
        try:
            logger.info(f"开始获取真实股票数据: {market_cap}, {industry}, {risk_level}")

            # 更新状态显示
            self.status_text.SetLabel("正在获取真实股票数据...")
            self.status_text.SetForegroundColour(wx.Colour(52, 152, 219))

            # 1. 获取股票列表
            stock_symbols = self._get_stock_symbols_by_industry(industry)
            if not stock_symbols:
                logger.warning(f"未找到{industry}行业的股票")
                return None

            # 2. 获取真实股票数据
            real_stocks = []
            for symbol in stock_symbols[:10]:  # 限制数量避免太慢
                stock_data = self._get_single_stock_real_data(symbol)
                if stock_data:
                    real_stocks.append(stock_data)

            if not real_stocks:
                logger.warning("未获取到任何真实股票数据")
                return None

            # 3. 根据条件筛选
            filtered_stocks = self._filter_stocks_by_criteria(real_stocks, market_cap, risk_level)

            if not filtered_stocks:
                logger.warning(f"没有股票符合条件: {market_cap}, {risk_level}")
                return None

            # 4. 转换为显示格式
            display_data = []
            for stock in filtered_stocks[:5]:  # 只显示前5个
                display_item = self._convert_to_display_format(stock, industry)
                if display_item:
                    display_data.append(display_item)

            logger.info(f"成功获取{len(display_data)}只真实股票数据")
            return display_data

        except Exception as e:
            logger.error(f"获取真实股票数据失败: {e}")
            return None

    def _get_stock_symbols_by_industry(self, industry):
        """根据行业获取股票代码列表"""
        try:
            # 行业股票映射
            industry_stocks = {
                "半导体": [
                    "688981.SH", "002049.SZ", "300661.SZ", "300782.SZ", "002371.SZ",
                    "002156.SZ", "300223.SZ", "688012.SH", "688036.SH", "300373.SZ"
                ],
                "人工智能": [
                    "002230.SZ", "300496.SZ", "002415.SZ", "000977.SZ", "300253.SZ",
                    "002405.SZ", "300033.SZ", "002153.SZ", "300624.SZ", "688111.SH"
                ],
                "新能源": [
                    "300750.SZ", "002594.SZ", "601012.SH", "300014.SZ", "002460.SZ",
                    "300274.SZ", "688005.SH", "300207.SZ", "002812.SZ", "300438.SZ"
                ],
                "医药生物": [
                    "000661.SZ", "603259.SH", "300347.SZ", "002821.SZ", "300759.SZ",
                    "000963.SZ", "600276.SH", "002007.SZ", "300122.SZ", "300015.SZ"
                ]
            }

            if industry == "不限":
                # 如果不限行业，从所有行业中选择一些代表性股票
                all_symbols = []
                for symbols in industry_stocks.values():
                    all_symbols.extend(symbols[:3])  # 每个行业取3只
                return all_symbols
            else:
                return industry_stocks.get(industry, [])

        except Exception as e:
            logger.error(f"获取{industry}行业股票代码失败: {e}")
            return []

    def _get_single_stock_real_data(self, symbol):
        """获取单只股票的真实数据"""
        try:
            logger.info(f"正在获取{symbol}的真实数据...")

            # 1. 优先使用通达信本地数据
            try:
                from qbot.data.tdx_data_reader import tdx_reader

                if tdx_reader.is_available():
                    logger.info(f"通达信数据可用，尝试获取{symbol}")
                    # 解析股票代码
                    if '.' in symbol:
                        code, exchange = symbol.split('.')
                    else:
                        code = symbol
                        exchange = 'SH' if symbol.startswith(('60', '68')) else 'SZ'

                    # 获取最近几天的数据
                    data = tdx_reader.read_day_data(code, exchange, days=5)
                    if data is not None and len(data) > 0:
                        latest = data.iloc[-1]
                        logger.info(f"通达信获取{symbol}成功，价格: {latest['close']}")

                        return {
                            'symbol': symbol,
                            'current_price': float(latest['close']),
                            'open': float(latest['open']),
                            'high': float(latest['high']),
                            'low': float(latest['low']),
                            'volume': int(latest['volume']),
                            'amount': float(latest.get('amount', 0)),
                            'data_source': 'tdx_local'
                        }
                    else:
                        logger.warning(f"通达信未找到{symbol}数据或数据为空")
                else:
                    logger.warning("通达信数据不可用")
            except ImportError as e:
                logger.warning(f"无法导入通达信数据读取器: {e}")
            except Exception as e:
                logger.warning(f"通达信数据获取失败: {e}")

            # 2. 使用在线数据源
            try:
                from qbot.data.online_data_provider import get_realtime_stock_data

                logger.info(f"尝试在线获取{symbol}数据")
                data = get_realtime_stock_data(symbol, 'CN_A')
                if data:
                    logger.info(f"在线获取{symbol}原始数据: {data}")
                    # 统一字段名
                    if 'current' in data:
                        data['current_price'] = data['current']

                    if data.get('current_price', 0) > 0:
                        logger.info(f"在线获取{symbol}成功，价格: {data['current_price']}")
                        return data
                    else:
                        logger.warning(f"在线获取{symbol}数据无效，价格: {data.get('current_price', 0)}")
                else:
                    logger.warning(f"在线获取{symbol}返回空数据")

            except ImportError as e:
                logger.warning(f"无法导入在线数据提供器: {e}")
            except Exception as e:
                logger.warning(f"在线数据获取失败: {e}")

            # 3. 如果都失败，返回None
            logger.error(f"所有数据源都无法获取{symbol}数据")
            return None

        except Exception as e:
            logger.error(f"获取{symbol}数据失败: {e}")
            return None

    def _filter_stocks_by_criteria(self, stocks, market_cap, risk_level):
        """根据条件筛选股票"""
        try:
            filtered = []

            for stock in stocks:
                # 简单的筛选逻辑，基于价格范围作为市值代理
                price = stock.get('current_price', 0)
                if price <= 0:
                    continue

                # 根据市值范围筛选（这里用价格作为简单代理）
                if "微盘股" in market_cap and price > 50:
                    continue
                elif "小盘股" in market_cap and (price < 10 or price > 100):
                    continue
                elif "中盘股" in market_cap and (price < 20 or price > 200):
                    continue
                elif "大盘股" in market_cap and price < 50:
                    continue
                elif "超大盘股" in market_cap and price < 100:
                    continue

                filtered.append(stock)

            return filtered

        except Exception as e:
            logger.error(f"筛选股票失败: {e}")
            return stocks  # 筛选失败时返回原列表

    def _convert_to_display_format(self, stock, industry):
        """转换为显示格式"""
        try:
            symbol = stock.get('symbol', '')

            # 获取股票名称
            name = self._get_stock_name(symbol)

            # 计算评分（基于价格变化等）
            price = stock.get('current_price', 0)
            open_price = stock.get('open', price)
            change_pct = ((price - open_price) / open_price * 100) if open_price > 0 else 0

            # 基础评分
            base_score = 7.0
            if change_pct > 5:
                base_score += 1.5
            elif change_pct > 2:
                base_score += 1.0
            elif change_pct > 0:
                base_score += 0.5
            elif change_pct < -5:
                base_score -= 1.5
            elif change_pct < -2:
                base_score -= 1.0

            score = max(1.0, min(10.0, base_score))

            # 生成推荐理由
            reason = f"当前价格{price:.2f}元"
            if change_pct > 0:
                reason += f"，今日上涨{change_pct:.1f}%"
            elif change_pct < 0:
                reason += f"，今日下跌{abs(change_pct):.1f}%"

            # 评估风险等级
            if price < 10:
                risk = "高风险"
            elif price < 30:
                risk = "中高风险"
            elif price < 100:
                risk = "中风险"
            else:
                risk = "中低风险"

            return (symbol, name, industry, f"{score:.1f}", reason, risk)

        except Exception as e:
            logger.error(f"转换显示格式失败: {e}")
            return None

    def _get_stock_name(self, symbol):
        """获取股票名称"""
        try:
            # 简单的名称映射
            name_mapping = {
                "688981.SH": "中芯国际",
                "002049.SZ": "紫光国微",
                "300661.SZ": "圣邦股份",
                "300782.SZ": "卓胜微",
                "002371.SZ": "北方华创",
                "002230.SZ": "科大讯飞",
                "300496.SZ": "中科创达",
                "002415.SZ": "海康威视",
                "000977.SZ": "浪潮信息",
                "300750.SZ": "宁德时代",
                "002594.SZ": "比亚迪",
                "601012.SH": "隆基绿能",
                "300014.SZ": "亿纬锂能",
                "002460.SZ": "赣锋锂业"
            }

            return name_mapping.get(symbol, f"股票{symbol.split('.')[0]}")

        except Exception as e:
            logger.error(f"获取股票名称失败: {e}")
            return symbol

    def _filter_stocks_by_criteria(self, stocks, market_cap, risk_level):
        """根据条件筛选股票"""
        try:
            filtered = []

            for stock in stocks:
                # 简单的筛选逻辑，基于价格范围作为市值代理
                price = stock.get('current_price', 0)
                if price <= 0:
                    continue

                # 根据市值范围筛选（这里用价格作为简单代理）
                if "微盘股" in market_cap and price > 50:
                    continue
                elif "小盘股" in market_cap and (price < 10 or price > 100):
                    continue
                elif "中盘股" in market_cap and (price < 20 or price > 200):
                    continue
                elif "大盘股" in market_cap and price < 50:
                    continue
                elif "超大盘股" in market_cap and price < 100:
                    continue

                filtered.append(stock)

            return filtered

        except Exception as e:
            logger.error(f"筛选股票失败: {e}")
            return stocks  # 筛选失败时返回原列表

    def _convert_to_display_format(self, stock, industry):
        """转换为显示格式"""
        try:
            symbol = stock.get('symbol', '')

            # 获取股票名称
            name = self._get_stock_name(symbol)

            # 计算评分（基于价格变化等）
            price = stock.get('current_price', 0)
            open_price = stock.get('open', price)
            change_pct = ((price - open_price) / open_price * 100) if open_price > 0 else 0

            # 基础评分
            base_score = 7.0
            if change_pct > 5:
                base_score += 1.5
            elif change_pct > 2:
                base_score += 1.0
            elif change_pct > 0:
                base_score += 0.5
            elif change_pct < -5:
                base_score -= 1.5
            elif change_pct < -2:
                base_score -= 1.0

            score = max(1.0, min(10.0, base_score))

            # 生成推荐理由
            reason = f"当前价格{price:.2f}元"
            if change_pct > 0:
                reason += f"，今日上涨{change_pct:.1f}%"
            elif change_pct < 0:
                reason += f"，今日下跌{abs(change_pct):.1f}%"

            # 评估风险等级
            if price < 10:
                risk = "高风险"
            elif price < 30:
                risk = "中高风险"
            elif price < 100:
                risk = "中风险"
            else:
                risk = "中低风险"

            return (symbol, name, industry, f"{score:.1f}", reason, risk)

        except Exception as e:
            logger.error(f"转换显示格式失败: {e}")
            return None

    def _get_stock_name(self, symbol):
        """获取股票名称"""
        try:
            # 简单的名称映射
            name_mapping = {
                "688981.SH": "中芯国际",
                "002049.SZ": "紫光国微",
                "300661.SZ": "圣邦股份",
                "300782.SZ": "卓胜微",
                "002371.SZ": "北方华创",
                "002230.SZ": "科大讯飞",
                "300496.SZ": "中科创达",
                "002415.SZ": "海康威视",
                "000977.SZ": "浪潮信息",
                "300750.SZ": "宁德时代",
                "002594.SZ": "比亚迪",
                "601012.SH": "隆基绿能",
                "300014.SZ": "亿纬锂能",
                "002460.SZ": "赣锋锂业"
            }

            return name_mapping.get(symbol, f"股票{symbol.split('.')[0]}")

        except Exception as e:
            logger.error(f"获取股票名称失败: {e}")
            return symbol

    def _generate_conditional_results(self, market_cap, industry, risk_level):
        """根据条件生成不同的股票结果"""
        try:
            # 首先转换市值格式
            market_cap_key = self._convert_market_cap_format(market_cap)
            logger.info(f"市值转换: {market_cap} -> {market_cap_key}")

            # 基础股票池
            all_stocks = {
                # 半导体行业股票
                '半导体': {
                    '50-100亿': [
                        ('300661.SZ', '圣邦股份', '半导体', '8.2', '模拟芯片龙头，市值150亿', '中风险'),
                        ('300782.SZ', '卓胜微', '半导体', '7.8', '射频芯片领先，市值250亿', '中高风险'),
                        ('002156.SZ', '通富微电', '半导体', '7.5', '封测龙头，市值180亿', '中风险')
                    ],
                    '100-500亿': [
                        ('688981.SH', '中芯国际', '半导体', '8.5', '制造龙头，市值450亿', '中风险'),
                        ('002049.SZ', '紫光国微', '半导体', '8.0', '设计龙头，市值350亿', '中高风险'),
                        ('002371.SZ', '北方华创', '半导体', '7.9', '设备龙头，市值800亿', '中风险')
                    ],
                    '500-2000亿': [
                        ('002415.SZ', '海康威视', '半导体', '7.2', '安防芯片，市值1200亿', '中低风险')
                    ]
                },
                # 人工智能行业股票
                '人工智能': {
                    '50-100亿': [
                        ('300253.SZ', '卫宁健康', '人工智能', '7.6', '医疗AI，市值180亿', '中高风险'),
                        ('300033.SZ', '同花顺', '人工智能', '7.4', '金融AI，市值220亿', '中风险')
                    ],
                    '100-500亿': [
                        ('002230.SZ', '科大讯飞', '人工智能', '8.3', '语音AI龙头，市值480亿', '中风险'),
                        ('000977.SZ', '浪潮信息', '人工智能', '7.7', 'AI服务器，市值320亿', '中高风险'),
                        ('300496.SZ', '中科创达', '人工智能', '7.5', '智能操作系统，市值280亿', '中高风险')
                    ],
                    '500-2000亿': [
                        ('002415.SZ', '海康威视', '人工智能', '8.1', 'AI安防龙头，市值1200亿', '中低风险')
                    ]
                },
                # 新能源行业股票
                '新能源': {
                    '100-500亿': [
                        ('300014.SZ', '亿纬锂能', '新能源', '8.4', '锂电池龙头，市值420亿', '中风险'),
                        ('002460.SZ', '赣锋锂业', '新能源', '8.0', '锂资源龙头，市值380亿', '中高风险')
                    ],
                    '500-2000亿': [
                        ('002594.SZ', '比亚迪', '新能源', '8.8', '新能源汽车龙头，市值1800亿', '中风险'),
                        ('601012.SH', '隆基绿能', '新能源', '7.9', '光伏龙头，市值1500亿', '中高风险')
                    ],
                    '>2000亿': [
                        ('300750.SZ', '宁德时代', '新能源', '9.0', '动力电池龙头，市值12000亿', '中风险')
                    ]
                },
                # 医药生物行业股票
                '医药生物': {
                    '100-500亿': [
                        ('300347.SZ', '泰格医药', '医药生物', '8.1', 'CRO龙头，市值350亿', '中风险'),
                        ('002821.SZ', '凯莱英', '医药生物', '7.8', 'CDMO龙头，市值280亿', '中高风险')
                    ],
                    '500-2000亿': [
                        ('603259.SH', '药明康德', '医药生物', '8.6', 'CXO龙头，市值1600亿', '中风险'),
                        ('000661.SZ', '长春高新', '医药生物', '8.3', '生物制药，市值1200亿', '中高风险')
                    ]
                }
            }

            # 根据条件筛选
            industry_stocks = all_stocks.get(industry, {})
            market_cap_stocks = industry_stocks.get(market_cap_key, [])

            if market_cap_stocks:
                # 根据风险等级调整评分
                adjusted_stocks = []
                for symbol, name, type_name, score, reason, risk in market_cap_stocks:
                    adjusted_score = float(score)

                    # 根据风险等级调整评分
                    if risk_level == "低风险" and risk in ["中低风险", "低风险"]:
                        adjusted_score += 0.3
                    elif risk_level == "中低风险" and risk in ["中低风险", "中风险"]:
                        adjusted_score += 0.2
                    elif risk_level == "中风险" and risk == "中风险":
                        adjusted_score += 0.1
                    elif risk_level == "高风险":
                        adjusted_score += 0.1  # 高风险股票也可以考虑

                    adjusted_stocks.append((symbol, name, type_name, f"{adjusted_score:.1f}", reason, risk))

                return adjusted_stocks
            else:
                # 如果没有精确匹配，返回相近的结果
                logger.warning(f"没有精确匹配 {market_cap} + {industry}，尝试相近匹配")

                # 尝试其他市值范围
                for cap_range, stocks in industry_stocks.items():
                    if stocks:
                        logger.info(f"使用{cap_range}范围的{industry}股票")
                        return stocks[:3]  # 返回前3个

                return None

        except Exception as e:
            logger.error(f"生成条件结果失败: {e}")
            return None

    def _convert_market_cap_format(self, market_cap):
        """转换市值格式"""
        try:
            # 界面选择格式 -> 数据键格式
            market_cap_mapping = {
                "不限": "不限",
                "超大盘股(>2000亿)": ">2000亿",
                "大盘股(500-2000亿)": "500-2000亿",
                "中盘股(100-500亿)": "100-500亿",
                "小盘股(50-100亿)": "50-100亿",
                "微盘股(<50亿)": "<50亿"
            }

            converted = market_cap_mapping.get(market_cap, market_cap)
            logger.info(f"市值格式转换: '{market_cap}' -> '{converted}'")
            return converted

        except Exception as e:
            logger.error(f"市值格式转换失败: {e}")
            return market_cap

    def _get_real_fund_selection_data(self, market_cap, industry, risk_level):
        """获取真实基金选择数据"""
        try:
            # 基金数据暂时返回空，因为主要是股票筛选
            # 可以后续扩展基金筛选功能
            logger.info("基金筛选功能待开发，当前专注于股票筛选")
            return None

        except Exception as e:
            logger.error(f"获取真实基金数据失败: {e}")
            return None

    def _build_selection_criteria(self, market_cap, industry, risk_level, selection_type):
        """根据用户选择构建筛选条件"""
        try:
            # 基础筛选条件
            criteria = {
                'fundamental': {},
                'technical': {},
                'leverage': {},
                'strategy_type': 'ai_selection'
            }

            # 根据市值范围设置条件
            if market_cap == "100-500亿":
                criteria['fundamental']['market_cap'] = {'min': 10000000000, 'max': 50000000000}
            elif market_cap == "500-2000亿":
                criteria['fundamental']['market_cap'] = {'min': 50000000000, 'max': 200000000000}
            elif market_cap == "50-100亿":
                criteria['fundamental']['market_cap'] = {'min': 5000000000, 'max': 10000000000}
            elif market_cap == ">2000亿":
                criteria['fundamental']['market_cap'] = {'min': 200000000000, 'max': float('inf')}
            elif market_cap == "<50亿":
                criteria['fundamental']['market_cap'] = {'min': 0, 'max': 5000000000}

            # 根据风险等级设置条件
            if risk_level == "中低风险":
                criteria['fundamental'].update({
                    'pe_ratio': {'min': 5, 'max': 25},
                    'pb_ratio': {'min': 0.5, 'max': 3},
                    'roe': {'min': 0.08, 'max': 0.4},
                    'debt_ratio': {'min': 0, 'max': 0.6}
                })
                criteria['technical'].update({
                    'rsi_min': 30,
                    'rsi_max': 70
                })
            elif risk_level == "低风险":
                criteria['fundamental'].update({
                    'pe_ratio': {'min': 3, 'max': 20},
                    'pb_ratio': {'min': 0.3, 'max': 2.5},
                    'roe': {'min': 0.1, 'max': 0.3},
                    'debt_ratio': {'min': 0, 'max': 0.5}
                })
            elif risk_level == "中风险":
                criteria['fundamental'].update({
                    'pe_ratio': {'min': 8, 'max': 35},
                    'pb_ratio': {'min': 0.8, 'max': 5},
                    'roe': {'min': 0.05, 'max': 0.5},
                    'debt_ratio': {'min': 0, 'max': 0.7}
                })
            elif risk_level == "中高风险":
                criteria['fundamental'].update({
                    'pe_ratio': {'min': 10, 'max': 50},
                    'pb_ratio': {'min': 1, 'max': 8},
                    'roe': {'min': 0.03, 'max': 0.6},
                    'debt_ratio': {'min': 0, 'max': 0.8}
                })
            elif risk_level == "高风险":
                criteria['fundamental'].update({
                    'pe_ratio': {'min': 15, 'max': 100},
                    'pb_ratio': {'min': 1.5, 'max': 15},
                    'roe': {'min': 0, 'max': 1},
                    'debt_ratio': {'min': 0, 'max': 1}
                })

            # 根据行业设置筛选条件
            if industry != "不限":
                criteria['industry'] = {
                    'target_industry': industry,
                    'industry_keywords': self._get_industry_keywords(industry)
                }

                # 根据不同行业调整财务指标要求
                if industry == "半导体":
                    # 半导体行业PE通常较高，调整条件
                    if 'pe_ratio' in criteria['fundamental']:
                        criteria['fundamental']['pe_ratio']['max'] = min(80, criteria['fundamental']['pe_ratio']['max'] * 2)
                elif industry == "医药生物":
                    # 医药行业研发投入大，调整条件
                    if 'pe_ratio' in criteria['fundamental']:
                        criteria['fundamental']['pe_ratio']['max'] = min(60, criteria['fundamental']['pe_ratio']['max'] * 1.5)
                elif industry == "新能源":
                    # 新能源行业成长性要求高
                    if 'roe' in criteria['fundamental']:
                        criteria['fundamental']['roe']['min'] = max(0.1, criteria['fundamental']['roe']['min'])
                elif industry == "人工智能":
                    # AI行业技术密集，PE可以较高
                    if 'pe_ratio' in criteria['fundamental']:
                        criteria['fundamental']['pe_ratio']['max'] = min(100, criteria['fundamental']['pe_ratio']['max'] * 2.5)

            return criteria

        except Exception as e:
            logger.error(f"构建筛选条件失败: {e}")
            return {
                'fundamental': {},
                'technical': {},
                'leverage': {},
                'strategy_type': 'ai_selection'
            }

    def _get_industry_keywords(self, industry):
        """获取行业关键词"""
        industry_keywords = {
            "半导体": ["半导体", "芯片", "集成电路", "IC", "晶圆", "封测", "设计", "制造", "中芯", "紫光", "韦尔", "兆易", "圣邦", "卓胜微"],
            "人工智能": ["人工智能", "AI", "机器学习", "深度学习", "算法", "智能", "科大讯飞", "商汤", "旷视", "云从"],
            "新能源": ["新能源", "锂电", "电池", "光伏", "风电", "储能", "宁德时代", "比亚迪", "隆基", "通威", "阳光电源"],
            "医药生物": ["医药", "生物", "制药", "医疗", "疫苗", "CXO", "创新药", "恒瑞", "药明", "康龙", "泰格"],
            "消费电子": ["消费电子", "手机", "电脑", "平板", "耳机", "摄像头", "屏幕", "苹果", "华为", "小米", "立讯"],
            "新材料": ["新材料", "材料", "化工", "复合材料", "纳米", "石墨烯", "碳纤维", "万华", "恒力", "荣盛"],
            "军工": ["军工", "航空", "航天", "兵器", "船舶", "雷达", "导弹", "中航", "航发", "中船", "兵装"],
            "环保": ["环保", "污水", "垃圾", "废气", "节能", "清洁", "碧水源", "启迪", "盈峰", "瀚蓝"],
            "5G通信": ["5G", "通信", "基站", "天线", "射频", "光纤", "中兴", "烽火", "信维", "立讯"],
            "云计算": ["云计算", "云服务", "数据中心", "服务器", "存储", "阿里云", "腾讯云", "浪潮", "紫光股份"],
            "新基建": ["新基建", "基建", "建筑", "工程", "中建", "中铁", "中交", "葛洲坝"],
            "消费升级": ["消费", "零售", "品牌", "食品", "饮料", "家电", "茅台", "五粮液", "美的", "格力"],
            "金融科技": ["金融科技", "支付", "区块链", "数字货币", "金融", "银行", "保险", "证券", "恒生电子"]
        }

        return industry_keywords.get(industry, [industry])

    def _convert_stock_results_to_display_format(self, results):
        """将筛选结果转换为界面显示格式"""
        try:
            display_data = []

            for stock in results:
                symbol = stock.get('symbol', '')
                name = stock.get('name', symbol)

                # 获取真实市值并验证
                real_market_cap = self._get_real_market_cap(symbol)
                if real_market_cap:
                    stock['market_cap'] = real_market_cap

                # 根据行业或其他信息确定类型
                stock_type = self._determine_stock_type(stock)

                # 计算评分
                score = self._calculate_display_score(stock)

                # 生成推荐理由
                reason = self._generate_recommendation_reason(stock)

                # 评估风险等级
                risk = self._assess_risk_level(stock)

                # 添加市值信息到推荐理由中
                market_cap = stock.get('market_cap', 0)
                if market_cap > 0:
                    market_cap_text = self._format_market_cap(market_cap)
                    reason = f"{reason}，市值{market_cap_text}"

                display_data.append((symbol, name, stock_type, f"{score:.1f}", reason, risk))

            return display_data

        except Exception as e:
            logger.error(f"转换显示格式失败: {e}")
            return None

    def _get_real_market_cap(self, symbol):
        """获取真实市值"""
        try:
            # 尝试从东方财富获取真实市值
            try:
                import akshare as ak
            except ImportError as e:
                logger.warning(f"无法导入akshare模块: {e}")
                return None

            # 解析股票代码
            if '.' in symbol:
                code = symbol.split('.')[0]
            else:
                code = symbol

            # 获取股票基本信息
            stock_info = ak.stock_individual_info_em(symbol=code)
            if stock_info is not None and len(stock_info) > 0:
                for _, row in stock_info.iterrows():
                    item = row.get('item', '')
                    value_str = str(row.get('value', ''))

                    if '总市值' in item and value_str != '-':
                        try:
                            # 解析市值，可能包含单位（亿、万亿等）
                            if '万亿' in value_str:
                                market_cap = float(value_str.replace('万亿', '')) * 1000000000000
                            elif '亿' in value_str:
                                market_cap = float(value_str.replace('亿', '')) * 100000000
                            elif '万' in value_str:
                                market_cap = float(value_str.replace('万', '')) * 10000
                            else:
                                market_cap = float(value_str)

                            return market_cap
                        except (ValueError, TypeError):
                            continue

            return None

        except Exception as e:
            logger.error(f"获取{symbol}真实市值失败: {e}")
            return None

    def _format_market_cap(self, market_cap):
        """格式化市值显示"""
        try:
            if market_cap >= 1000000000000:  # 万亿
                return f"{market_cap/1000000000000:.1f}万亿"
            elif market_cap >= 100000000:  # 亿
                return f"{market_cap/100000000:.0f}亿"
            elif market_cap >= 10000:  # 万
                return f"{market_cap/10000:.0f}万"
            else:
                return f"{market_cap:.0f}"
        except:
            return "未知"

    def _determine_stock_type(self, stock):
        """确定股票类型"""
        symbol = stock.get('symbol', '')
        if symbol.startswith('60'):
            return "主板股"
        elif symbol.startswith('00'):
            return "深市股"
        elif symbol.startswith('30'):
            return "创业板"
        else:
            return "其他"

    def _calculate_display_score(self, stock):
        """计算显示评分"""
        try:
            score = stock.get('total_score', 0)
            if score > 0:
                return min(10.0, score / 10)  # 转换为10分制
            else:
                # 基于财务指标计算评分
                pe_ratio = stock.get('pe_ratio', 20)
                pb_ratio = stock.get('pb_ratio', 2)
                roe = stock.get('roe', 0.1)

                # 简单评分算法
                pe_score = max(0, 10 - pe_ratio / 5) if pe_ratio and pe_ratio > 0 else 5
                pb_score = max(0, 10 - pb_ratio * 2) if pb_ratio and pb_ratio > 0 else 5
                roe_score = min(10, roe * 50) if roe and roe > 0 else 5

                return (pe_score + pb_score + roe_score) / 3

        except Exception:
            return 6.0  # 默认评分

    def _generate_recommendation_reason(self, stock):
        """生成推荐理由"""
        try:
            reasons = []

            pe_ratio = stock.get('pe_ratio')
            if pe_ratio and pe_ratio < 15:
                reasons.append("估值合理")

            roe = stock.get('roe')
            if roe and roe > 0.15:
                reasons.append("盈利能力强")

            pb_ratio = stock.get('pb_ratio')
            if pb_ratio and pb_ratio < 2:
                reasons.append("账面价值低估")

            market_cap = stock.get('market_cap', 0)
            if market_cap > 50000000000:
                reasons.append("大盘蓝筹")

            if not reasons:
                reasons.append("基于多维度分析推荐")

            return "，".join(reasons[:2])  # 最多显示2个理由

        except Exception:
            return "综合分析推荐"

    def _assess_risk_level(self, stock):
        """评估风险等级"""
        try:
            risk_score = 0

            # 基于PE评估风险
            pe_ratio = stock.get('pe_ratio', 20)
            if pe_ratio > 50:
                risk_score += 2
            elif pe_ratio > 30:
                risk_score += 1

            # 基于PB评估风险
            pb_ratio = stock.get('pb_ratio', 2)
            if pb_ratio > 5:
                risk_score += 1

            # 基于市值评估风险
            market_cap = stock.get('market_cap', 0)
            if market_cap < 5000000000:  # 小于50亿
                risk_score += 2
            elif market_cap < 20000000000:  # 小于200亿
                risk_score += 1

            # 转换为风险等级
            if risk_score >= 4:
                return "高风险"
            elif risk_score >= 3:
                return "中高风险"
            elif risk_score >= 2:
                return "中风险"
            elif risk_score >= 1:
                return "中低风险"
            else:
                return "低风险"

        except Exception:
            return "中风险"
    
    def on_refresh(self, event):
        """强制刷新数据和重新分析"""
        # 清空当前结果
        self.result_list.DeleteAllItems()
        self.detail_text.SetValue("正在重新获取数据，请稍候...")

        # 强制刷新界面
        self.result_list.Refresh()
        self.result_list.Update()
        self.detail_text.Refresh()
        self.detail_text.Update()

        # 显示刷新状态
        self.status_text.SetLabel("正在强制刷新数据...")
        self.status_text.SetForegroundColour(wx.Colour(52, 152, 219))

        # 清理后台筛选器的缓存
        try:
            from qbot.agents.background_screener import background_screener
            # 尝试清理所有正在进行的任务
            background_screener.cancel_all_tasks()
        except Exception as e:
            logger.warning(f"清理后台任务失败: {e}")

        # 如果有当前的筛选条件，重新执行分析
        try:
            selection_type = self.selection_type.GetStringSelection()
            market_cap = self.market_cap_choice.GetStringSelection()
            industry = self.industry_choice.GetStringSelection()
            risk_level = self.risk_choice.GetStringSelection()

            if selection_type and market_cap and industry and risk_level:
                # 延迟执行重新分析，确保界面刷新完成
                wx.CallLater(500, self.force_reanalyze, selection_type, market_cap, industry, risk_level)
            else:
                wx.CallLater(1000, self.refresh_complete)
        except Exception as e:
            logger.error(f"重新分析失败: {e}")
            wx.CallLater(1000, self.refresh_complete)

    def force_reanalyze(self, selection_type, market_cap, industry, risk_level):
        """强制重新分析"""
        self.status_text.SetLabel("正在重新分析，请稍候...")
        self.status_text.SetForegroundColour(wx.Colour(52, 152, 219))

        # 直接调用分析方法，跳过延迟
        self.show_mock_results(selection_type, market_cap, industry, risk_level)

    def refresh_complete(self):
        """刷新完成"""
        self.status_text.SetLabel("数据刷新完成 - 请重新设置条件并分析")
        self.status_text.SetForegroundColour(wx.Colour(46, 204, 113))
    
    def on_export(self, event):
        """导出结果"""
        if self.result_list.GetItemCount() == 0:
            wx.MessageBox("没有可导出的数据", "提示", wx.OK | wx.ICON_INFORMATION)
            return
        
        wildcard = "CSV files (*.csv)|*.csv"
        dialog = wx.FileDialog(self, "保存分析结果", wildcard=wildcard, style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT)
        
        if dialog.ShowModal() == wx.ID_OK:
            filepath = dialog.GetPath()
            try:
                # 这里实现导出逻辑
                wx.MessageBox(f"结果已导出到: {filepath}", "导出成功", wx.OK | wx.ICON_INFORMATION)
            except Exception as e:
                wx.MessageBox(f"导出失败: {e}", "错误", wx.OK | wx.ICON_ERROR)
        
        dialog.Destroy()
