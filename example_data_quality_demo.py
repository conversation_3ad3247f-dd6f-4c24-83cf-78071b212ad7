#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据质量管理演示
展示数据清洗、标准化、验证和质量评估的完整流程
"""

import asyncio
import json
from typing import Dict, List, Any
from qbot.config.constants import MarketType
from qbot.data.data_quality_manager import (
    DataQualityManager, DataNormalizer, DataCleaner, 
    BasicDataValidator, data_quality_manager
)
from qbot.common.enhanced_logger import get_logger


class DataQualityDemo:
    """数据质量演示"""
    
    def __init__(self):
        self.logger = get_logger("data_quality_demo")
        self.quality_manager = DataQualityManager()
    
    def generate_problematic_data_samples(self) -> List[Dict[str, Any]]:
        """生成有问题的数据样本"""
        return [
            # 1. 新浪财经格式 - 有缺失值和异常值
            {
                'source': 'sina',
                'data': {
                    'code': '000001',
                    'name': '  平安银行  ',  # 有空白字符
                    'price': '12.34',
                    'open': '',  # 缺失值
                    'high': '12.50',
                    'low': '12.20',
                    'prev_close': '12.30',
                    'vol': '1234567',  # 可能是手为单位
                    'amount': '15234567890',
                    'pe_ratio': '-5.67',  # 负PE
                    'pb_ratio': '150.0',  # 异常高PB
                    'roe': '250%',  # 百分比格式
                    'market_cap': '1234.56'  # 可能是亿为单位
                }
            },
            
            # 2. 腾讯财经格式 - 字段名不同，单位不统一
            {
                'source': 'tencent',
                'data': {
                    'code': 'sz000002',  # 带市场前缀
                    'name': '万科A',
                    'cur_pri': 25.67,
                    'open_pri': None,  # 空值
                    'high_pri': 26.00,
                    'low_pri': 25.50,
                    'pre_close': 25.60,
                    'volume': 8765432,
                    'turnover': 2234567890,
                    'pe_ratio': 8.5,
                    'pb_ratio': 1.2,
                    'roe': 0.15,
                    'debt_ratio': 0.45,
                    'market_cap': 2876.5  # 亿元
                }
            },
            
            # 3. 东方财富格式 - 字段编码，数据类型混乱
            {
                'source': 'eastmoney',
                'data': {
                    'f12': '600036',
                    'f14': '招商银行',
                    'f2': '45.67',
                    'f17': 45.20,
                    'f15': '46.00',
                    'f16': 45.10,
                    'f18': 45.50,
                    'f5': '3456789',
                    'f6': '15678901234',
                    'pe_ratio': 'N/A',  # 非数值
                    'pb_ratio': '0.95',
                    'roe': '18.5%',
                    'volatility': '0.25',
                    'rsi': 65.5
                }
            },
            
            # 4. Yahoo Finance格式 - 英文字段名
            {
                'source': 'yahoo',
                'data': {
                    'symbol': 'AAPL',
                    'regularMarketPrice': 150.25,
                    'regularMarketOpen': 149.80,
                    'regularMarketDayHigh': 151.00,
                    'regularMarketDayLow': 149.50,
                    'regularMarketPreviousClose': 149.90,
                    'regularMarketVolume': 45678901,
                    'marketCap': 2456789012345,  # 美元
                    'trailingPE': 28.5,
                    'priceToBook': 8.2,
                    'returnOnEquity': 0.875,  # 87.5%
                    'debtToEquity': 1.73
                }
            },
            
            # 5. 极端异常数据
            {
                'source': 'unknown',
                'data': {
                    'symbol': '  test@123  ',  # 包含特殊字符
                    'name': '',  # 空名称
                    'current_price': -10.5,  # 负价格
                    'volume': 'abc',  # 非数值
                    'market_cap': 999999999999999,  # 异常大市值
                    'pe_ratio': 99999,  # 异常高PE
                    'pb_ratio': -5.0,  # 负PB
                    'roe': 50.0,  # 5000% ROE
                    'debt_ratio': 15.0,  # 1500%负债率
                    'rsi': 150,  # 超出范围RSI
                    'timestamp': '1640995200000'  # 毫秒时间戳
                }
            }
        ]
    
    def demonstrate_data_normalization(self):
        """演示数据标准化"""
        print("🔄 数据标准化演示")
        print("=" * 60)
        
        normalizer = DataNormalizer()
        samples = self.generate_problematic_data_samples()
        
        for i, sample in enumerate(samples[:3], 1):  # 演示前3个样本
            source = sample['source']
            raw_data = sample['data']
            
            print(f"\n📊 样本 {i} - 数据源: {source}")
            print("原始数据:")
            for key, value in raw_data.items():
                print(f"  {key}: {value} ({type(value).__name__})")
            
            # 标准化
            normalized = normalizer.normalize(raw_data, source, MarketType.CN_A)
            
            print("\n标准化后:")
            for key, value in normalized.items():
                if key in raw_data or key in ['source', 'market', 'normalized_at']:
                    print(f"  {key}: {value} ({type(value).__name__})")
    
    def demonstrate_data_cleaning(self):
        """演示数据清洗"""
        print("\n🧹 数据清洗演示")
        print("=" * 60)
        
        cleaner = DataCleaner()
        
        # 使用有问题的数据
        dirty_data = {
            'symbol': '  000001  ',
            'name': '',
            'current_price': '12.34',
            'volume': '',
            'market_cap': 'N/A',
            'pe_ratio': '-5.67',
            'pb_ratio': '150.0',
            'roe': '250%',
            'debt_ratio': None,
            'rsi': '105',
            'ma_trend': '  上升  '
        }
        
        print("清洗前的脏数据:")
        for key, value in dirty_data.items():
            print(f"  {key}: '{value}' ({type(value).__name__})")
        
        # 清洗数据
        cleaned = cleaner.clean(dirty_data, '000001')
        
        print("\n清洗后的数据:")
        for key, value in cleaned.items():
            print(f"  {key}: {value} ({type(value).__name__})")
    
    def demonstrate_data_validation(self):
        """演示数据验证"""
        print("\n✅ 数据验证演示")
        print("=" * 60)
        
        validator = BasicDataValidator()
        
        # 测试数据
        test_cases = [
            {
                'name': '正常数据',
                'data': {
                    'symbol': '000001',
                    'name': '平安银行',
                    'current_price': 12.34,
                    'volume': 1234567,
                    'market_cap': 2.5e11,
                    'pe_ratio': 8.5,
                    'pb_ratio': 1.2,
                    'roe': 0.15,
                    'debt_ratio': 0.45,
                    'rsi': 65.5
                }
            },
            {
                'name': '有问题的数据',
                'data': {
                    'symbol': '000002',
                    # 缺少 current_price
                    'volume': -1000,  # 负成交量
                    'pe_ratio': -5.0,  # 负PE
                    'pb_ratio': 200.0,  # 异常高PB
                    'roe': 5.0,  # 500% ROE
                    'rsi': 150  # 超出范围
                }
            }
        ]
        
        for test_case in test_cases:
            name = test_case['name']
            data = test_case['data']
            
            print(f"\n📋 测试案例: {name}")
            
            # 验证数据
            issues = validator.validate(data, data.get('symbol', 'UNKNOWN'))
            
            if issues:
                print(f"发现 {len(issues)} 个质量问题:")
                for issue in issues:
                    print(f"  🔸 {issue.severity.upper()}: {issue.description}")
                    if issue.auto_fixable:
                        print(f"    建议值: {issue.suggested_value}")
            else:
                print("✅ 数据质量良好，无问题发现")
    
    async def demonstrate_complete_quality_process(self):
        """演示完整的质量管理流程"""
        print("\n🔄 完整质量管理流程演示")
        print("=" * 60)
        
        samples = self.generate_problematic_data_samples()
        
        for i, sample in enumerate(samples, 1):
            source = sample['source']
            raw_data = sample['data']
            symbol = raw_data.get('code') or raw_data.get('symbol') or raw_data.get('f12') or f'TEST{i:03d}'
            
            print(f"\n📊 处理样本 {i}: {symbol} (来源: {source})")
            
            # 使用质量管理器处理数据
            result = self.quality_manager.process_stock_data(
                raw_data, source, MarketType.CN_A, symbol
            )
            
            if result.is_success():
                processed_data = result.data
                quality_report = result.metadata.get('quality_report')
                
                print(f"✅ 处理成功")
                print(f"  质量评分: {quality_report.overall_score:.3f}")
                print(f"  问题数量: {len(quality_report.issues)}")
                print(f"  数据可用: {'是' if quality_report.is_usable else '否'}")
                
                # 显示关键字段
                key_fields = ['symbol', 'current_price', 'volume', 'pe_ratio', 'quality_score']
                print("  关键字段:")
                for field in key_fields:
                    if field in processed_data:
                        print(f"    {field}: {processed_data[field]}")
                
                # 显示质量问题
                if quality_report.issues:
                    print("  质量问题:")
                    for issue in quality_report.issues[:3]:  # 显示前3个问题
                        print(f"    🔸 {issue.severity}: {issue.description}")
            else:
                print(f"❌ 处理失败: {result.get_error_message()}")
    
    def demonstrate_quality_statistics(self):
        """演示质量统计"""
        print("\n📊 数据质量统计演示")
        print("=" * 60)
        
        # 获取质量摘要
        summary = self.quality_manager.get_quality_summary()
        
        print("整体质量统计:")
        print(f"  处理总数: {summary.get('total_reports', 0)}")
        print(f"  平均质量评分: {summary.get('avg_quality_score', 0):.3f}")
        print(f"  数据可用率: {summary.get('usable_rate', 0):.1%}")
        print(f"  平均问题数: {summary.get('avg_issues_count', 0):.1f}")
        
        # 数据源质量统计
        source_stats = self.quality_manager.get_source_quality_stats()
        if source_stats:
            print("\n各数据源质量统计:")
            for source, stats in source_stats.items():
                print(f"  {source}:")
                print(f"    处理数量: {stats['total_count']}")
                print(f"    平均质量: {stats['avg_quality_score']:.3f}")
                print(f"    可用率: {stats['usable_rate']:.1%}")
                print(f"    平均问题数: {stats['avg_issues_count']:.1f}")
    
    def demonstrate_quality_improvement_suggestions(self):
        """演示质量改进建议"""
        print("\n💡 数据质量改进建议")
        print("=" * 60)
        
        source_stats = self.quality_manager.get_source_quality_stats()
        
        print("基于质量统计的改进建议:")
        
        for source, stats in source_stats.items():
            quality_score = stats['avg_quality_score']
            usable_rate = stats['usable_rate']
            issues_count = stats['avg_issues_count']
            
            print(f"\n📊 数据源: {source}")
            
            if quality_score < 0.7:
                print("  🔴 质量评分较低，建议:")
                print("    - 检查数据源API的稳定性")
                print("    - 增加数据验证规则")
                print("    - 考虑更换更可靠的数据源")
            
            if usable_rate < 0.8:
                print("  🟡 数据可用率偏低，建议:")
                print("    - 增强数据清洗逻辑")
                print("    - 添加更多默认值处理")
                print("    - 实现数据修复机制")
            
            if issues_count > 3:
                print("  🟠 数据问题较多，建议:")
                print("    - 优化数据标准化流程")
                print("    - 加强数据源监控")
                print("    - 建立数据质量预警机制")
            
            if quality_score >= 0.8 and usable_rate >= 0.9:
                print("  ✅ 数据质量良好，继续保持")


async def main():
    """主演示函数"""
    print("🔍 数据质量管理系统演示")
    print("=" * 80)
    
    demo = DataQualityDemo()
    
    # 1. 数据标准化演示
    demo.demonstrate_data_normalization()
    
    # 2. 数据清洗演示
    demo.demonstrate_data_cleaning()
    
    # 3. 数据验证演示
    demo.demonstrate_data_validation()
    
    # 4. 完整质量管理流程演示
    await demo.demonstrate_complete_quality_process()
    
    # 5. 质量统计演示
    demo.demonstrate_quality_statistics()
    
    # 6. 改进建议演示
    demo.demonstrate_quality_improvement_suggestions()
    
    print("\n" + "=" * 80)
    print("🎉 数据质量管理演示完成!")
    print("\n💡 数据质量管理的关键价值:")
    print("  1. ✅ 统一数据格式 - 消除不同数据源的差异")
    print("  2. ✅ 自动数据清洗 - 处理缺失值和异常值")
    print("  3. ✅ 质量验证 - 确保数据的准确性和完整性")
    print("  4. ✅ 质量评分 - 量化数据质量水平")
    print("  5. ✅ 问题追踪 - 识别和记录数据质量问题")
    print("  6. ✅ 源头监控 - 监控各数据源的质量表现")
    print("  7. ✅ 自动修复 - 对可修复的问题进行自动处理")


if __name__ == "__main__":
    asyncio.run(main())
