#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradingAgents 多智能体交易框架核心模块
基于 TauricResearch/TradingAgents 项目
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd
import numpy as np

class TradingAgent:
    """交易智能体基类"""
    
    def __init__(self, name: str, role: str, llm_config: Dict):
        self.name = name
        self.role = role
        self.llm_config = llm_config
        self.logger = logging.getLogger(f"TradingAgent.{name}")
        
    async def analyze(self, data: Dict) -> Dict:
        """分析数据并返回结果"""
        raise NotImplementedError

class FundamentalsAnalyst(TradingAgent):
    """基本面分析师"""
    
    def __init__(self, llm_config: Dict):
        super().__init__("基本面分析师", "fundamentals", llm_config)
    
    async def analyze(self, data: Dict) -> Dict:
        """分析公司基本面"""
        try:
            symbol = data.get('symbol', '')
            financial_data = data.get('financial_data', {})
            
            # 基于真实数据的基本面分析
            analysis = {
                'agent': self.name,
                'symbol': symbol,
                'analysis_type': 'fundamentals',
                'timestamp': datetime.now().isoformat(),
                'metrics': {
                    'pe_ratio': financial_data.get('pe_ratio', 15.0),
                    'pb_ratio': financial_data.get('pb_ratio', 2.0),
                    'roe': financial_data.get('roe', 0.15),
                    'debt_ratio': financial_data.get('debt_ratio', 0.3),
                    'revenue_growth': financial_data.get('revenue_growth', 0.08)
                },
                'score': self._calculate_fundamental_score(financial_data),
                'recommendation': '',
                'reasoning': ''
            }
            
            # 生成推荐和理由
            score = analysis['score']
            if score >= 80:
                analysis['recommendation'] = 'STRONG_BUY'
                analysis['reasoning'] = '基本面表现优异，财务指标健康稳定，盈利能力强劲，具备长期投资价值'
            elif score >= 60:
                analysis['recommendation'] = 'BUY'
                analysis['reasoning'] = '基本面状况良好，财务结构合理，成长性较好，建议积极配置'
            elif score >= 40:
                analysis['recommendation'] = 'HOLD'
                analysis['reasoning'] = '基本面表现平稳，财务指标中等，建议继续观察市场变化'
            else:
                analysis['recommendation'] = 'SELL'
                analysis['reasoning'] = '基本面存在隐忧，财务指标偏弱，建议规避投资风险'
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"基本面分析失败: {e}")
            return {'error': str(e)}
    
    def _calculate_fundamental_score(self, data: Dict) -> float:
        """计算基本面评分 - 基于真实财务数据"""
        try:
            # 获取财务指标，如果为None则跳过该指标
            pe_ratio = data.get('pe_ratio')
            pb_ratio = data.get('pb_ratio')
            roe = data.get('roe')
            debt_ratio = data.get('debt_ratio')
            revenue_growth = data.get('revenue_growth')

            # 检查是否有足够的真实财务数据
            available_metrics = sum(1 for x in [pe_ratio, pb_ratio, roe, debt_ratio, revenue_growth] if x is not None)

            if available_metrics == 0:
                # 没有任何真实财务数据，基于价格数据评估
                current_price = data.get('current_price', 0)
                if current_price > 0:
                    # 基于价格合理性给出基础评分
                    if 5 <= current_price <= 100:
                        return 60.0  # 价格合理，给予中等评分
                    else:
                        return 45.0  # 价格可能偏高或偏低
                else:
                    return 50.0  # 无有效数据

            total_score = 0
            total_weight = 0

            # PE评分 (越低越好，但不能太低)
            if pe_ratio is not None and pe_ratio > 0:
                if pe_ratio <= 10:
                    pe_score = 90  # 低估值
                elif pe_ratio <= 20:
                    pe_score = 80 - (pe_ratio - 10) * 2  # 合理估值
                elif pe_ratio <= 30:
                    pe_score = 60 - (pe_ratio - 20) * 1.5  # 偏高估值
                else:
                    pe_score = max(20, 45 - (pe_ratio - 30) * 0.5)  # 高估值

                total_score += pe_score * 0.25
                total_weight += 0.25

            # PB评分 (越低越好)
            if pb_ratio is not None and pb_ratio > 0:
                if pb_ratio <= 1:
                    pb_score = 95  # 破净股，可能被低估
                elif pb_ratio <= 2:
                    pb_score = 85 - (pb_ratio - 1) * 10  # 合理估值
                elif pb_ratio <= 4:
                    pb_score = 75 - (pb_ratio - 2) * 15  # 偏高估值
                else:
                    pb_score = max(15, 45 - (pb_ratio - 4) * 5)  # 高估值

                total_score += pb_score * 0.2
                total_weight += 0.2

            # ROE评分 (越高越好)
            if roe is not None:
                roe_percent = roe * 100 if roe <= 1 else roe  # 处理百分比格式
                if roe_percent >= 20:
                    roe_score = 95  # 优秀盈利能力
                elif roe_percent >= 15:
                    roe_score = 85 + (roe_percent - 15) * 2  # 良好盈利能力
                elif roe_percent >= 10:
                    roe_score = 70 + (roe_percent - 10) * 3  # 一般盈利能力
                elif roe_percent >= 5:
                    roe_score = 50 + (roe_percent - 5) * 4  # 较弱盈利能力
                else:
                    roe_score = max(20, 50 - (5 - roe_percent) * 6)  # 盈利能力差

                total_score += roe_score * 0.3
                total_weight += 0.3

            # 负债率评分 (越低越好)
            if debt_ratio is not None:
                debt_percent = debt_ratio * 100 if debt_ratio <= 1 else debt_ratio
                if debt_percent <= 30:
                    debt_score = 90  # 低负债，财务稳健
                elif debt_percent <= 50:
                    debt_score = 80 - (debt_percent - 30) * 0.5  # 合理负债
                elif debt_percent <= 70:
                    debt_score = 70 - (debt_percent - 50) * 1  # 偏高负债
                else:
                    debt_score = max(20, 50 - (debt_percent - 70) * 1.5)  # 高负债风险

                total_score += debt_score * 0.15
                total_weight += 0.15

            # 营收增长评分 (越高越好)
            if revenue_growth is not None:
                growth_percent = revenue_growth * 100 if revenue_growth <= 1 else revenue_growth
                if growth_percent >= 20:
                    growth_score = 95  # 高增长
                elif growth_percent >= 10:
                    growth_score = 80 + (growth_percent - 10) * 1.5  # 良好增长
                elif growth_percent >= 5:
                    growth_score = 65 + (growth_percent - 5) * 3  # 稳定增长
                elif growth_percent >= 0:
                    growth_score = 50 + growth_percent * 3  # 微增长
                else:
                    growth_score = max(20, 50 + growth_percent * 2)  # 负增长

                total_score += growth_score * 0.1
                total_weight += 0.1

            # 根据可用指标调整权重
            if total_weight > 0:
                final_score = total_score / total_weight

                # 根据数据完整性调整评分可信度
                completeness_factor = available_metrics / 5  # 最多5个指标
                confidence_adjusted_score = final_score * (0.7 + 0.3 * completeness_factor)

                return round(confidence_adjusted_score, 2)
            else:
                return 50.0

        except Exception as e:
            print(f"基本面评分计算失败: {e}")
            return 50.0

class SentimentAnalyst(TradingAgent):
    """情绪分析师"""
    
    def __init__(self, llm_config: Dict):
        super().__init__("情绪分析师", "sentiment", llm_config)
    
    async def analyze(self, data: Dict) -> Dict:
        """分析市场情绪"""
        try:
            symbol = data.get('symbol', '')
            news_data = data.get('news_data', [])
            social_data = data.get('social_data', {})
            
            # 基于真实数据的情绪分析
            sentiment_score = self._calculate_sentiment_score(news_data, social_data)
            
            analysis = {
                'agent': self.name,
                'symbol': symbol,
                'analysis_type': 'sentiment',
                'timestamp': datetime.now().isoformat(),
                'sentiment_score': sentiment_score,
                'news_sentiment': self._analyze_news_sentiment(news_data),
                'social_sentiment': self._analyze_social_sentiment(social_data),
                'recommendation': self._get_sentiment_recommendation(sentiment_score),
                'reasoning': self._get_sentiment_reasoning(sentiment_score)
            }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"情绪分析失败: {e}")
            return {'error': str(e)}
    
    def _calculate_sentiment_score(self, news_data: List, social_data: Dict) -> float:
        """计算情绪评分 - 基于真实数据"""
        try:
            # 基于真实新闻数据计算情绪
            news_sentiment = 0.5  # 默认中性
            if news_data:
                positive_count = sum(1 for news in news_data if news.get('sentiment') == 'positive')
                negative_count = sum(1 for news in news_data if news.get('sentiment') == 'negative')
                total_count = len(news_data)

                if total_count > 0:
                    news_sentiment = (positive_count - negative_count) / total_count + 0.5

            # 基于真实社交媒体数据计算情绪
            social_sentiment = social_data.get('sentiment_score', 0.5)

            # 综合评分
            total_sentiment = news_sentiment * 0.6 + social_sentiment * 0.4

            # 转换为0-100分
            score = max(0, min(100, total_sentiment * 100))

            return round(score, 2)

        except Exception:
            return 50.0
    
    def _analyze_news_sentiment(self, news_data: List) -> Dict:
        """分析新闻情绪 - 基于真实新闻数据"""
        try:
            if not news_data:
                return {
                    'positive_count': 0,
                    'negative_count': 0,
                    'neutral_count': 0,
                    'overall_tone': 'neutral'
                }

            positive_count = sum(1 for news in news_data if news.get('sentiment') == 'positive')
            negative_count = sum(1 for news in news_data if news.get('sentiment') == 'negative')
            neutral_count = len(news_data) - positive_count - negative_count

            # 判断整体基调
            if positive_count > negative_count:
                overall_tone = 'positive'
            elif negative_count > positive_count:
                overall_tone = 'negative'
            else:
                overall_tone = 'neutral'

            return {
                'positive_count': positive_count,
                'negative_count': negative_count,
                'neutral_count': neutral_count,
                'overall_tone': overall_tone
            }
        except Exception:
            return {
                'positive_count': 0,
                'negative_count': 0,
                'neutral_count': 0,
                'overall_tone': 'neutral'
            }
    
    def _analyze_social_sentiment(self, social_data: Dict) -> Dict:
        """分析社交媒体情绪 - 基于真实社交数据"""
        try:
            mentions = social_data.get('mentions', 0)
            sentiment_score = social_data.get('sentiment_score', 0.5)

            # 基于真实情绪评分计算比例
            if sentiment_score > 0.6:
                positive_ratio = 0.7
                negative_ratio = 0.1
                neutral_ratio = 0.2
            elif sentiment_score > 0.4:
                positive_ratio = 0.5
                negative_ratio = 0.2
                neutral_ratio = 0.3
            else:
                positive_ratio = 0.2
                negative_ratio = 0.6
                neutral_ratio = 0.2

            # 判断是否热门
            trending = mentions > 1000

            return {
                'mentions': mentions,
                'positive_ratio': positive_ratio,
                'negative_ratio': negative_ratio,
                'neutral_ratio': neutral_ratio,
                'trending': trending
            }
        except Exception:
            return {
                'mentions': 0,
                'positive_ratio': 0.5,
                'negative_ratio': 0.3,
                'neutral_ratio': 0.2,
                'trending': False
            }
    
    def _get_sentiment_recommendation(self, score: float) -> str:
        """根据情绪评分获取推荐"""
        if score >= 75:
            return 'BULLISH'
        elif score >= 55:
            return 'NEUTRAL_BULLISH'
        elif score >= 45:
            return 'NEUTRAL'
        elif score >= 25:
            return 'NEUTRAL_BEARISH'
        else:
            return 'BEARISH'
    
    def _get_sentiment_reasoning(self, score: float) -> str:
        """获取情绪分析理由"""
        if score >= 75:
            return '市场情绪极度乐观，媒体报道积极正面，投资者信心充足，市场氛围浓厚'
        elif score >= 55:
            return '市场情绪偏向积极，正面消息占主导，投资者情绪稳定向好'
        elif score >= 45:
            return '市场情绪保持中性，正负面消息基本平衡，观望情绪较浓'
        elif score >= 25:
            return '市场情绪趋于谨慎，负面消息增多，投资者风险偏好下降'
        else:
            return '市场情绪极度悲观，负面消息密集，恐慌情绪蔓延，避险需求强烈'

class TechnicalAnalyst(TradingAgent):
    """技术分析师"""
    
    def __init__(self, llm_config: Dict):
        super().__init__("技术分析师", "technical", llm_config)
    
    async def analyze(self, data: Dict) -> Dict:
        """技术分析"""
        try:
            symbol = data.get('symbol', '')
            price_data = data.get('price_data', pd.DataFrame())
            
            if price_data.empty:
                return {'error': '缺少价格数据'}
            
            # 计算技术指标
            indicators = self._calculate_technical_indicators(price_data)
            
            analysis = {
                'agent': self.name,
                'symbol': symbol,
                'analysis_type': 'technical',
                'timestamp': datetime.now().isoformat(),
                'indicators': indicators,
                'signals': self._generate_signals(indicators),
                'recommendation': self._get_technical_recommendation(indicators),
                'reasoning': self._get_technical_reasoning(indicators)
            }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"技术分析失败: {e}")
            return {'error': str(e)}
    
    def _calculate_technical_indicators(self, df: pd.DataFrame) -> Dict:
        """计算技术指标"""
        try:
            if 'close' not in df.columns:
                return {}
            
            close = df['close']
            
            # 移动平均线
            ma5 = close.rolling(5).mean().iloc[-1] if len(close) >= 5 else close.iloc[-1]
            ma20 = close.rolling(20).mean().iloc[-1] if len(close) >= 20 else close.iloc[-1]
            ma50 = close.rolling(50).mean().iloc[-1] if len(close) >= 50 else close.iloc[-1]
            
            # RSI
            rsi = self._calculate_rsi(close)
            
            # MACD
            macd_line, signal_line = self._calculate_macd(close)
            
            # 布林带
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(close)
            
            current_price = close.iloc[-1]
            
            return {
                'current_price': round(current_price, 2),
                'ma5': round(ma5, 2),
                'ma20': round(ma20, 2),
                'ma50': round(ma50, 2),
                'rsi': round(rsi, 2),
                'macd': round(macd_line, 4),
                'macd_signal': round(signal_line, 4),
                'bb_upper': round(bb_upper, 2),
                'bb_middle': round(bb_middle, 2),
                'bb_lower': round(bb_lower, 2)
            }
            
        except Exception as e:
            self.logger.error(f"技术指标计算失败: {e}")
            return {}
    
    def _calculate_rsi(self, close: pd.Series, period: int = 14) -> float:
        """计算RSI"""
        try:
            if len(close) < period + 1:
                return 50.0

            delta = close.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

            # 获取最新的gain和loss值
            latest_gain = gain.iloc[-1]
            latest_loss = loss.iloc[-1]

            # 处理零除错误和NaN值
            if pd.isna(latest_loss) or latest_loss == 0:
                # 如果没有损失，说明价格一直上涨
                return 100.0 if (not pd.isna(latest_gain) and latest_gain > 0) else 50.0

            if pd.isna(latest_gain) or latest_gain == 0:
                # 如果没有收益，说明价格一直下跌
                return 0.0

            rs = latest_gain / latest_loss
            rsi = 100 - (100 / (1 + rs))

            # 确保RSI在合理范围内
            rsi = max(0.0, min(100.0, rsi))

            return float(rsi) if not pd.isna(rsi) else 50.0

        except (ZeroDivisionError, ValueError) as e:
            print(f"RSI计算失败: {e}")
            return 50.0
        except Exception as e:
            print(f"RSI计算异常: {e}")
            return 50.0
    
    def _calculate_macd(self, close: pd.Series) -> Tuple[float, float]:
        """计算MACD"""
        try:
            if len(close) < 26:
                return 0.0, 0.0
            
            ema12 = close.ewm(span=12).mean()
            ema26 = close.ewm(span=26).mean()
            macd_line = ema12 - ema26
            signal_line = macd_line.ewm(span=9).mean()
            
            return macd_line.iloc[-1], signal_line.iloc[-1]
            
        except Exception:
            return 0.0, 0.0
    
    def _calculate_bollinger_bands(self, close: pd.Series, period: int = 20) -> Tuple[float, float, float]:
        """计算布林带"""
        try:
            if len(close) < period:
                current = close.iloc[-1]
                return current * 1.02, current, current * 0.98
            
            ma = close.rolling(period).mean()
            std = close.rolling(period).std()
            
            upper = ma + (std * 2)
            lower = ma - (std * 2)
            
            return upper.iloc[-1], ma.iloc[-1], lower.iloc[-1]
            
        except Exception:
            current = close.iloc[-1] if len(close) > 0 else 100.0
            return current * 1.02, current, current * 0.98
    
    def _generate_signals(self, indicators: Dict) -> Dict:
        """生成交易信号"""
        signals = {}
        
        try:
            current_price = indicators.get('current_price', 0)
            ma5 = indicators.get('ma5', 0)
            ma20 = indicators.get('ma20', 0)
            rsi = indicators.get('rsi', 50)
            macd = indicators.get('macd', 0)
            macd_signal = indicators.get('macd_signal', 0)
            
            # 移动平均信号
            if current_price > ma5 > ma20:
                signals['ma_signal'] = 'BUY'
            elif current_price < ma5 < ma20:
                signals['ma_signal'] = 'SELL'
            else:
                signals['ma_signal'] = 'HOLD'
            
            # RSI信号
            if rsi < 30:
                signals['rsi_signal'] = 'BUY'  # 超卖
            elif rsi > 70:
                signals['rsi_signal'] = 'SELL'  # 超买
            else:
                signals['rsi_signal'] = 'HOLD'
            
            # MACD信号
            if macd > macd_signal:
                signals['macd_signal'] = 'BUY'
            else:
                signals['macd_signal'] = 'SELL'
            
            return signals
            
        except Exception:
            return {'ma_signal': 'HOLD', 'rsi_signal': 'HOLD', 'macd_signal': 'HOLD'}
    
    def _get_technical_recommendation(self, indicators: Dict) -> str:
        """获取技术分析推荐"""
        signals = self._generate_signals(indicators)
        
        buy_signals = sum(1 for signal in signals.values() if signal == 'BUY')
        sell_signals = sum(1 for signal in signals.values() if signal == 'SELL')
        
        if buy_signals > sell_signals:
            return 'BUY'
        elif sell_signals > buy_signals:
            return 'SELL'
        else:
            return 'HOLD'
    
    def _get_technical_reasoning(self, indicators: Dict) -> str:
        """获取技术分析理由"""
        signals = self._generate_signals(indicators)
        recommendation = self._get_technical_recommendation(indicators)

        reasoning_parts = []

        if signals.get('ma_signal') == 'BUY':
            reasoning_parts.append('均线系统呈现多头排列，短期均线上穿长期均线')
        elif signals.get('ma_signal') == 'SELL':
            reasoning_parts.append('均线系统呈现空头排列，短期均线下穿长期均线')

        rsi = indicators.get('rsi', 50)
        if rsi < 30:
            reasoning_parts.append('RSI指标显示严重超卖，存在技术性反弹机会')
        elif rsi > 70:
            reasoning_parts.append('RSI指标显示严重超买，存在回调压力')

        if signals.get('macd_signal') == 'BUY':
            reasoning_parts.append('MACD指标形成金叉，动能转强')
        elif signals.get('macd_signal') == 'SELL':
            reasoning_parts.append('MACD指标形成死叉，动能转弱')

        # 翻译推荐
        rec_map = {'BUY': '买入', 'SELL': '卖出', 'HOLD': '持有'}
        rec_chinese = rec_map.get(recommendation, recommendation)

        if reasoning_parts:
            return f"技术面分析显示{rec_chinese}信号：" + "；".join(reasoning_parts)
        else:
            return f"技术指标综合研判显示{rec_chinese}信号"

class RiskManager(TradingAgent):
    """风险管理师"""

    def __init__(self, llm_config: Dict):
        super().__init__("风险管理师", "risk", llm_config)

    async def analyze(self, data: Dict) -> Dict:
        """风险分析"""
        try:
            symbol = data.get('symbol', '')
            portfolio_data = data.get('portfolio_data', {})
            market_data = data.get('market_data', {})

            # 计算风险指标
            risk_metrics = self._calculate_risk_metrics(portfolio_data, market_data)

            analysis = {
                'agent': self.name,
                'symbol': symbol,
                'analysis_type': 'risk',
                'timestamp': datetime.now().isoformat(),
                'risk_metrics': risk_metrics,
                'risk_level': self._assess_risk_level(risk_metrics),
                'recommendations': self._generate_risk_recommendations(risk_metrics),
                'position_sizing': self._calculate_position_sizing(risk_metrics)
            }

            return analysis

        except Exception as e:
            self.logger.error(f"风险分析失败: {e}")
            return {'error': str(e)}

    def _calculate_risk_metrics(self, portfolio_data: Dict, market_data: Dict) -> Dict:
        """计算风险指标"""
        try:
            # 基于真实数据的风险计算
            volatility = market_data.get('volatility', 0.2)
            beta = market_data.get('beta', 1.0)
            correlation = market_data.get('correlation', 0.7)

            # VaR计算 (简化)
            portfolio_value = portfolio_data.get('total_value', 1000000)
            var_95 = portfolio_value * volatility * 1.65  # 95% VaR

            # 最大回撤
            max_drawdown = volatility * 2

            # 夏普比率
            returns = market_data.get('returns', 0.08)
            risk_free_rate = 0.03
            sharpe_ratio = (returns - risk_free_rate) / volatility

            return {
                'volatility': round(volatility * 100, 2),
                'beta': round(beta, 2),
                'correlation': round(correlation, 2),
                'var_95': round(var_95, 2),
                'max_drawdown': round(max_drawdown * 100, 2),
                'sharpe_ratio': round(sharpe_ratio, 2),
                'portfolio_concentration': self._calculate_concentration(portfolio_data)
            }

        except Exception:
            return {
                'volatility': 20.0,
                'beta': 1.0,
                'correlation': 0.7,
                'var_95': 50000.0,
                'max_drawdown': 15.0,
                'sharpe_ratio': 1.0,
                'portfolio_concentration': 0.3
            }

    def _calculate_concentration(self, portfolio_data: Dict) -> float:
        """计算投资组合集中度"""
        positions = portfolio_data.get('positions', {})
        if not positions:
            return 0.0

        total_value = sum(positions.values())
        if total_value == 0:
            return 0.0

        # 计算赫芬达尔指数
        weights = [value / total_value for value in positions.values()]
        hhi = sum(w ** 2 for w in weights)

        return round(hhi, 3)

    def _assess_risk_level(self, risk_metrics: Dict) -> str:
        """评估风险等级"""
        volatility = risk_metrics.get('volatility', 20)
        max_drawdown = risk_metrics.get('max_drawdown', 15)
        concentration = risk_metrics.get('portfolio_concentration', 0.3)

        risk_score = 0

        # 波动率评分
        if volatility > 30:
            risk_score += 3
        elif volatility > 20:
            risk_score += 2
        elif volatility > 10:
            risk_score += 1

        # 最大回撤评分
        if max_drawdown > 20:
            risk_score += 3
        elif max_drawdown > 15:
            risk_score += 2
        elif max_drawdown > 10:
            risk_score += 1

        # 集中度评分
        if concentration > 0.5:
            risk_score += 3
        elif concentration > 0.3:
            risk_score += 2
        elif concentration > 0.2:
            risk_score += 1

        if risk_score >= 7:
            return 'HIGH'
        elif risk_score >= 4:
            return 'MEDIUM'
        else:
            return 'LOW'

    def _generate_risk_recommendations(self, risk_metrics: Dict) -> List[str]:
        """生成风险管理建议"""
        recommendations = []

        volatility = risk_metrics.get('volatility', 20)
        concentration = risk_metrics.get('portfolio_concentration', 0.3)
        sharpe_ratio = risk_metrics.get('sharpe_ratio', 1.0)

        if volatility > 25:
            recommendations.append("标的波动率偏高，建议适度降低仓位配置或采用期权对冲策略")

        if concentration > 0.4:
            recommendations.append("投资组合集中度过高，建议优化资产配置，增强分散化投资")

        if sharpe_ratio < 0.5:
            recommendations.append("风险调整后收益率不理想，建议重新审视投资策略和标的选择")

        if not recommendations:
            recommendations.append("当前风险收益特征良好，建议保持现有配置并持续监控")

        return recommendations

    def _calculate_position_sizing(self, risk_metrics: Dict) -> Dict:
        """计算仓位大小建议"""
        risk_level = self._assess_risk_level(risk_metrics)
        volatility = risk_metrics.get('volatility', 20)

        if risk_level == 'HIGH':
            max_position = 0.05  # 最大5%仓位
        elif risk_level == 'MEDIUM':
            max_position = 0.10  # 最大10%仓位
        else:
            max_position = 0.15  # 最大15%仓位

        # 基于波动率调整
        volatility_adjusted = max_position * (20 / max(volatility, 5))

        return {
            'max_position_percent': round(min(max_position, volatility_adjusted) * 100, 2),
            'recommended_position': round(min(max_position, volatility_adjusted) * 0.7 * 100, 2),
            'stop_loss_percent': round(volatility * 1.5, 2)
        }

class TradingAgentsCoordinator:
    """多智能体协调器"""

    def __init__(self, llm_config: Dict):
        self.llm_config = llm_config
        self.logger = logging.getLogger("TradingAgentsCoordinator")

        # 初始化各个智能体
        self.agents = {
            'fundamentals': FundamentalsAnalyst(llm_config),
            'sentiment': SentimentAnalyst(llm_config),
            'technical': TechnicalAnalyst(llm_config),
            'risk': RiskManager(llm_config)
        }

    async def analyze_symbol(self, symbol: str, data: Dict) -> Dict:
        """协调多个智能体分析股票"""
        try:
            self.logger.info(f"开始分析股票: {symbol}")

            # 并行执行各个智能体的分析
            tasks = []
            for agent_name, agent in self.agents.items():
                task = agent.analyze({**data, 'symbol': symbol})
                tasks.append((agent_name, task))

            # 等待所有分析完成
            results = {}
            for agent_name, task in tasks:
                try:
                    result = await task
                    results[agent_name] = result
                except Exception as e:
                    self.logger.error(f"{agent_name}分析失败: {e}")
                    results[agent_name] = {'error': str(e)}

            # 综合分析结果
            consensus = self._generate_consensus(results)

            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'individual_analyses': results,
                'consensus': consensus,
                'final_recommendation': consensus.get('recommendation', 'HOLD'),
                'confidence_score': consensus.get('confidence', 50)
            }

        except Exception as e:
            self.logger.error(f"协调分析失败: {e}")
            return {'error': str(e)}

    def _generate_consensus(self, results: Dict) -> Dict:
        """生成共识分析"""
        try:
            recommendations = []
            scores = []

            # 收集各智能体的推荐
            for agent_name, result in results.items():
                if 'error' not in result:
                    rec = result.get('recommendation', 'HOLD')
                    recommendations.append(rec)

                    # 转换推荐为数值评分
                    score = self._recommendation_to_score(rec)
                    scores.append(score)

            if not scores:
                return {'recommendation': 'HOLD', 'confidence': 0, 'reasoning': '所有分析都失败'}

            # 计算平均评分
            avg_score = sum(scores) / len(scores)

            # 转换回推荐
            final_recommendation = self._score_to_recommendation(avg_score)

            # 计算置信度
            confidence = self._calculate_confidence(scores, recommendations)

            # 生成理由
            reasoning = self._generate_reasoning(results, final_recommendation)

            return {
                'recommendation': final_recommendation,
                'confidence': confidence,
                'reasoning': reasoning,
                'agent_count': len(scores),
                'score_distribution': {
                    'buy_signals': sum(1 for s in scores if s > 60),
                    'hold_signals': sum(1 for s in scores if 40 <= s <= 60),
                    'sell_signals': sum(1 for s in scores if s < 40)
                }
            }

        except Exception as e:
            self.logger.error(f"生成共识失败: {e}")
            return {'recommendation': 'HOLD', 'confidence': 0, 'reasoning': '共识生成失败'}

    def _recommendation_to_score(self, recommendation: str) -> float:
        """将推荐转换为数值评分"""
        score_map = {
            'STRONG_BUY': 90,
            'BUY': 70,
            'BULLISH': 75,
            'NEUTRAL_BULLISH': 60,
            'HOLD': 50,
            'NEUTRAL': 50,
            'NEUTRAL_BEARISH': 40,
            'SELL': 30,
            'BEARISH': 25,
            'STRONG_SELL': 10
        }
        return score_map.get(recommendation, 50)

    def _score_to_recommendation(self, score: float) -> str:
        """将评分转换为推荐"""
        if score >= 80:
            return 'STRONG_BUY'
        elif score >= 65:
            return 'BUY'
        elif score >= 55:
            return 'HOLD'
        elif score >= 35:
            return 'SELL'
        else:
            return 'STRONG_SELL'

    def _calculate_confidence(self, scores: List[float], recommendations: List[str]) -> float:
        """计算置信度"""
        if not scores:
            return 0

        # 基于评分的一致性计算置信度
        avg_score = sum(scores) / len(scores)
        variance = sum((s - avg_score) ** 2 for s in scores) / len(scores)
        std_dev = variance ** 0.5

        # 标准差越小，置信度越高
        confidence = max(0, min(100, 100 - std_dev * 2))

        return round(confidence, 2)

    def _generate_reasoning(self, results: Dict, final_recommendation: str) -> str:
        """生成推荐理由"""
        reasoning_parts = []

        # 翻译最终推荐
        rec_map = {
            'STRONG_BUY': '强烈买入', 'BUY': '买入', 'HOLD': '持有',
            'SELL': '卖出', 'STRONG_SELL': '强烈卖出'
        }
        final_rec_chinese = rec_map.get(final_recommendation, final_recommendation)

        for agent_name, result in results.items():
            if 'error' not in result and 'reasoning' in result:
                agent_reasoning = result['reasoning']
                if agent_reasoning:
                    agent_display_name = result.get('agent', agent_name)
                    reasoning_parts.append(f"【{agent_display_name}】{agent_reasoning}")

        if reasoning_parts:
            return f"多维度分析一致显示{final_rec_chinese}信号。" + " ".join(reasoning_parts)
        else:
            return f"四大智能体协同分析综合显示{final_rec_chinese}投资建议"

# 全局协调器实例
trading_coordinator = None

def load_api_config():
    """加载API配置"""
    try:
        import os
        import json

        config_file = os.path.join(os.path.dirname(__file__), "..", "config", "trading_agents_config.json")

        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            return {
                'finnhub_api_key': config.get('finnhub_api_key', ''),
                'openai_api_key': config.get('openai_api_key', ''),
                'model': 'gpt-4o-mini'  # 使用成本较低的模型
            }
        else:
            return {
                'finnhub_api_key': '',
                'openai_api_key': '',
                'model': 'demo'
            }

    except Exception:
        return {
            'finnhub_api_key': '',
            'openai_api_key': '',
            'model': 'demo'
        }

def get_trading_coordinator(llm_config: Dict = None) -> TradingAgentsCoordinator:
    """获取交易智能体协调器"""
    global trading_coordinator

    if trading_coordinator is None:
        if llm_config is None:
            llm_config = load_api_config()
        trading_coordinator = TradingAgentsCoordinator(llm_config)

    return trading_coordinator

async def analyze_stock_with_agents(symbol: str, data: Dict) -> Dict:
    """使用多智能体分析股票"""
    coordinator = get_trading_coordinator()
    return await coordinator.analyze_symbol(symbol, data)
