#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚假流动性识别CNN模型
使用卷积神经网络识别市场中的虚假流动性模式
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import pickle
import os

try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers, models, optimizers, callbacks
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report, confusion_matrix
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False
    print("⚠️ TensorFlow未安装，将使用简化版本")

logger = logging.getLogger(__name__)

@dataclass
class LiquidityPattern:
    """流动性模式数据"""
    timestamp: datetime
    symbol: str
    order_book_snapshot: np.ndarray    # 订单簿快照 (深度×2)
    trade_sequence: np.ndarray         # 交易序列 (时间×特征)
    volume_profile: np.ndarray         # 成交量分布
    price_impact: float                # 价格冲击
    spread_dynamics: np.ndarray        # 价差动态
    is_fake: bool                      # 是否为虚假流动性
    confidence: float                  # 标注置信度

@dataclass
class FakeLiquiditySignal:
    """虚假流动性信号"""
    timestamp: datetime
    symbol: str
    fake_probability: float            # 虚假概率
    pattern_type: str                  # 模式类型
    risk_level: str                    # 风险等级
    evidence: Dict[str, float]         # 证据权重
    recommendation: str                # 建议操作

class FakeLiquidityCNNDetector:
    """虚假流动性CNN检测器"""
    
    def __init__(self, model_path: str = "models/fake_liquidity_cnn.h5"):
        self.model_path = model_path
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        
        # 模型参数
        self.sequence_length = 60      # 时间序列长度
        self.orderbook_depth = 10      # 订单簿深度
        self.feature_dim = 8           # 特征维度
        
        # 检测参数
        self.fake_threshold = 0.7      # 虚假流动性阈值
        self.confidence_threshold = 0.8 # 置信度阈值
        
        # 数据存储
        self.patterns_buffer = []      # 模式缓冲区
        self.signals_history = []      # 信号历史
        
        # 统计信息
        self.detection_stats = {
            'total_detections': 0,
            'fake_detections': 0,
            'accuracy': 0.0,
            'false_positive_rate': 0.0
        }
        
        if TF_AVAILABLE:
            self._build_model()
            self._load_model()
        else:
            self._init_simple_detector()
            
        logger.info("虚假流动性CNN检测器初始化完成")
        
    def _build_model(self):
        """构建CNN模型"""
        try:
            # 输入层
            # 订单簿输入 (batch, depth, 2)
            orderbook_input = keras.Input(shape=(self.orderbook_depth, 2), name='orderbook')
            
            # 交易序列输入 (batch, sequence_length, features)
            trade_input = keras.Input(shape=(self.sequence_length, self.feature_dim), name='trades')
            
            # 成交量分布输入 (batch, depth)
            volume_input = keras.Input(shape=(self.orderbook_depth,), name='volume')
            
            # 订单簿CNN分支
            ob_conv1 = layers.Conv1D(32, 3, activation='relu')(orderbook_input)
            ob_conv2 = layers.Conv1D(64, 3, activation='relu')(ob_conv1)
            ob_pool = layers.GlobalMaxPooling1D()(ob_conv2)
            ob_dense = layers.Dense(32, activation='relu')(ob_pool)
            
            # 交易序列CNN分支
            trade_conv1 = layers.Conv1D(32, 5, activation='relu')(trade_input)
            trade_conv2 = layers.Conv1D(64, 3, activation='relu')(trade_conv1)
            trade_pool = layers.GlobalMaxPooling1D()(trade_conv2)
            trade_dense = layers.Dense(32, activation='relu')(trade_pool)
            
            # 成交量分布分支
            vol_dense1 = layers.Dense(16, activation='relu')(volume_input)
            vol_dense2 = layers.Dense(8, activation='relu')(vol_dense1)
            
            # 特征融合
            merged = layers.concatenate([ob_dense, trade_dense, vol_dense2])
            
            # 全连接层
            fc1 = layers.Dense(128, activation='relu')(merged)
            fc1 = layers.Dropout(0.3)(fc1)
            fc2 = layers.Dense(64, activation='relu')(fc1)
            fc2 = layers.Dropout(0.2)(fc2)
            fc3 = layers.Dense(32, activation='relu')(fc2)
            
            # 输出层
            output = layers.Dense(1, activation='sigmoid', name='fake_probability')(fc3)
            
            # 创建模型
            self.model = keras.Model(
                inputs=[orderbook_input, trade_input, volume_input],
                outputs=output
            )
            
            # 编译模型
            self.model.compile(
                optimizer=optimizers.Adam(learning_rate=0.001),
                loss='binary_crossentropy',
                metrics=['accuracy', 'precision', 'recall']
            )
            
            logger.info("CNN模型构建完成")
            
        except Exception as e:
            logger.error(f"构建CNN模型失败: {e}")
            self._init_simple_detector()
            
    def _init_simple_detector(self):
        """初始化简化检测器"""
        logger.info("使用简化版虚假流动性检测器")
        self.model = None
        
    def _load_model(self):
        """加载预训练模型"""
        try:
            if os.path.exists(self.model_path) and self.model is not None:
                self.model.load_weights(self.model_path)
                self.is_trained = True
                logger.info(f"加载预训练模型: {self.model_path}")
            else:
                logger.info("未找到预训练模型，需要训练")
                
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            
    def prepare_training_data(self, patterns: List[LiquidityPattern]) -> Tuple[List[np.ndarray], np.ndarray]:
        """准备训练数据"""
        try:
            orderbook_data = []
            trade_data = []
            volume_data = []
            labels = []
            
            for pattern in patterns:
                # 订单簿数据预处理
                ob_data = self._preprocess_orderbook(pattern.order_book_snapshot)
                if ob_data is not None:
                    orderbook_data.append(ob_data)
                    
                    # 交易序列数据预处理
                    trade_seq = self._preprocess_trades(pattern.trade_sequence)
                    trade_data.append(trade_seq)
                    
                    # 成交量分布预处理
                    vol_data = self._preprocess_volume(pattern.volume_profile)
                    volume_data.append(vol_data)
                    
                    # 标签
                    labels.append(1.0 if pattern.is_fake else 0.0)
                    
            if not orderbook_data:
                raise ValueError("没有有效的训练数据")
                
            # 转换为numpy数组
            X_orderbook = np.array(orderbook_data)
            X_trades = np.array(trade_data)
            X_volume = np.array(volume_data)
            y = np.array(labels)
            
            logger.info(f"准备训练数据完成: {len(labels)} 个样本")
            return [X_orderbook, X_trades, X_volume], y
            
        except Exception as e:
            logger.error(f"准备训练数据失败: {e}")
            return [], np.array([])
            
    def _preprocess_orderbook(self, orderbook: np.ndarray) -> Optional[np.ndarray]:
        """预处理订单簿数据"""
        try:
            if orderbook is None or orderbook.size == 0:
                return None
                
            # 确保形状正确
            if len(orderbook.shape) != 2 or orderbook.shape[1] != 2:
                return None
                
            # 截取或填充到指定深度
            if orderbook.shape[0] > self.orderbook_depth:
                processed = orderbook[:self.orderbook_depth, :]
            else:
                # 填充零值
                processed = np.zeros((self.orderbook_depth, 2))
                processed[:orderbook.shape[0], :] = orderbook
                
            # 标准化
            processed = self.scaler.fit_transform(processed.reshape(-1, 1)).reshape(self.orderbook_depth, 2)
            
            return processed
            
        except Exception as e:
            logger.error(f"预处理订单簿数据失败: {e}")
            return None
            
    def _preprocess_trades(self, trades: np.ndarray) -> np.ndarray:
        """预处理交易序列数据"""
        try:
            if trades is None or trades.size == 0:
                return np.zeros((self.sequence_length, self.feature_dim))
                
            # 确保特征维度
            if len(trades.shape) == 1:
                trades = trades.reshape(-1, 1)
                
            # 截取或填充到指定长度
            if trades.shape[0] > self.sequence_length:
                processed = trades[:self.sequence_length, :]
            else:
                processed = np.zeros((self.sequence_length, trades.shape[1]))
                processed[:trades.shape[0], :] = trades
                
            # 确保特征维度
            if processed.shape[1] < self.feature_dim:
                temp = np.zeros((self.sequence_length, self.feature_dim))
                temp[:, :processed.shape[1]] = processed
                processed = temp
            elif processed.shape[1] > self.feature_dim:
                processed = processed[:, :self.feature_dim]
                
            return processed
            
        except Exception as e:
            logger.error(f"预处理交易序列失败: {e}")
            return np.zeros((self.sequence_length, self.feature_dim))
            
    def _preprocess_volume(self, volume: np.ndarray) -> np.ndarray:
        """预处理成交量分布"""
        try:
            if volume is None or volume.size == 0:
                return np.zeros(self.orderbook_depth)
                
            # 截取或填充到指定深度
            if len(volume) > self.orderbook_depth:
                processed = volume[:self.orderbook_depth]
            else:
                processed = np.zeros(self.orderbook_depth)
                processed[:len(volume)] = volume
                
            # 归一化
            if np.sum(processed) > 0:
                processed = processed / np.sum(processed)
                
            return processed
            
        except Exception as e:
            logger.error(f"预处理成交量分布失败: {e}")
            return np.zeros(self.orderbook_depth)
            
    def train_model(self, patterns: List[LiquidityPattern], 
                   validation_split: float = 0.2, epochs: int = 50) -> Dict[str, Any]:
        """训练模型"""
        try:
            if not TF_AVAILABLE or self.model is None:
                return self._train_simple_model(patterns)
                
            # 准备数据
            X, y = self.prepare_training_data(patterns)
            if len(X) == 0:
                raise ValueError("训练数据为空")
                
            # 分割训练和验证集
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=validation_split, random_state=42, stratify=y
            )
            
            # 训练回调
            callbacks_list = [
                callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=10,
                    restore_best_weights=True
                ),
                callbacks.ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=5,
                    min_lr=1e-6
                )
            ]
            
            # 训练模型
            history = self.model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=epochs,
                batch_size=32,
                callbacks=callbacks_list,
                verbose=1
            )
            
            # 保存模型
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            self.model.save_weights(self.model_path)
            self.is_trained = True
            
            # 评估模型
            val_loss, val_acc, val_precision, val_recall = self.model.evaluate(X_val, y_val, verbose=0)
            
            # 预测验证集
            y_pred = self.model.predict(X_val)
            y_pred_binary = (y_pred > 0.5).astype(int)
            
            # 生成报告
            report = classification_report(y_val, y_pred_binary, output_dict=True)
            
            training_result = {
                'final_loss': val_loss,
                'final_accuracy': val_acc,
                'final_precision': val_precision,
                'final_recall': val_recall,
                'classification_report': report,
                'training_samples': len(y_train),
                'validation_samples': len(y_val)
            }
            
            logger.info(f"模型训练完成 - 准确率: {val_acc:.4f}, 精确率: {val_precision:.4f}, 召回率: {val_recall:.4f}")
            
            return training_result
            
        except Exception as e:
            logger.error(f"训练模型失败: {e}")
            return {'error': str(e)}
            
    def _train_simple_model(self, patterns: List[LiquidityPattern]) -> Dict[str, Any]:
        """训练简化模型"""
        try:
            # 简单的基于规则的模型
            fake_count = sum(1 for p in patterns if p.is_fake)
            total_count = len(patterns)
            
            # 计算基础统计
            self.detection_stats['total_detections'] = total_count
            self.detection_stats['fake_detections'] = fake_count
            
            if total_count > 0:
                self.detection_stats['accuracy'] = 0.75  # 假设准确率
                
            self.is_trained = True
            
            logger.info(f"简化模型训练完成 - 样本数: {total_count}, 虚假样本: {fake_count}")
            
            return {
                'model_type': 'simple_rule_based',
                'training_samples': total_count,
                'fake_samples': fake_count,
                'estimated_accuracy': 0.75
            }
            
        except Exception as e:
            logger.error(f"训练简化模型失败: {e}")
            return {'error': str(e)}

    def detect_fake_liquidity(self, pattern: LiquidityPattern) -> FakeLiquiditySignal:
        """检测虚假流动性"""
        try:
            if not self.is_trained:
                logger.warning("模型未训练，使用默认检测")
                return self._default_detection(pattern)

            if TF_AVAILABLE and self.model is not None:
                return self._cnn_detection(pattern)
            else:
                return self._rule_based_detection(pattern)

        except Exception as e:
            logger.error(f"检测虚假流动性失败: {e}")
            return self._default_detection(pattern)

    def _cnn_detection(self, pattern: LiquidityPattern) -> FakeLiquiditySignal:
        """CNN模型检测"""
        try:
            # 预处理输入数据
            ob_data = self._preprocess_orderbook(pattern.order_book_snapshot)
            trade_data = self._preprocess_trades(pattern.trade_sequence)
            vol_data = self._preprocess_volume(pattern.volume_profile)

            if ob_data is None:
                return self._default_detection(pattern)

            # 准备模型输入
            X_ob = np.expand_dims(ob_data, axis=0)
            X_trade = np.expand_dims(trade_data, axis=0)
            X_vol = np.expand_dims(vol_data, axis=0)

            # 模型预测
            fake_prob = self.model.predict([X_ob, X_trade, X_vol], verbose=0)[0][0]

            # 分析证据
            evidence = self._analyze_evidence(pattern, fake_prob)

            # 确定模式类型
            pattern_type = self._classify_pattern_type(pattern, fake_prob)

            # 风险等级
            risk_level = self._assess_risk_level(fake_prob)

            # 生成建议
            recommendation = self._generate_recommendation(fake_prob, risk_level)

            signal = FakeLiquiditySignal(
                timestamp=pattern.timestamp,
                symbol=pattern.symbol,
                fake_probability=float(fake_prob),
                pattern_type=pattern_type,
                risk_level=risk_level,
                evidence=evidence,
                recommendation=recommendation
            )

            # 更新统计
            self.detection_stats['total_detections'] += 1
            if fake_prob > self.fake_threshold:
                self.detection_stats['fake_detections'] += 1

            # 存储信号
            self.signals_history.append(signal)

            return signal

        except Exception as e:
            logger.error(f"CNN检测失败: {e}")
            return self._default_detection(pattern)

    def _rule_based_detection(self, pattern: LiquidityPattern) -> FakeLiquiditySignal:
        """基于规则的检测"""
        try:
            fake_score = 0.0
            evidence = {}

            # 规则1: 订单簿不平衡检测
            if pattern.order_book_snapshot is not None and pattern.order_book_snapshot.size > 0:
                bid_volume = np.sum(pattern.order_book_snapshot[:, 0])
                ask_volume = np.sum(pattern.order_book_snapshot[:, 1])

                if bid_volume > 0 and ask_volume > 0:
                    imbalance = abs(bid_volume - ask_volume) / (bid_volume + ask_volume)
                    if imbalance > 0.8:  # 严重不平衡
                        fake_score += 0.3
                        evidence['order_imbalance'] = imbalance

            # 规则2: 价格冲击异常
            if abs(pattern.price_impact) > 0.05:  # 5%以上价格冲击
                fake_score += 0.25
                evidence['price_impact'] = abs(pattern.price_impact)

            # 规则3: 价差动态异常
            if pattern.spread_dynamics is not None and len(pattern.spread_dynamics) > 0:
                spread_volatility = np.std(pattern.spread_dynamics)
                if spread_volatility > 0.01:  # 价差波动过大
                    fake_score += 0.2
                    evidence['spread_volatility'] = spread_volatility

            # 规则4: 成交量分布异常
            if pattern.volume_profile is not None and len(pattern.volume_profile) > 0:
                # 检查是否存在异常的成交量集中
                max_vol_ratio = np.max(pattern.volume_profile) / np.sum(pattern.volume_profile)
                if max_vol_ratio > 0.7:  # 70%以上成交量集中在一个价位
                    fake_score += 0.25
                    evidence['volume_concentration'] = max_vol_ratio

            # 限制分数范围
            fake_prob = min(fake_score, 1.0)

            # 分类和建议
            pattern_type = self._classify_pattern_type(pattern, fake_prob)
            risk_level = self._assess_risk_level(fake_prob)
            recommendation = self._generate_recommendation(fake_prob, risk_level)

            signal = FakeLiquiditySignal(
                timestamp=pattern.timestamp,
                symbol=pattern.symbol,
                fake_probability=fake_prob,
                pattern_type=pattern_type,
                risk_level=risk_level,
                evidence=evidence,
                recommendation=recommendation
            )

            return signal

        except Exception as e:
            logger.error(f"规则检测失败: {e}")
            return self._default_detection(pattern)

    def _default_detection(self, pattern: LiquidityPattern) -> FakeLiquiditySignal:
        """默认检测"""
        return FakeLiquiditySignal(
            timestamp=pattern.timestamp,
            symbol=pattern.symbol,
            fake_probability=0.5,
            pattern_type="UNKNOWN",
            risk_level="MEDIUM",
            evidence={},
            recommendation="MONITOR"
        )

    def _analyze_evidence(self, pattern: LiquidityPattern, fake_prob: float) -> Dict[str, float]:
        """分析检测证据"""
        evidence = {}

        try:
            # 订单簿证据
            if pattern.order_book_snapshot is not None and pattern.order_book_snapshot.size > 0:
                bid_vol = np.sum(pattern.order_book_snapshot[:, 0])
                ask_vol = np.sum(pattern.order_book_snapshot[:, 1])
                if bid_vol + ask_vol > 0:
                    evidence['bid_ask_ratio'] = bid_vol / (bid_vol + ask_vol)

            # 价格冲击证据
            evidence['price_impact'] = abs(pattern.price_impact)

            # 价差证据
            if pattern.spread_dynamics is not None and len(pattern.spread_dynamics) > 0:
                evidence['spread_mean'] = np.mean(pattern.spread_dynamics)
                evidence['spread_std'] = np.std(pattern.spread_dynamics)

            # 成交量证据
            if pattern.volume_profile is not None and len(pattern.volume_profile) > 0:
                evidence['volume_entropy'] = self._calculate_entropy(pattern.volume_profile)

            # 模型置信度
            evidence['model_confidence'] = fake_prob

        except Exception as e:
            logger.error(f"分析证据失败: {e}")

        return evidence

    def _calculate_entropy(self, distribution: np.ndarray) -> float:
        """计算分布熵"""
        try:
            # 归一化
            dist = distribution / np.sum(distribution)
            # 避免log(0)
            dist = dist[dist > 0]
            # 计算熵
            entropy = -np.sum(dist * np.log2(dist))
            return entropy
        except:
            return 0.0

    def _classify_pattern_type(self, pattern: LiquidityPattern, fake_prob: float) -> str:
        """分类模式类型"""
        try:
            if fake_prob < 0.3:
                return "GENUINE_LIQUIDITY"
            elif fake_prob < 0.5:
                return "SUSPICIOUS_ACTIVITY"
            elif fake_prob < 0.7:
                return "LIKELY_FAKE"
            elif fake_prob < 0.9:
                return "FAKE_LIQUIDITY"
            else:
                return "OBVIOUS_MANIPULATION"

        except Exception as e:
            logger.error(f"分类模式类型失败: {e}")
            return "UNKNOWN"

    def _assess_risk_level(self, fake_prob: float) -> str:
        """评估风险等级"""
        try:
            if fake_prob < 0.3:
                return "LOW"
            elif fake_prob < 0.6:
                return "MEDIUM"
            elif fake_prob < 0.8:
                return "HIGH"
            else:
                return "CRITICAL"

        except Exception as e:
            logger.error(f"评估风险等级失败: {e}")
            return "MEDIUM"

    def _generate_recommendation(self, fake_prob: float, risk_level: str) -> str:
        """生成操作建议"""
        try:
            if fake_prob < 0.3:
                return "SAFE_TO_TRADE"
            elif fake_prob < 0.5:
                return "MONITOR_CLOSELY"
            elif fake_prob < 0.7:
                return "REDUCE_POSITION"
            elif fake_prob < 0.9:
                return "AVOID_TRADING"
            else:
                return "REPORT_MANIPULATION"

        except Exception as e:
            logger.error(f"生成建议失败: {e}")
            return "MONITOR"

    def batch_detect(self, patterns: List[LiquidityPattern]) -> List[FakeLiquiditySignal]:
        """批量检测虚假流动性"""
        try:
            signals = []
            for pattern in patterns:
                signal = self.detect_fake_liquidity(pattern)
                signals.append(signal)

            logger.info(f"批量检测完成: {len(patterns)} 个模式")
            return signals

        except Exception as e:
            logger.error(f"批量检测失败: {e}")
            return []

    def evaluate_model(self, test_patterns: List[LiquidityPattern]) -> Dict[str, Any]:
        """评估模型性能"""
        try:
            if not test_patterns:
                return {'error': '测试数据为空'}

            # 获取预测结果
            predictions = []
            true_labels = []

            for pattern in test_patterns:
                signal = self.detect_fake_liquidity(pattern)
                predictions.append(1 if signal.fake_probability > self.fake_threshold else 0)
                true_labels.append(1 if pattern.is_fake else 0)

            # 计算评估指标
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

            accuracy = accuracy_score(true_labels, predictions)
            precision = precision_score(true_labels, predictions, zero_division=0)
            recall = recall_score(true_labels, predictions, zero_division=0)
            f1 = f1_score(true_labels, predictions, zero_division=0)

            # 混淆矩阵
            tn, fp, fn, tp = confusion_matrix(true_labels, predictions).ravel()

            evaluation_result = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'true_positives': int(tp),
                'true_negatives': int(tn),
                'false_positives': int(fp),
                'false_negatives': int(fn),
                'test_samples': len(test_patterns),
                'fake_samples': sum(true_labels),
                'threshold': self.fake_threshold
            }

            logger.info(f"模型评估完成 - 准确率: {accuracy:.4f}, F1分数: {f1:.4f}")

            return evaluation_result

        except Exception as e:
            logger.error(f"模型评估失败: {e}")
            return {'error': str(e)}

    def get_detection_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        try:
            stats = self.detection_stats.copy()

            # 计算准确率
            if stats['total_detections'] > 0:
                stats['fake_rate'] = stats['fake_detections'] / stats['total_detections']
            else:
                stats['fake_rate'] = 0.0

            # 最近信号统计
            recent_signals = [s for s in self.signals_history
                            if (datetime.now() - s.timestamp).total_seconds() < 3600]  # 1小时内

            stats['recent_signals'] = len(recent_signals)
            stats['recent_fake_signals'] = len([s for s in recent_signals
                                              if s.fake_probability > self.fake_threshold])

            # 风险等级分布
            risk_distribution = {}
            for signal in recent_signals:
                risk = signal.risk_level
                risk_distribution[risk] = risk_distribution.get(risk, 0) + 1

            stats['risk_distribution'] = risk_distribution

            return stats

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return self.detection_stats.copy()

    def generate_training_data(self, symbols: List[str], days: int = 30) -> List[LiquidityPattern]:
        """生成训练数据（模拟）"""
        try:
            patterns = []

            for _ in range(days * 100):  # 每天100个模式
                symbol = np.random.choice(symbols)
                timestamp = datetime.now() - timedelta(
                    days=np.random.randint(0, days),
                    hours=np.random.randint(0, 24),
                    minutes=np.random.randint(0, 60)
                )

                # 真实数据加载逻辑
                # 实际实现应从外部数据源加载
                raise NotImplementedError("真实数据加载需要从外部数据源实现")

                # 随机标记为虚假或真实
                is_fake = np.random.random() < 0.3  # 30%为虚假

                pattern = LiquidityPattern(
                    timestamp=timestamp,
                    symbol=symbol,
                    order_book_snapshot=orderbook,
                    trade_sequence=trades,
                    volume_profile=volume,
                    price_impact=np.random.normal(0, 0.02),
                    spread_dynamics=np.random.normal(0.001, 0.0005, 20),
                    is_fake=is_fake,
                    confidence=np.random.uniform(0.7, 1.0)
                )

                patterns.append(pattern)

            logger.info(f"生成训练数据: {len(patterns)} 个模式")
            return patterns

        except Exception as e:
            logger.error(f"生成训练数据失败: {e}")
            return []

    def _generate_mock_orderbook(self) -> np.ndarray:
        """生成模拟订单簿"""
        try:
            depth = self.orderbook_depth

            # 生成买卖盘数据
            bid_volumes = np.random.exponential(1000, depth)
            ask_volumes = np.random.exponential(1000, depth)

            # 添加一些不平衡
            if np.random.random() < 0.2:  # 20%概率不平衡
                if np.random.random() < 0.5:
                    bid_volumes *= 3  # 买盘过多
                else:
                    ask_volumes *= 3  # 卖盘过多

            orderbook = np.column_stack([bid_volumes, ask_volumes])
            return orderbook

        except Exception as e:
            logger.error(f"生成模拟订单簿失败: {e}")
            return np.zeros((self.orderbook_depth, 2))

    def _generate_mock_trades(self) -> np.ndarray:
        """生成模拟交易序列"""
        try:
            length = self.sequence_length
            features = self.feature_dim

            # 生成随机交易特征
            trades = np.random.normal(0, 1, (length, features))

            # 添加一些趋势
            if np.random.random() < 0.3:  # 30%概率有趋势
                trend = np.random.normal(0, 0.1, length)
                trades[:, 0] += np.cumsum(trend)  # 价格趋势

            return trades

        except Exception as e:
            logger.error(f"生成模拟交易序列失败: {e}")
            return np.zeros((self.sequence_length, self.feature_dim))

    def _generate_mock_volume(self) -> np.ndarray:
        """生成模拟成交量分布"""
        try:
            depth = self.orderbook_depth

            # 正常分布
            volume = np.random.exponential(100, depth)

            # 添加异常集中
            if np.random.random() < 0.2:  # 20%概率异常集中
                idx = np.random.randint(0, depth)
                volume[idx] *= 10  # 某个价位成交量异常大

            return volume

        except Exception as e:
            logger.error(f"生成模拟成交量失败: {e}")
            return np.zeros(self.orderbook_depth)

    def save_model_config(self, config_path: str):
        """保存模型配置"""
        try:
            config = {
                'model_path': self.model_path,
                'sequence_length': self.sequence_length,
                'orderbook_depth': self.orderbook_depth,
                'feature_dim': self.feature_dim,
                'fake_threshold': self.fake_threshold,
                'confidence_threshold': self.confidence_threshold,
                'is_trained': self.is_trained,
                'detection_stats': self.detection_stats
            }

            with open(config_path, 'wb') as f:
                pickle.dump(config, f)

            logger.info(f"模型配置已保存: {config_path}")

        except Exception as e:
            logger.error(f"保存模型配置失败: {e}")

    def load_model_config(self, config_path: str):
        """加载模型配置"""
        try:
            if not os.path.exists(config_path):
                logger.warning(f"配置文件不存在: {config_path}")
                return

            with open(config_path, 'rb') as f:
                config = pickle.load(f)

            # 更新配置
            self.model_path = config.get('model_path', self.model_path)
            self.sequence_length = config.get('sequence_length', self.sequence_length)
            self.orderbook_depth = config.get('orderbook_depth', self.orderbook_depth)
            self.feature_dim = config.get('feature_dim', self.feature_dim)
            self.fake_threshold = config.get('fake_threshold', self.fake_threshold)
            self.confidence_threshold = config.get('confidence_threshold', self.confidence_threshold)
            self.is_trained = config.get('is_trained', self.is_trained)
            self.detection_stats = config.get('detection_stats', self.detection_stats)

            logger.info(f"模型配置已加载: {config_path}")

        except Exception as e:
            logger.error(f"加载模型配置失败: {e}")

    def get_model_summary(self) -> str:
        """获取模型摘要"""
        try:
            if TF_AVAILABLE and self.model is not None:
                import io
                stream = io.StringIO()
                self.model.summary(print_fn=lambda x: stream.write(x + '\n'))
                return stream.getvalue()
            else:
                return "使用简化规则模型"

        except Exception as e:
            logger.error(f"获取模型摘要失败: {e}")
            return "无法获取模型摘要"
