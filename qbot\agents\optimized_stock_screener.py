#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能优化的股票筛选器
使用向量化操作和分批处理提升性能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from qbot.common.result import Result, ErrorCode, ErrorSeverity, success, failure
from qbot.common.enhanced_logger import get_logger


class OptimizedStockScreener:
    """性能优化的股票筛选器"""
    
    def __init__(self, batch_size: int = 500, max_workers: int = 20):
        self.logger = get_logger("optimized_stock_screener")
        self.batch_size = batch_size  # 分批处理大小
        self.max_workers = max_workers  # 最大并发数
        
        # 性能监控
        self.performance_metrics = {
            'total_stocks_processed': 0,
            'processing_time': 0,
            'vectorization_time': 0,
            'data_fetch_time': 0
        }
    
    async def get_china_stock_list_optimized(self, limit: Optional[int] = None) -> List[str]:
        """获取中国A股列表 - 优化版本，支持分批处理"""
        operation_name = "get_china_stock_list"
        self.logger.start_operation(operation_name, limit=limit)
        
        try:
            # 尝试从通达信获取完整列表
            try:
                from qbot.data.tdx_data_reader import tdx_reader
                if tdx_reader.is_available():
                    stock_list = tdx_reader.get_stock_list()
                    if stock_list:
                        symbols = [stock['symbol'] for stock in stock_list]
                        
                        # 应用限制（如果指定）
                        if limit and limit > 0:
                            symbols = symbols[:limit]
                            self.logger.info(f"从通达信获取到{len(symbols)}只A股（限制{limit}只）")
                        else:
                            self.logger.info(f"从通达信获取到{len(symbols)}只A股（完整列表）")
                        
                        self.logger.end_operation(operation_name, success=True, count=len(symbols))
                        return symbols
            except ImportError:
                self.logger.warning("通达信数据读取器不可用")
            
            # 备用：从股票名称数据库获取
            try:
                from qbot.data.stock_names_database import stock_names_db
                symbols = list(stock_names_db.all_stocks['CN_A'].keys())
                
                if limit and limit > 0:
                    symbols = symbols[:limit]
                    self.logger.info(f"从名称数据库获取到{len(symbols)}只A股（限制{limit}只）")
                else:
                    self.logger.info(f"从名称数据库获取到{len(symbols)}只A股（完整列表）")
                
                self.logger.end_operation(operation_name, success=True, count=len(symbols))
                return symbols
                
            except ImportError:
                self.logger.warning("股票名称数据库不可用")
            
            # 最后备用：扩展的A股列表
            extended_symbols = self._get_extended_china_symbols()
            
            if limit and limit > 0:
                extended_symbols = extended_symbols[:limit]
            
            self.logger.info(f"使用扩展A股列表，共{len(extended_symbols)}只股票")
            self.logger.end_operation(operation_name, success=True, count=len(extended_symbols))
            return extended_symbols
            
        except Exception as e:
            self.logger.log_exception(e, 
                context={'limit': limit},
                error_code=ErrorCode.DATA_SOURCE_UNAVAILABLE,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error=str(e))
            return []
    
    def _get_extended_china_symbols(self) -> List[str]:
        """获取扩展的中国A股代码列表"""
        # 主板股票 (600xxx, 601xxx, 603xxx, 605xxx)
        main_board = []
        for prefix in ['600', '601', '603', '605']:
            for i in range(0, 1000, 10):  # 每10个取一个样本
                main_board.append(f"{prefix}{i:03d}")
        
        # 深圳主板 (000xxx, 001xxx)
        shenzhen_main = []
        for prefix in ['000', '001']:
            for i in range(0, 1000, 10):
                shenzhen_main.append(f"{prefix}{i:03d}")
        
        # 中小板 (002xxx)
        sme_board = []
        for i in range(0, 1000, 10):
            sme_board.append(f"002{i:03d}")
        
        # 创业板 (300xxx, 301xxx)
        growth_board = []
        for prefix in ['300', '301']:
            for i in range(0, 1000, 20):  # 创业板密度较低
                growth_board.append(f"{prefix}{i:03d}")
        
        # 科创板 (688xxx)
        star_board = []
        for i in range(0, 1000, 20):
            star_board.append(f"688{i:03d}")
        
        # 北交所 (430xxx, 831xxx, 832xxx, 833xxx, 834xxx, 835xxx, 836xxx, 837xxx, 838xxx, 839xxx)
        beijing_board = []
        for prefix in ['430', '831', '832', '833', '834', '835']:
            for i in range(0, 1000, 50):  # 北交所密度更低
                beijing_board.append(f"{prefix}{i:03d}")
        
        all_symbols = main_board + shenzhen_main + sme_board + growth_board + star_board + beijing_board
        
        # 添加一些知名股票确保覆盖
        famous_stocks = [
            '000001', '000002', '000858', '000876', '000977',
            '600000', '600036', '600519', '600887', '600900',
            '002415', '002594', '002714', '002841',
            '300059', '300750', '300760', '300896',
            '688001', '688009', '688036', '688111'
        ]
        
        # 合并并去重
        all_symbols.extend(famous_stocks)
        return list(set(all_symbols))
    
    def apply_screening_criteria_vectorized(self, data: pd.DataFrame, criteria: Dict) -> pd.DataFrame:
        """应用筛选条件 - 向量化版本"""
        operation_name = "apply_screening_criteria_vectorized"
        start_time = time.time()
        self.logger.start_operation(operation_name, 
                                   rows=len(data), 
                                   criteria_count=len(criteria))
        
        try:
            if data.empty:
                return data
            
            # 创建筛选掩码
            mask = pd.Series(True, index=data.index)
            
            # 基本面筛选 - 向量化操作
            fundamental = criteria.get('fundamental', {})
            for key, value in fundamental.items():
                if key in data.columns:
                    min_val = value.get('min', -np.inf)
                    max_val = value.get('max', np.inf)
                    
                    # 向量化条件检查
                    condition = (data[key] >= min_val) & (data[key] <= max_val)
                    mask = mask & condition
                    
                    self.logger.debug(f"基本面筛选 {key}: {condition.sum()}/{len(data)} 通过")
                else:
                    self.logger.warning(f"筛选条件中的列'{key}'不存在，跳过此条件")
            
            # 技术面筛选 - 向量化操作
            technical = criteria.get('technical', {})
            
            # RSI筛选
            if 'rsi' in data.columns:
                rsi_min = technical.get('rsi_min', 0)
                rsi_max = technical.get('rsi_max', 100)
                rsi_condition = (data['rsi'] >= rsi_min) & (data['rsi'] <= rsi_max)
                mask = mask & rsi_condition
                self.logger.debug(f"RSI筛选: {rsi_condition.sum()}/{len(data)} 通过")
            
            # 均线趋势筛选
            ma_trend = technical.get('ma_trend', '不限')
            if ma_trend != '不限' and 'ma_trend' in data.columns:
                trend_condition = data['ma_trend'] == ma_trend
                mask = mask & trend_condition
                self.logger.debug(f"均线趋势筛选: {trend_condition.sum()}/{len(data)} 通过")
            
            # 融资融券筛选 - 向量化操作
            leverage = criteria.get('leverage', {})
            
            if leverage.get('short_interest_max') and 'short_interest' in data.columns:
                short_condition = data['short_interest'] <= leverage['short_interest_max']
                mask = mask & short_condition
                self.logger.debug(f"融券筛选: {short_condition.sum()}/{len(data)} 通过")
            
            if leverage.get('liquidity_min') and 'liquidity_ratio' in data.columns:
                liquidity_condition = data['liquidity_ratio'] >= leverage['liquidity_min']
                mask = mask & liquidity_condition
                self.logger.debug(f"流动性筛选: {liquidity_condition.sum()}/{len(data)} 通过")
            
            # 应用筛选掩码
            filtered_data = data[mask].copy()
            
            processing_time = time.time() - start_time
            self.performance_metrics['vectorization_time'] += processing_time
            
            self.logger.end_operation(operation_name, success=True,
                                    input_rows=len(data),
                                    output_rows=len(filtered_data),
                                    filter_ratio=len(filtered_data)/len(data),
                                    processing_time=processing_time)
            
            return filtered_data
            
        except Exception as e:
            self.logger.log_exception(e,
                context={'data_shape': data.shape, 'criteria': criteria},
                error_code=ErrorCode.UNKNOWN_ERROR,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error=str(e))
            return data  # 返回原始数据
    
    def calculate_margin_score_vectorized(self, data: pd.DataFrame, criteria: Dict) -> pd.Series:
        """计算融资融券评分 - 向量化版本"""
        operation_name = "calculate_margin_score_vectorized"
        start_time = time.time()
        self.logger.start_operation(operation_name, rows=len(data))
        
        try:
            if data.empty:
                return pd.Series(dtype=float)
            
            # 检查必需的列
            required_columns = ['market_cap', 'liquidity_ratio', 'volatility', 'daily_volume']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                self.logger.warning(f"缺少融资融券评分所需的列: {missing_columns}，返回默认评分")
                return pd.Series([50.0] * len(data), index=data.index)
            
            # 向量化计算各项评分
            
            # 市值评分 (30%) - 向量化
            market_cap_score = np.minimum(
                data['market_cap'] / criteria['min_market_cap'], 5
            ) * 0.3
            
            # 流动性评分 (25%) - 向量化
            liquidity_score = np.minimum(
                data['liquidity_ratio'] / criteria['min_liquidity_ratio'], 3
            ) * 0.25
            
            # 价格稳定性评分 (20%) - 向量化
            volatility_score = np.maximum(
                0, (criteria['max_volatility'] - data['volatility']) / criteria['max_volatility']
            ) * 0.2
            
            # 成交量评分 (15%) - 向量化
            volume_score = np.minimum(
                data['daily_volume'] / criteria['min_daily_volume'], 3
            ) * 0.15
            
            # 基本面评分 (10%) - 向量化
            fundamental_score = pd.Series(0.1, index=data.index)
            
            if 'pe_ratio' in data.columns:
                pe_condition = (data['pe_ratio'] > 0) & (data['pe_ratio'] < 30)
                fundamental_score = fundamental_score * np.where(pe_condition, 1.5, 1.0)
            
            if 'roe' in data.columns:
                roe_condition = data['roe'] > 0.1
                fundamental_score = fundamental_score * np.where(roe_condition, 1.5, 1.0)
            
            # 合并所有评分 - 向量化
            total_scores = (market_cap_score + liquidity_score + 
                          volatility_score + volume_score + fundamental_score)
            
            processing_time = time.time() - start_time
            self.performance_metrics['vectorization_time'] += processing_time
            
            self.logger.end_operation(operation_name, success=True,
                                    rows=len(data),
                                    avg_score=total_scores.mean(),
                                    processing_time=processing_time)
            
            return total_scores
            
        except Exception as e:
            self.logger.log_exception(e,
                context={'data_shape': data.shape, 'criteria': criteria},
                error_code=ErrorCode.UNKNOWN_ERROR,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error=str(e))
            return pd.Series([50.0] * len(data), index=data.index)

    async def batch_process_stocks(self, stock_list: List[str], market: str,
                                 process_func, **kwargs) -> List[Any]:
        """分批处理股票列表"""
        operation_name = "batch_process_stocks"
        self.logger.start_operation(operation_name,
                                   total_stocks=len(stock_list),
                                   batch_size=self.batch_size,
                                   market=market)

        try:
            all_results = []
            total_batches = (len(stock_list) + self.batch_size - 1) // self.batch_size

            for batch_idx in range(total_batches):
                start_idx = batch_idx * self.batch_size
                end_idx = min(start_idx + self.batch_size, len(stock_list))
                batch_stocks = stock_list[start_idx:end_idx]

                self.logger.info(f"处理批次 {batch_idx + 1}/{total_batches}: "
                               f"{len(batch_stocks)} 只股票 ({start_idx}-{end_idx})")

                batch_start_time = time.time()

                try:
                    # 处理当前批次
                    batch_results = await process_func(batch_stocks, market, **kwargs)
                    all_results.extend(batch_results)

                    batch_time = time.time() - batch_start_time
                    self.logger.info(f"批次 {batch_idx + 1} 完成: "
                                   f"{len(batch_results)} 个结果, "
                                   f"耗时 {batch_time:.2f}秒")

                except Exception as e:
                    self.logger.log_exception(e,
                        context={'batch_idx': batch_idx, 'batch_stocks': batch_stocks},
                        error_code=ErrorCode.UNKNOWN_ERROR,
                        severity=ErrorSeverity.MEDIUM)
                    continue

                # 批次间短暂休息，避免过载
                if batch_idx < total_batches - 1:
                    await asyncio.sleep(0.1)

            self.logger.end_operation(operation_name, success=True,
                                    total_results=len(all_results),
                                    success_rate=len(all_results)/len(stock_list))

            return all_results

        except Exception as e:
            self.logger.log_exception(e,
                context={'stock_count': len(stock_list), 'market': market},
                error_code=ErrorCode.UNKNOWN_ERROR,
                severity=ErrorSeverity.HIGH)

            self.logger.end_operation(operation_name, success=False, error=str(e))
            return []

    async def get_stock_data_batch(self, stock_batch: List[str], market: str) -> List[Dict]:
        """获取一批股票数据"""
        results = []

        # 使用信号量控制并发
        semaphore = asyncio.Semaphore(self.max_workers)

        async def fetch_single_stock(symbol):
            async with semaphore:
                try:
                    # 这里调用实际的数据获取方法
                    # 为了演示，使用模拟数据
                    stock_data = await self._fetch_stock_data_mock(symbol, market)
                    return stock_data
                except Exception as e:
                    self.logger.debug(f"获取{symbol}数据失败: {e}")
                    return None

        # 并发获取批次内的所有股票
        tasks = [fetch_single_stock(symbol) for symbol in stock_batch]
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)

        # 过滤有效结果
        for result in batch_results:
            if result and not isinstance(result, Exception):
                results.append(result)

        return results

    async def _fetch_stock_data_mock(self, symbol: str, market: str) -> Optional[Dict]:
        """模拟股票数据获取（实际使用时替换为真实数据源）"""
        # 模拟网络延迟
        await asyncio.sleep(0.01)

        # 模拟数据
        return {
            'symbol': symbol,
            'market': market,
            'current_price': np.random.uniform(10, 200),
            'volume': np.random.randint(1000, 1000000),
            'market_cap': np.random.uniform(1e8, 1e12),
            'pe_ratio': np.random.uniform(5, 50),
            'pb_ratio': np.random.uniform(0.5, 10),
            'roe': np.random.uniform(-0.2, 0.3),
            'debt_ratio': np.random.uniform(0.1, 0.8),
            'rsi': np.random.uniform(20, 80),
            'ma_trend': np.random.choice(['上升', '下降', '震荡']),
            'liquidity_ratio': np.random.uniform(0.01, 0.1),
            'volatility': np.random.uniform(0.1, 0.8),
            'daily_volume': np.random.uniform(1e6, 1e9)
        }

    def rank_stocks_vectorized(self, data: pd.DataFrame, criteria: Dict) -> List[Dict]:
        """对股票进行排序和排名 - 向量化版本"""
        operation_name = "rank_stocks_vectorized"
        start_time = time.time()
        self.logger.start_operation(operation_name, rows=len(data))

        try:
            if data.empty:
                return []

            # 向量化计算综合评分
            data['total_score'] = self.calculate_total_score_vectorized(data, criteria)

            # 向量化排序
            ranked_data = data.sort_values('total_score', ascending=False).reset_index(drop=True)

            # 向量化生成排名
            ranked_data['rank'] = range(1, len(ranked_data) + 1)

            # 向量化计算投资建议
            ranked_data['recommendation'] = pd.cut(
                ranked_data['total_score'],
                bins=[-np.inf, 35, 50, 65, 80, np.inf],
                labels=['强烈卖出', '卖出', '持有', '买入', '强烈买入']
            )

            # 向量化计算风险等级
            if 'volatility' in ranked_data.columns and 'debt_ratio' in ranked_data.columns:
                risk_score = ranked_data['volatility'] * 0.6 + ranked_data['debt_ratio'] * 0.4
                ranked_data['risk_level'] = pd.cut(
                    risk_score,
                    bins=[-np.inf, 0.4, 0.6, np.inf],
                    labels=['低风险', '中等风险', '高风险']
                )
            else:
                ranked_data['risk_level'] = '中等风险'

            # 转换为字典列表（向量化）
            result_columns = [
                'rank', 'symbol', 'market', 'current_price', 'market_cap',
                'pe_ratio', 'pb_ratio', 'roe', 'total_score',
                'recommendation', 'risk_level'
            ]

            # 确保所有需要的列都存在
            for col in result_columns:
                if col not in ranked_data.columns:
                    if col == 'market':
                        ranked_data[col] = 'CN_A'  # 默认市场
                    elif col in ['current_price', 'market_cap', 'pe_ratio', 'pb_ratio', 'roe']:
                        ranked_data[col] = 0.0  # 数值型默认值
                    else:
                        ranked_data[col] = ''  # 字符串默认值

            # 向量化数据类型转换和格式化
            result_data = ranked_data[result_columns].copy()

            # 数值格式化
            numeric_columns = ['current_price', 'pe_ratio', 'pb_ratio', 'roe', 'total_score']
            for col in numeric_columns:
                if col in result_data.columns:
                    result_data[col] = result_data[col].round(2)

            # 转换为字典列表
            results = result_data.to_dict('records')

            processing_time = time.time() - start_time
            self.performance_metrics['vectorization_time'] += processing_time

            self.logger.end_operation(operation_name, success=True,
                                    input_rows=len(data),
                                    output_rows=len(results),
                                    processing_time=processing_time)

            return results

        except Exception as e:
            self.logger.log_exception(e,
                context={'data_shape': data.shape, 'criteria': criteria},
                error_code=ErrorCode.UNKNOWN_ERROR,
                severity=ErrorSeverity.HIGH)

            self.logger.end_operation(operation_name, success=False, error=str(e))
            return []

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        total_time = self.performance_metrics['processing_time']
        vectorization_time = self.performance_metrics['vectorization_time']
        data_fetch_time = self.performance_metrics['data_fetch_time']

        return {
            'total_stocks_processed': self.performance_metrics['total_stocks_processed'],
            'total_processing_time': total_time,
            'vectorization_time': vectorization_time,
            'data_fetch_time': data_fetch_time,
            'other_time': total_time - vectorization_time - data_fetch_time,
            'vectorization_ratio': vectorization_time / total_time if total_time > 0 else 0,
            'stocks_per_second': (self.performance_metrics['total_stocks_processed'] /
                                total_time if total_time > 0 else 0),
            'batch_size': self.batch_size,
            'max_workers': self.max_workers
        }

    def reset_performance_metrics(self):
        """重置性能指标"""
        self.performance_metrics = {
            'total_stocks_processed': 0,
            'processing_time': 0,
            'vectorization_time': 0,
            'data_fetch_time': 0
        }
    
    def calculate_total_score_vectorized(self, data: pd.DataFrame, criteria: Dict) -> pd.Series:
        """计算综合评分 - 向量化版本"""
        operation_name = "calculate_total_score_vectorized"
        start_time = time.time()
        self.logger.start_operation(operation_name, rows=len(data))
        
        try:
            if data.empty:
                return pd.Series(dtype=float)
            
            # 基本面评分 (40%) - 向量化
            fundamental_score = pd.Series(0.0, index=data.index)
            
            if 'pe_ratio' in data.columns:
                pe_condition = (data['pe_ratio'] > 0) & (data['pe_ratio'] < 25)
                fundamental_score += np.where(pe_condition, 0.3, 0.0)
            
            if 'pb_ratio' in data.columns:
                pb_condition = (data['pb_ratio'] > 0) & (data['pb_ratio'] < 3)
                fundamental_score += np.where(pb_condition, 0.2, 0.0)
            
            if 'roe' in data.columns:
                roe_condition = data['roe'] > 0.1
                fundamental_score += np.where(roe_condition, 0.3, 0.0)
            
            if 'debt_ratio' in data.columns:
                debt_condition = data['debt_ratio'] < 0.5
                fundamental_score += np.where(debt_condition, 0.2, 0.0)
            
            # 技术面评分 (30%) - 向量化
            technical_score = pd.Series(0.0, index=data.index)
            
            if 'rsi' in data.columns:
                rsi_condition = (data['rsi'] >= 30) & (data['rsi'] <= 70)
                technical_score += np.where(rsi_condition, 0.4, 0.0)
            
            if 'ma_trend' in data.columns:
                trend_condition = data['ma_trend'] == '上升'
                technical_score += np.where(trend_condition, 0.6, 0.0)
            
            # 流动性评分 (20%) - 向量化
            liquidity_score = pd.Series(0.0, index=data.index)
            if 'liquidity_ratio' in data.columns:
                liquidity_score = np.minimum(data['liquidity_ratio'] * 10, 1.0)
            
            # 融资融券评分 (10%) - 向量化
            margin_score = data.get('margin_score', pd.Series(0.0, index=data.index)) / 5.0
            
            # 合并所有评分 - 向量化
            total_scores = (fundamental_score * 0.4 + 
                          technical_score * 0.3 + 
                          liquidity_score * 0.2 + 
                          margin_score * 0.1) * 100
            
            processing_time = time.time() - start_time
            self.performance_metrics['vectorization_time'] += processing_time
            
            self.logger.end_operation(operation_name, success=True,
                                    rows=len(data),
                                    avg_score=total_scores.mean(),
                                    max_score=total_scores.max(),
                                    min_score=total_scores.min(),
                                    processing_time=processing_time)
            
            return total_scores
            
        except Exception as e:
            self.logger.log_exception(e,
                context={'data_shape': data.shape, 'criteria': criteria},
                error_code=ErrorCode.UNKNOWN_ERROR,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error=str(e))
            return pd.Series([50.0] * len(data), index=data.index)
