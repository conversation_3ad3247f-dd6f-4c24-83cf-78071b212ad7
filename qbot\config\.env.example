# 数据源API密钥配置示例
# 复制此文件为 .env 并填入真实的API密钥

# Finnhub API密钥
# 注册地址: https://finnhub.io/
QBOT_FINNHUB_API_KEY=your_finnhub_api_key_here
QBOT_FINNHUB_ENABLED=true

# Alpha Vantage API密钥
# 注册地址: https://www.alphavantage.co/support/#api-key
QBOT_ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
QBOT_ALPHA_VANTAGE_ENABLED=true

# IEX Cloud API密钥
# 注册地址: https://iexcloud.io/
QBOT_IEX_CLOUD_API_KEY=your_iex_cloud_api_key_here
QBOT_IEX_CLOUD_ENABLED=true

# 腾讯云API密钥（如果使用）
QBOT_TENCENT_API_KEY=your_tencent_api_key_here
QBOT_TENCENT_API_SECRET=your_tencent_api_secret_here
QBOT_TENCENT_ENABLED=true

# 新浪财经（通常不需要API密钥）
QBOT_SINA_ENABLED=true

# Yahoo Finance（通常不需要API密钥）
QBOT_YAHOO_ENABLED=true

# 东方财富（通常不需要API密钥）
QBOT_EASTMONEY_ENABLED=true

# 自定义基础URL（可选）
# QBOT_FINNHUB_BASE_URL=https://finnhub.io/api/v1
# QBOT_SINA_BASE_URL=http://hq.sinajs.cn
# QBOT_YAHOO_BASE_URL=https://query1.finance.yahoo.com

# 数据库配置（如果使用）
DATABASE_URL=sqlite:///qbot_data.db

# 日志级别
LOG_LEVEL=INFO

# 调试模式
DEBUG=false

# 缓存配置
REDIS_URL=redis://localhost:6379/0

# 其他配置
TIMEZONE=Asia/Shanghai
DEFAULT_MARKET=CN_A
