#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强版数据源管理器
实现依赖注入、策略模式和集中化配置
"""

from typing import Dict, Any, Optional, List
import asyncio
from qbot.config.data_source_config import config_manager
from qbot.data.data_source_interface import DataSourceManager, StockData
from qbot.common.logging.logger import LOGGER as logger


class EnhancedDataManager:
    """增强版数据源管理器"""
    
    def __init__(self, config_manager_instance=None):
        """
        初始化数据管理器
        
        Args:
            config_manager_instance: 配置管理器实例（依赖注入）
        """
        self.config_manager = config_manager_instance or config_manager
        self.data_source_manager = DataSourceManager(self.config_manager)
        self._initialized = False
    
    async def initialize(self):
        """异步初始化"""
        if self._initialized:
            return
        
        try:
            # 初始化数据源管理器
            self.data_source_manager.initialize()
            
            # 验证配置
            await self._validate_configuration()
            
            self._initialized = True
            logger.info("增强版数据管理器初始化完成")
            
        except Exception as e:
            logger.error(f"增强版数据管理器初始化失败: {e}")
            raise
    
    async def _validate_configuration(self):
        """验证配置"""
        try:
            summary = self.config_manager.get_summary()
            logger.info(f"数据源配置摘要: {summary}")
            
            # 检查每个市场是否有可用的数据源
            markets = ['CN_A', 'US', 'HK', 'UK', 'JP']
            for market in markets:
                sources = self.config_manager.get_sources_by_market(market)
                if sources:
                    logger.info(f"{market}市场可用数据源: {list(sources.keys())}")
                else:
                    logger.warning(f"{market}市场没有可用的数据源")
                    
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
    
    async def get_stock_data(self, symbol: str, market: str, 
                           strategy: str = 'priority') -> Optional[StockData]:
        """
        获取股票数据
        
        Args:
            symbol: 股票代码
            market: 市场类型
            strategy: 数据源选择策略 ('priority', 'fastest', 'most_reliable')
            
        Returns:
            标准化的股票数据
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            # 根据策略选择数据源
            preferred_sources = self._get_sources_by_strategy(market, strategy)
            
            # 获取数据
            data = await self.data_source_manager.get_stock_data(
                symbol, market, preferred_sources
            )
            
            if data:
                # 数据后处理
                data = self._post_process_data(data)
                logger.debug(f"成功获取{symbol}数据，来源: {data.source}")
                return data
            else:
                logger.warning(f"无法获取{symbol}数据")
                return None
                
        except Exception as e:
            logger.error(f"获取股票数据失败 {symbol}: {e}")
            return None
    
    def _get_sources_by_strategy(self, market: str, strategy: str) -> List[str]:
        """根据策略获取数据源优先级"""
        sources = self.config_manager.get_sources_by_market(market)
        
        if strategy == 'priority':
            # 按配置的优先级排序
            return list(sources.keys())
        
        elif strategy == 'fastest':
            # 按响应速度排序（这里简化为固定顺序）
            fast_order = {
                'CN_A': ['sina', 'tencent', 'eastmoney'],
                'US': ['yahoo', 'finnhub', 'alpha_vantage'],
                'HK': ['yahoo', 'finnhub'],
                'UK': ['yahoo', 'finnhub'],
                'JP': ['yahoo', 'finnhub']
            }
            preferred = fast_order.get(market, [])
            return [s for s in preferred if s in sources] + [s for s in sources if s not in preferred]
        
        elif strategy == 'most_reliable':
            # 按可靠性排序
            reliable_order = {
                'CN_A': ['eastmoney', 'sina', 'tencent'],
                'US': ['finnhub', 'alpha_vantage', 'yahoo'],
                'HK': ['finnhub', 'yahoo'],
                'UK': ['finnhub', 'yahoo'],
                'JP': ['finnhub', 'yahoo']
            }
            preferred = reliable_order.get(market, [])
            return [s for s in preferred if s in sources] + [s for s in sources if s not in preferred]
        
        else:
            return list(sources.keys())
    
    def _post_process_data(self, data: StockData) -> StockData:
        """数据后处理"""
        try:
            # 数据验证和清理
            if data.current_price <= 0:
                logger.warning(f"股票{data.symbol}价格异常: {data.current_price}")
            
            # 计算衍生指标
            if data.close_prev > 0 and data.current_price > 0:
                change = data.current_price - data.close_prev
                change_percent = (change / data.close_prev) * 100
                data.extra_data['price_change'] = round(change, 4)
                data.extra_data['price_change_percent'] = round(change_percent, 2)
            
            # 添加数据质量评分
            quality_score = self._calculate_data_quality(data)
            data.extra_data['quality_score'] = quality_score
            
            return data
            
        except Exception as e:
            logger.warning(f"数据后处理失败: {e}")
            return data
    
    def _calculate_data_quality(self, data: StockData) -> float:
        """计算数据质量评分"""
        try:
            score = 0.0
            
            # 基础价格数据完整性 (40%)
            if data.current_price > 0:
                score += 0.2
            if data.open_price > 0:
                score += 0.1
            if data.high_price > 0 and data.low_price > 0:
                score += 0.1
            
            # 成交量数据 (20%)
            if data.volume > 0:
                score += 0.2
            
            # 财务数据 (30%)
            if data.market_cap > 0:
                score += 0.1
            if data.pe_ratio > 0:
                score += 0.1
            if data.pb_ratio > 0:
                score += 0.1
            
            # 数据一致性 (10%)
            if (data.high_price >= data.current_price >= data.low_price and
                data.high_price >= data.open_price >= data.low_price):
                score += 0.1
            
            return min(score, 1.0)
            
        except Exception:
            return 0.5  # 默认评分
    
    async def get_multiple_stocks(self, symbols: List[str], market: str,
                                strategy: str = 'priority') -> Dict[str, Optional[StockData]]:
        """批量获取股票数据"""
        if not self._initialized:
            await self.initialize()
        
        try:
            # 并发获取数据
            tasks = [
                self.get_stock_data(symbol, market, strategy)
                for symbol in symbols
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 整理结果
            data_dict = {}
            for i, result in enumerate(results):
                symbol = symbols[i]
                if isinstance(result, Exception):
                    logger.error(f"获取{symbol}数据异常: {result}")
                    data_dict[symbol] = None
                else:
                    data_dict[symbol] = result
            
            return data_dict
            
        except Exception as e:
            logger.error(f"批量获取股票数据失败: {e}")
            return {symbol: None for symbol in symbols}
    
    def get_data_source_status(self) -> Dict[str, Any]:
        """获取数据源状态"""
        try:
            if not self._initialized:
                return {'error': '数据管理器未初始化'}
            
            status = self.data_source_manager.get_source_status()
            config_summary = self.config_manager.get_summary()
            
            return {
                'initialized': self._initialized,
                'source_status': status,
                'config_summary': config_summary,
                'available_strategies': ['priority', 'fastest', 'most_reliable']
            }
            
        except Exception as e:
            logger.error(f"获取数据源状态失败: {e}")
            return {'error': str(e)}
    
    def register_custom_source(self, name: str, source_class: type, 
                             config: Dict[str, Any]) -> bool:
        """注册自定义数据源"""
        try:
            if not self._initialized:
                logger.error("数据管理器未初始化，无法注册自定义数据源")
                return False
            
            instance = self.data_source_manager.register_custom_source(
                name, source_class, config
            )
            
            if instance:
                logger.info(f"成功注册自定义数据源: {name}")
                return True
            else:
                logger.error(f"注册自定义数据源失败: {name}")
                return False
                
        except Exception as e:
            logger.error(f"注册自定义数据源异常: {e}")
            return False
    
    async def test_data_sources(self, test_symbol: str = '000001', 
                              test_market: str = 'CN_A') -> Dict[str, Any]:
        """测试数据源连通性"""
        if not self._initialized:
            await self.initialize()
        
        test_results = {}
        sources = self.config_manager.get_sources_by_market(test_market)
        
        for source_name in sources:
            try:
                start_time = asyncio.get_event_loop().time()
                
                # 测试单个数据源
                data = await self.data_source_manager.get_stock_data(
                    test_symbol, test_market, [source_name]
                )
                
                end_time = asyncio.get_event_loop().time()
                response_time = round((end_time - start_time) * 1000, 2)  # 毫秒
                
                test_results[source_name] = {
                    'success': data is not None,
                    'response_time_ms': response_time,
                    'data_quality': self._calculate_data_quality(data) if data else 0.0,
                    'error': None
                }
                
            except Exception as e:
                test_results[source_name] = {
                    'success': False,
                    'response_time_ms': 0,
                    'data_quality': 0.0,
                    'error': str(e)
                }
        
        return test_results


# 全局增强版数据管理器实例
enhanced_data_manager = EnhancedDataManager()
