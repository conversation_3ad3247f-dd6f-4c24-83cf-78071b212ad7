#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据源配置管理系统
支持环境变量、配置文件和默认配置的层级加载
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from qbot.common.logging.logger import LOGGER as logger


@dataclass
class DataSourceConfig:
    """数据源配置类"""
    name: str
    enabled: bool = True
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    base_url: Optional[str] = None
    rate_limit: int = 60  # 每分钟请求数
    timeout: int = 10  # 超时时间（秒）
    retry_count: int = 3  # 重试次数
    priority: int = 1  # 优先级（数字越小优先级越高）
    markets: list = field(default_factory=list)  # 支持的市场
    extra_params: Dict[str, Any] = field(default_factory=dict)


class DataSourceConfigManager:
    """数据源配置管理器"""
    
    def __init__(self, config_dir: str = "qbot/config"):
        self.config_dir = Path(config_dir)
        self.config_file = self.config_dir / "data_sources.yaml"
        self.env_prefix = "QBOT_"
        
        # 默认配置
        self.default_configs = {
            'sina': DataSourceConfig(
                name='sina',
                enabled=True,
                base_url='http://hq.sinajs.cn',
                rate_limit=120,
                timeout=5,
                priority=1,
                markets=['CN_A']
            ),
            'tencent': DataSourceConfig(
                name='tencent',
                enabled=True,
                base_url='http://qt.gtimg.cn',
                rate_limit=100,
                timeout=5,
                priority=2,
                markets=['CN_A']
            ),
            'eastmoney': DataSourceConfig(
                name='eastmoney',
                enabled=True,
                base_url='http://push2.eastmoney.com',
                rate_limit=80,
                timeout=8,
                priority=3,
                markets=['CN_A']
            ),
            'finnhub': DataSourceConfig(
                name='finnhub',
                enabled=False,  # 需要API密钥
                base_url='https://finnhub.io/api/v1',
                rate_limit=60,
                timeout=10,
                priority=1,
                markets=['US', 'HK', 'UK', 'JP'],
                extra_params={'sandbox': False}
            ),
            'yahoo': DataSourceConfig(
                name='yahoo',
                enabled=True,
                base_url='https://query1.finance.yahoo.com',
                rate_limit=100,
                timeout=10,
                priority=2,
                markets=['US', 'HK', 'UK', 'JP']
            ),
            'alpha_vantage': DataSourceConfig(
                name='alpha_vantage',
                enabled=False,  # 需要API密钥
                base_url='https://www.alphavantage.co/query',
                rate_limit=5,  # 免费版限制
                timeout=15,
                priority=3,
                markets=['US']
            ),
            'iex_cloud': DataSourceConfig(
                name='iex_cloud',
                enabled=False,  # 需要API密钥
                base_url='https://cloud.iexapis.com/stable',
                rate_limit=100,
                timeout=10,
                priority=2,
                markets=['US']
            )
        }
        
        self.configs = {}
        self.load_configs()
    
    def load_configs(self):
        """加载配置（优先级：环境变量 > 配置文件 > 默认配置）"""
        try:
            # 1. 从默认配置开始
            self.configs = self.default_configs.copy()
            
            # 2. 加载配置文件
            self._load_from_file()
            
            # 3. 加载环境变量
            self._load_from_env()
            
            # 4. 验证和启用配置
            self._validate_configs()
            
            logger.info("数据源配置加载完成")
            
        except Exception as e:
            logger.error(f"加载数据源配置失败: {e}")
            # 使用默认配置
            self.configs = self.default_configs.copy()
    
    def _load_from_file(self):
        """从配置文件加载"""
        if not self.config_file.exists():
            logger.info(f"配置文件不存在: {self.config_file}")
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                file_config = yaml.safe_load(f)
            
            if not file_config or 'data_sources' not in file_config:
                return
            
            for source_name, source_config in file_config['data_sources'].items():
                if source_name in self.configs:
                    # 更新现有配置
                    config = self.configs[source_name]
                    for key, value in source_config.items():
                        if hasattr(config, key):
                            setattr(config, key, value)
                else:
                    # 创建新配置
                    self.configs[source_name] = DataSourceConfig(
                        name=source_name,
                        **source_config
                    )
            
            logger.info(f"从配置文件加载了 {len(file_config['data_sources'])} 个数据源配置")
            
        except Exception as e:
            logger.error(f"读取配置文件失败: {e}")
    
    def _load_from_env(self):
        """从环境变量加载"""
        env_loaded = 0
        
        for source_name in self.configs:
            # API密钥
            api_key_env = f"{self.env_prefix}{source_name.upper()}_API_KEY"
            if api_key_env in os.environ:
                self.configs[source_name].api_key = os.environ[api_key_env]
                env_loaded += 1
            
            # API密钥（备用名称）
            api_secret_env = f"{self.env_prefix}{source_name.upper()}_API_SECRET"
            if api_secret_env in os.environ:
                self.configs[source_name].api_secret = os.environ[api_secret_env]
                env_loaded += 1
            
            # 启用状态
            enabled_env = f"{self.env_prefix}{source_name.upper()}_ENABLED"
            if enabled_env in os.environ:
                self.configs[source_name].enabled = os.environ[enabled_env].lower() in ('true', '1', 'yes')
                env_loaded += 1
            
            # 基础URL
            base_url_env = f"{self.env_prefix}{source_name.upper()}_BASE_URL"
            if base_url_env in os.environ:
                self.configs[source_name].base_url = os.environ[base_url_env]
                env_loaded += 1
        
        if env_loaded > 0:
            logger.info(f"从环境变量加载了 {env_loaded} 个配置项")
    
    def _validate_configs(self):
        """验证和启用配置"""
        for source_name, config in self.configs.items():
            # 检查需要API密钥的数据源
            if source_name in ['finnhub', 'alpha_vantage', 'iex_cloud']:
                if not config.api_key:
                    config.enabled = False
                    logger.warning(f"{source_name} 缺少API密钥，已禁用")
                else:
                    config.enabled = True
                    logger.info(f"{source_name} API密钥已配置，已启用")
    
    def get_config(self, source_name: str) -> Optional[DataSourceConfig]:
        """获取指定数据源配置"""
        return self.configs.get(source_name)
    
    def get_enabled_sources(self, market: str = None) -> Dict[str, DataSourceConfig]:
        """获取启用的数据源配置"""
        enabled = {}
        for name, config in self.configs.items():
            if config.enabled:
                if market is None or market in config.markets:
                    enabled[name] = config
        return enabled
    
    def get_sources_by_market(self, market: str) -> Dict[str, DataSourceConfig]:
        """按市场获取数据源配置（按优先级排序）"""
        sources = {}
        for name, config in self.configs.items():
            if config.enabled and market in config.markets:
                sources[name] = config
        
        # 按优先级排序
        return dict(sorted(sources.items(), key=lambda x: x[1].priority))
    
    def is_source_available(self, source_name: str) -> bool:
        """检查数据源是否可用"""
        config = self.get_config(source_name)
        return config is not None and config.enabled
    
    def save_config(self):
        """保存配置到文件"""
        try:
            # 确保配置目录存在
            self.config_dir.mkdir(parents=True, exist_ok=True)
            
            # 准备配置数据
            config_data = {
                'data_sources': {}
            }
            
            for name, config in self.configs.items():
                config_data['data_sources'][name] = {
                    'enabled': config.enabled,
                    'base_url': config.base_url,
                    'rate_limit': config.rate_limit,
                    'timeout': config.timeout,
                    'retry_count': config.retry_count,
                    'priority': config.priority,
                    'markets': config.markets,
                    'extra_params': config.extra_params
                }
                
                # 不保存敏感信息到文件
                # API密钥应该通过环境变量设置
            
            # 写入文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置已保存到: {self.config_file}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def get_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        summary = {
            'total_sources': len(self.configs),
            'enabled_sources': len([c for c in self.configs.values() if c.enabled]),
            'sources_by_market': {},
            'sources_with_api_key': []
        }
        
        # 按市场统计
        for config in self.configs.values():
            if config.enabled:
                for market in config.markets:
                    if market not in summary['sources_by_market']:
                        summary['sources_by_market'][market] = []
                    summary['sources_by_market'][market].append(config.name)
        
        # 有API密钥的数据源
        for name, config in self.configs.items():
            if config.api_key:
                summary['sources_with_api_key'].append(name)
        
        return summary


# 全局配置管理器实例
config_manager = DataSourceConfigManager()
