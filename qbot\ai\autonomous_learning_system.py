#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI自主学习系统
每日休市后自动分析涨停跌停股票手法，发现新策略，验证后加入模型库
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import json
import pickle
import threading
import time
from collections import defaultdict, deque
import warnings
warnings.filterwarnings('ignore')

try:
    from sklearn.cluster import DBSCAN, KMeans
    from sklearn.preprocessing import StandardScaler
    from sklearn.decomposition import PCA
    from sklearn.ensemble import RandomForestClassifier, IsolationForest
    from sklearn.metrics import classification_report, accuracy_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠️ scikit-learn未安装，将使用简化版本")

logger = logging.getLogger(__name__)

@dataclass
class TradingPattern:
    """交易模式"""
    pattern_id: str
    pattern_name: str
    pattern_type: str          # LIMIT_UP, LIMIT_DOWN, BREAKOUT, REVERSAL
    discovery_date: datetime
    confidence_score: float    # 0-1
    success_rate: float       # 历史成功率
    sample_count: int         # 样本数量
    features: Dict[str, Any]  # 特征描述
    conditions: List[str]     # 触发条件
    signals: List[str]        # 交易信号
    risk_level: str          # LOW, MEDIUM, HIGH
    validation_status: str    # DISCOVERED, TESTING, VALIDATED, REJECTED
    performance_metrics: Dict[str, float]
    metadata: Dict[str, Any]

@dataclass
class MarketBehaviorSignature:
    """市场行为特征"""
    signature_id: str
    main_force_id: str        # 主力资金标识
    behavior_type: str        # 操盘手法类型
    time_pattern: List[str]   # 时间模式
    volume_pattern: List[float]  # 成交量模式
    price_pattern: List[float]   # 价格模式
    order_pattern: Dict[str, Any]  # 订单模式
    frequency: int           # 出现频率
    stocks_involved: List[str]  # 涉及股票
    last_seen: datetime
    confidence: float

class PatternDiscoveryEngine:
    """模式发现引擎"""
    
    def __init__(self, params: Dict[str, Any] = None):
        self.params = params or {
            'min_samples': 5,              # 最小样本数
            'confidence_threshold': 0.7,    # 置信度阈值
            'success_rate_threshold': 0.6,  # 成功率阈值
            'clustering_eps': 0.5,          # DBSCAN聚类参数
            'min_cluster_size': 3,          # 最小聚类大小
            'feature_importance_threshold': 0.1,  # 特征重要性阈值
            'validation_period': 30,        # 验证期（天）
            'max_patterns_per_day': 10      # 每日最大发现模式数
        }
        
        self.discovered_patterns = {}    # 已发现模式
        self.testing_patterns = {}       # 测试中模式
        self.validated_patterns = {}     # 已验证模式
        self.rejected_patterns = {}      # 已拒绝模式
        self.behavior_signatures = {}    # 行为特征库
        
        self.daily_analysis_results = []
        self.pattern_performance_tracker = defaultdict(list)
        
    def analyze_daily_extremes(self, date: datetime, market_data: Dict[str, pd.DataFrame]) -> List[TradingPattern]:
        """分析每日涨停跌停股票"""
        try:
            logger.info(f"开始分析 {date.strftime('%Y-%m-%d')} 的极端股票")
            
            discovered_patterns = []
            
            # 筛选涨停跌停股票
            limit_up_stocks = self._find_limit_up_stocks(market_data)
            limit_down_stocks = self._find_limit_down_stocks(market_data)
            
            logger.info(f"发现涨停股票: {len(limit_up_stocks)}只, 跌停股票: {len(limit_down_stocks)}只")
            
            # 分析涨停股票模式
            if limit_up_stocks:
                limit_up_patterns = self._analyze_limit_up_patterns(limit_up_stocks, market_data)
                discovered_patterns.extend(limit_up_patterns)
                
            # 分析跌停股票模式
            if limit_down_stocks:
                limit_down_patterns = self._analyze_limit_down_patterns(limit_down_stocks, market_data)
                discovered_patterns.extend(limit_down_patterns)
                
            # 分析异常波动模式
            abnormal_patterns = self._analyze_abnormal_patterns(market_data)
            discovered_patterns.extend(abnormal_patterns)
            
            # 主力行为识别
            main_force_patterns = self._identify_main_force_behaviors(market_data)
            discovered_patterns.extend(main_force_patterns)
            
            # 过滤和验证新发现的模式
            new_patterns = self._filter_new_patterns(discovered_patterns)
            
            logger.info(f"发现新模式: {len(new_patterns)}个")
            
            # 保存分析结果
            self.daily_analysis_results.append({
                'date': date,
                'limit_up_count': len(limit_up_stocks),
                'limit_down_count': len(limit_down_stocks),
                'patterns_discovered': len(new_patterns),
                'patterns': new_patterns
            })
            
            return new_patterns
            
        except Exception as e:
            logger.error(f"每日极端分析失败: {e}")
            return []
            
    def _find_limit_up_stocks(self, market_data: Dict[str, pd.DataFrame]) -> List[str]:
        """寻找涨停股票"""
        limit_up_stocks = []
        
        try:
            for symbol, data in market_data.items():
                if data.empty:
                    continue
                    
                latest = data.iloc[-1]
                
                # 涨停判断条件
                change_pct = latest.get('change_pct', 0)
                volume_ratio = latest.get('volume_ratio', 1)
                
                # 涨停条件：涨幅接近10%，成交量放大
                if change_pct >= 9.5 and volume_ratio > 2.0:
                    limit_up_stocks.append(symbol)
                    
        except Exception as e:
            logger.error(f"寻找涨停股票失败: {e}")
            
        return limit_up_stocks
        
    def _find_limit_down_stocks(self, market_data: Dict[str, pd.DataFrame]) -> List[str]:
        """寻找跌停股票"""
        limit_down_stocks = []
        
        try:
            for symbol, data in market_data.items():
                if data.empty:
                    continue
                    
                latest = data.iloc[-1]
                
                # 跌停判断条件
                change_pct = latest.get('change_pct', 0)
                volume_ratio = latest.get('volume_ratio', 1)
                
                # 跌停条件：跌幅接近-10%
                if change_pct <= -9.5:
                    limit_down_stocks.append(symbol)
                    
        except Exception as e:
            logger.error(f"寻找跌停股票失败: {e}")
            
        return limit_down_stocks
        
    def _analyze_limit_up_patterns(self, limit_up_stocks: List[str], 
                                 market_data: Dict[str, pd.DataFrame]) -> List[TradingPattern]:
        """分析涨停股票模式"""
        patterns = []
        
        try:
            # 提取涨停股票特征
            features_data = []
            stock_features = {}
            
            for symbol in limit_up_stocks:
                if symbol not in market_data or market_data[symbol].empty:
                    continue
                    
                data = market_data[symbol]
                features = self._extract_stock_features(symbol, data)
                
                if features:
                    features_data.append(features)
                    stock_features[symbol] = features
                    
            if len(features_data) < self.params['min_samples']:
                return patterns
                
            # 聚类分析发现模式
            if SKLEARN_AVAILABLE:
                patterns = self._cluster_analysis_patterns(features_data, stock_features, 'LIMIT_UP')
            else:
                patterns = self._simple_pattern_analysis(features_data, stock_features, 'LIMIT_UP')
                
        except Exception as e:
            logger.error(f"分析涨停模式失败: {e}")
            
        return patterns
        
    def _analyze_limit_down_patterns(self, limit_down_stocks: List[str], 
                                   market_data: Dict[str, pd.DataFrame]) -> List[TradingPattern]:
        """分析跌停股票模式"""
        patterns = []
        
        try:
            # 提取跌停股票特征
            features_data = []
            stock_features = {}
            
            for symbol in limit_down_stocks:
                if symbol not in market_data or market_data[symbol].empty:
                    continue
                    
                data = market_data[symbol]
                features = self._extract_stock_features(symbol, data)
                
                if features:
                    features_data.append(features)
                    stock_features[symbol] = features
                    
            if len(features_data) < self.params['min_samples']:
                return patterns
                
            # 聚类分析发现模式
            if SKLEARN_AVAILABLE:
                patterns = self._cluster_analysis_patterns(features_data, stock_features, 'LIMIT_DOWN')
            else:
                patterns = self._simple_pattern_analysis(features_data, stock_features, 'LIMIT_DOWN')
                
        except Exception as e:
            logger.error(f"分析跌停模式失败: {e}")
            
        return patterns
        
    def _extract_stock_features(self, symbol: str, data: pd.DataFrame) -> Dict[str, float]:
        """提取股票特征"""
        try:
            if len(data) < 20:
                return {}
                
            latest = data.iloc[-1]
            recent_5 = data.tail(5)
            recent_20 = data.tail(20)
            
            features = {
                # 价格特征
                'change_pct': latest.get('change_pct', 0),
                'volume_ratio': latest.get('volume_ratio', 1),
                'turnover_rate': latest.get('turnover_rate', 0),
                
                # 技术指标特征
                'rsi': self._calculate_rsi(data['close'].values),
                'macd_signal': self._calculate_macd_signal(data['close'].values),
                'bollinger_position': self._calculate_bollinger_position(data['close'].values),
                
                # 成交量特征
                'volume_trend': recent_5['volume'].mean() / recent_20['volume'].mean() if recent_20['volume'].mean() > 0 else 1,
                'volume_spike': latest.get('volume', 0) / recent_20['volume'].mean() if recent_20['volume'].mean() > 0 else 1,
                
                # 价格动量特征
                'price_momentum_5': (latest['close'] - recent_5.iloc[0]['close']) / recent_5.iloc[0]['close'] if recent_5.iloc[0]['close'] > 0 else 0,
                'price_momentum_20': (latest['close'] - recent_20.iloc[0]['close']) / recent_20.iloc[0]['close'] if recent_20.iloc[0]['close'] > 0 else 0,
                
                # 波动率特征
                'volatility_5': recent_5['close'].pct_change().std(),
                'volatility_20': recent_20['close'].pct_change().std(),
                
                # 资金流特征
                'main_inflow_ratio': latest.get('main_inflow_ratio', 0),
                'retail_inflow_ratio': latest.get('retail_inflow_ratio', 0),
                
                # 时间特征
                'hour': latest.get('datetime', datetime.now()).hour if 'datetime' in latest else 14,
                'minute': latest.get('datetime', datetime.now()).minute if 'datetime' in latest else 30,
                
                # 市场特征
                'market_cap': latest.get('market_cap', 0),
                'pe_ratio': latest.get('pe_ratio', 0),
                'sector_performance': latest.get('sector_performance', 0)
            }
            
            return features
            
        except Exception as e:
            logger.error(f"提取股票特征失败: {e}")
            return {}
            
    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """计算RSI"""
        try:
            if len(prices) < period + 1:
                return 50.0

            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])

            # 处理零除错误和边界情况
            if np.isnan(avg_loss) or avg_loss == 0:
                # 如果没有损失，说明价格一直上涨
                return 100.0 if (not np.isnan(avg_gain) and avg_gain > 0) else 50.0

            if np.isnan(avg_gain) or avg_gain == 0:
                # 如果没有收益，说明价格一直下跌
                return 0.0

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            # 确保RSI在合理范围内
            rsi = max(0.0, min(100.0, rsi))

            return float(rsi) if not np.isnan(rsi) else 50.0

        except (ZeroDivisionError, ValueError) as e:
            logger.warning(f"RSI计算失败: {e}")
            return 50.0
        except Exception as e:
            logger.error(f"RSI计算异常: {e}")
            return 50.0
            
    def _calculate_macd_signal(self, prices: np.ndarray) -> float:
        """计算MACD信号"""
        try:
            if len(prices) < 26:
                return 0.0
                
            ema12 = self._calculate_ema(prices, 12)
            ema26 = self._calculate_ema(prices, 26)
            
            macd_line = ema12 - ema26
            signal_line = self._calculate_ema(np.array([macd_line]), 9)
            
            return macd_line - signal_line
            
        except:
            return 0.0
            
    def _calculate_ema(self, prices: np.ndarray, period: int) -> float:
        """计算指数移动平均"""
        try:
            if len(prices) < period:
                return np.mean(prices)
                
            alpha = 2 / (period + 1)
            ema = prices[0]
            
            for price in prices[1:]:
                ema = alpha * price + (1 - alpha) * ema
                
            return ema
            
        except:
            return np.mean(prices) if len(prices) > 0 else 0.0
            
    def _calculate_bollinger_position(self, prices: np.ndarray, period: int = 20) -> float:
        """计算布林带位置"""
        try:
            if len(prices) < period:
                return 0.5
                
            recent_prices = prices[-period:]
            mean_price = np.mean(recent_prices)
            std_price = np.std(recent_prices)
            
            if std_price == 0:
                return 0.5
                
            current_price = prices[-1]
            upper_band = mean_price + 2 * std_price
            lower_band = mean_price - 2 * std_price
            
            # 返回在布林带中的位置 (0-1)
            position = (current_price - lower_band) / (upper_band - lower_band)
            return max(0, min(1, position))
            
        except:
            return 0.5

    def _cluster_analysis_patterns(self, features_data: List[Dict[str, float]],
                                 stock_features: Dict[str, Dict[str, float]],
                                 pattern_type: str) -> List[TradingPattern]:
        """聚类分析发现模式"""
        patterns = []

        try:
            if not SKLEARN_AVAILABLE or len(features_data) < self.params['min_cluster_size']:
                return patterns

            # 准备特征矩阵
            feature_names = list(features_data[0].keys())
            X = np.array([[features[name] for name in feature_names] for features in features_data])

            # 标准化特征
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # DBSCAN聚类
            clustering = DBSCAN(eps=self.params['clustering_eps'],
                              min_samples=self.params['min_cluster_size'])
            cluster_labels = clustering.fit_predict(X_scaled)

            # 分析每个聚类
            unique_labels = set(cluster_labels)

            for label in unique_labels:
                if label == -1:  # 噪声点
                    continue

                cluster_mask = cluster_labels == label
                cluster_features = X[cluster_mask]
                cluster_stocks = [list(stock_features.keys())[i] for i, mask in enumerate(cluster_mask) if mask]

                if len(cluster_stocks) >= self.params['min_cluster_size']:
                    pattern = self._create_pattern_from_cluster(
                        cluster_features, cluster_stocks, feature_names, pattern_type, label
                    )
                    if pattern:
                        patterns.append(pattern)

        except Exception as e:
            logger.error(f"聚类分析失败: {e}")

        return patterns

    def _create_pattern_from_cluster(self, cluster_features: np.ndarray,
                                   cluster_stocks: List[str],
                                   feature_names: List[str],
                                   pattern_type: str,
                                   cluster_id: int) -> Optional[TradingPattern]:
        """从聚类创建交易模式"""
        try:
            # 计算聚类中心特征
            center_features = np.mean(cluster_features, axis=0)
            feature_std = np.std(cluster_features, axis=0)

            # 识别重要特征
            important_features = {}
            conditions = []
            signals = []

            for i, name in enumerate(feature_names):
                value = center_features[i]
                std = feature_std[i]

                # 特征重要性判断
                if abs(value) > self.params['feature_importance_threshold']:
                    important_features[name] = {
                        'mean': float(value),
                        'std': float(std),
                        'range': [float(value - std), float(value + std)]
                    }

                    # 生成条件和信号
                    if name == 'change_pct':
                        if pattern_type == 'LIMIT_UP':
                            conditions.append(f"涨幅 >= {value-std:.1f}%")
                            signals.append("强势突破信号")
                        elif pattern_type == 'LIMIT_DOWN':
                            conditions.append(f"跌幅 <= {value+std:.1f}%")
                            signals.append("弱势破位信号")

                    elif name == 'volume_ratio':
                        if value > 2.0:
                            conditions.append(f"量比 >= {value-std:.1f}")
                            signals.append("放量突破")

                    elif name == 'rsi':
                        if value > 70:
                            conditions.append(f"RSI >= {value-std:.1f}")
                            signals.append("超买信号")
                        elif value < 30:
                            conditions.append(f"RSI <= {value+std:.1f}")
                            signals.append("超卖信号")

                    elif name == 'main_inflow_ratio':
                        if value > 0.1:
                            conditions.append(f"主力流入比例 >= {value-std:.2f}")
                            signals.append("主力资金流入")

            # 计算置信度
            confidence_score = min(len(cluster_stocks) / 10, 1.0)  # 样本数量影响置信度

            # 风险等级评估
            risk_level = self._assess_risk_level(important_features, pattern_type)

            # 创建模式
            pattern_id = f"{pattern_type}_{cluster_id}_{datetime.now().strftime('%Y%m%d')}"
            pattern_name = f"{pattern_type}模式_{cluster_id}"

            pattern = TradingPattern(
                pattern_id=pattern_id,
                pattern_name=pattern_name,
                pattern_type=pattern_type,
                discovery_date=datetime.now(),
                confidence_score=confidence_score,
                success_rate=0.0,  # 初始成功率，需要验证
                sample_count=len(cluster_stocks),
                features=important_features,
                conditions=conditions,
                signals=signals,
                risk_level=risk_level,
                validation_status='DISCOVERED',
                performance_metrics={},
                metadata={
                    'cluster_id': cluster_id,
                    'sample_stocks': cluster_stocks,
                    'discovery_method': 'DBSCAN_CLUSTERING'
                }
            )

            return pattern

        except Exception as e:
            logger.error(f"创建模式失败: {e}")
            return None

    def _assess_risk_level(self, features: Dict[str, Any], pattern_type: str) -> str:
        """评估风险等级"""
        try:
            risk_score = 0

            # 基于特征评估风险
            if 'volatility_5' in features:
                vol = features['volatility_5']['mean']
                if vol > 0.05:
                    risk_score += 2
                elif vol > 0.03:
                    risk_score += 1

            if 'volume_ratio' in features:
                vol_ratio = features['volume_ratio']['mean']
                if vol_ratio > 5:
                    risk_score += 2
                elif vol_ratio > 3:
                    risk_score += 1

            if pattern_type in ['LIMIT_UP', 'LIMIT_DOWN']:
                risk_score += 2  # 涨跌停本身就是高风险

            # 风险等级判断
            if risk_score >= 4:
                return 'HIGH'
            elif risk_score >= 2:
                return 'MEDIUM'
            else:
                return 'LOW'

        except:
            return 'MEDIUM'

    def _identify_main_force_behaviors(self, market_data: Dict[str, pd.DataFrame]) -> List[TradingPattern]:
        """识别主力操盘行为"""
        patterns = []

        try:
            # 主力行为特征分析
            main_force_stocks = []
            behavior_features = {}

            for symbol, data in market_data.items():
                if data.empty or len(data) < 20:
                    continue

                # 识别主力行为特征
                behavior_signature = self._analyze_main_force_signature(symbol, data)

                if behavior_signature and behavior_signature['confidence'] > 0.7:
                    main_force_stocks.append(symbol)
                    behavior_features[symbol] = behavior_signature

            if len(main_force_stocks) >= self.params['min_samples']:
                # 按行为类型分组
                behavior_groups = defaultdict(list)

                for symbol, signature in behavior_features.items():
                    behavior_type = signature['behavior_type']
                    behavior_groups[behavior_type].append(symbol)

                # 为每种行为类型创建模式
                for behavior_type, stocks in behavior_groups.items():
                    if len(stocks) >= self.params['min_samples']:
                        pattern = self._create_main_force_pattern(behavior_type, stocks, behavior_features)
                        if pattern:
                            patterns.append(pattern)

        except Exception as e:
            logger.error(f"识别主力行为失败: {e}")

        return patterns

    def _analyze_main_force_signature(self, symbol: str, data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """分析主力操盘特征"""
        try:
            if len(data) < 20:
                return None

            recent_data = data.tail(20)
            latest = data.iloc[-1]

            # 主力资金流向分析
            main_inflow = recent_data.get('main_inflow', pd.Series([0]*len(recent_data)))
            volume_pattern = recent_data['volume'].values
            price_pattern = recent_data['close'].pct_change().values[1:]

            # 识别操盘手法
            behavior_type = self._classify_trading_behavior(main_inflow, volume_pattern, price_pattern)

            # 计算行为置信度
            confidence = self._calculate_behavior_confidence(main_inflow, volume_pattern, price_pattern, behavior_type)

            # 时间模式分析
            time_pattern = self._analyze_time_pattern(data)

            signature = {
                'behavior_type': behavior_type,
                'confidence': confidence,
                'main_inflow_pattern': main_inflow.tolist(),
                'volume_pattern': volume_pattern.tolist(),
                'price_pattern': price_pattern.tolist(),
                'time_pattern': time_pattern,
                'avg_main_inflow': main_inflow.mean(),
                'volume_consistency': np.std(volume_pattern) / np.mean(volume_pattern) if np.mean(volume_pattern) > 0 else 0,
                'price_momentum': np.mean(price_pattern)
            }

            return signature

        except Exception as e:
            logger.error(f"分析主力特征失败: {e}")
            return None

    def _classify_trading_behavior(self, main_inflow: pd.Series,
                                 volume_pattern: np.ndarray,
                                 price_pattern: np.ndarray) -> str:
        """分类交易行为"""
        try:
            avg_inflow = main_inflow.mean()
            inflow_trend = np.polyfit(range(len(main_inflow)), main_inflow.values, 1)[0]

            volume_trend = np.polyfit(range(len(volume_pattern)), volume_pattern, 1)[0]
            price_trend = np.mean(price_pattern)

            # 行为分类逻辑
            if avg_inflow > 0 and inflow_trend > 0 and price_trend > 0:
                if volume_trend > 0:
                    return "AGGRESSIVE_ACCUMULATION"  # 激进建仓
                else:
                    return "STEALTH_ACCUMULATION"     # 隐蔽建仓

            elif avg_inflow < 0 and inflow_trend < 0 and price_trend < 0:
                if volume_trend > 0:
                    return "AGGRESSIVE_DISTRIBUTION"  # 激进派发
                else:
                    return "STEALTH_DISTRIBUTION"     # 隐蔽派发

            elif abs(avg_inflow) < 1000000 and abs(price_trend) < 0.01:
                return "SIDEWAYS_MANIPULATION"       # 横盘控盘

            elif avg_inflow > 0 and price_trend < 0:
                return "BOTTOM_FISHING"              # 抄底行为

            elif avg_inflow < 0 and price_trend > 0:
                return "TOP_DISTRIBUTION"            # 顶部派发

            else:
                return "MIXED_BEHAVIOR"              # 混合行为

        except:
            return "UNKNOWN"

    def _calculate_behavior_confidence(self, main_inflow: pd.Series,
                                     volume_pattern: np.ndarray,
                                     price_pattern: np.ndarray,
                                     behavior_type: str) -> float:
        """计算行为置信度"""
        try:
            confidence = 0.5  # 基础置信度

            # 资金流一致性
            inflow_consistency = len([x for x in main_inflow if (x > 0) == (main_inflow.mean() > 0)]) / len(main_inflow)
            confidence += inflow_consistency * 0.3

            # 价格趋势一致性
            price_consistency = len([x for x in price_pattern if (x > 0) == (np.mean(price_pattern) > 0)]) / len(price_pattern)
            confidence += price_consistency * 0.2

            # 行为类型特定加成
            if behavior_type in ["AGGRESSIVE_ACCUMULATION", "AGGRESSIVE_DISTRIBUTION"]:
                if np.mean(volume_pattern) > np.median(volume_pattern) * 1.5:
                    confidence += 0.1

            elif behavior_type in ["STEALTH_ACCUMULATION", "STEALTH_DISTRIBUTION"]:
                if np.std(volume_pattern) / np.mean(volume_pattern) < 0.5:  # 成交量稳定
                    confidence += 0.1

            return min(confidence, 1.0)

        except:
            return 0.5

    def _analyze_time_pattern(self, data: pd.DataFrame) -> List[str]:
        """分析时间模式"""
        try:
            time_patterns = []

            if 'datetime' in data.columns:
                # 分析交易时间分布
                hours = data['datetime'].dt.hour

                morning_volume = data[(hours >= 9) & (hours < 12)]['volume'].sum()
                afternoon_volume = data[(hours >= 13) & (hours < 16)]['volume'].sum()

                if morning_volume > afternoon_volume * 1.5:
                    time_patterns.append("MORNING_ACTIVE")
                elif afternoon_volume > morning_volume * 1.5:
                    time_patterns.append("AFTERNOON_ACTIVE")
                else:
                    time_patterns.append("BALANCED_TRADING")

                # 分析开盘收盘特征
                opening_changes = data.groupby(data['datetime'].dt.date)['change_pct'].first()
                closing_changes = data.groupby(data['datetime'].dt.date)['change_pct'].last()

                if opening_changes.mean() > 0.02:
                    time_patterns.append("STRONG_OPENING")
                if closing_changes.mean() > 0.02:
                    time_patterns.append("STRONG_CLOSING")

            return time_patterns

        except:
            return ["UNKNOWN_PATTERN"]
