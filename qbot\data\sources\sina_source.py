#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
新浪财经数据源实现
"""

import aiohttp
import asyncio
from typing import Optional
from qbot.data.data_source_interface import IDataSource, StockData
from qbot.common.logging.logger import LOGGER as logger


class SinaDataSource(IDataSource):
    """新浪财经数据源"""
    
    def __init__(self, name: str, config: dict):
        super().__init__(name, config)
        self.base_url = config.get('base_url', 'http://hq.sinajs.cn')
    
    def supports_market(self, market: str) -> bool:
        """检查是否支持指定市场"""
        return market in ['CN_A']
    
    def is_available(self) -> bool:
        """检查数据源是否可用"""
        return self.enabled and 'CN_A' in self.supported_markets
    
    async def get_stock_data(self, symbol: str, market: str) -> Optional[StockData]:
        """获取股票数据"""
        if not self.supports_market(market):
            return None
        
        try:
            return await self._make_request_with_retry(self._fetch_sina_data, symbol)
        except Exception as e:
            logger.error(f"新浪财经获取{symbol}数据失败: {e}")
            return None
    
    async def _fetch_sina_data(self, symbol: str) -> Optional[StockData]:
        """从新浪财经获取数据"""
        try:
            # 处理股票代码格式
            if symbol.startswith('6'):
                full_code = f"sh{symbol}"
            elif symbol.startswith(('0', '3')):
                full_code = f"sz{symbol}"
            else:
                full_code = symbol
            
            url = f"{self.base_url}/list={full_code}"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        return None
                    
                    content = await response.text(encoding='gbk')
                    
                    if 'var hq_str_' not in content:
                        return None
                    
                    # 解析数据
                    data_str = content.split('"')[1]
                    if not data_str:
                        return None
                    
                    fields = data_str.split(',')
                    if len(fields) < 32:
                        return None
                    
                    # 构建标准化数据
                    stock_data = StockData(
                        symbol=symbol,
                        market='CN_A',
                        name=fields[0],
                        open_price=float(fields[1]) if fields[1] else 0.0,
                        close_prev=float(fields[2]) if fields[2] else 0.0,
                        current_price=float(fields[3]) if fields[3] else 0.0,
                        high_price=float(fields[4]) if fields[4] else 0.0,
                        low_price=float(fields[5]) if fields[5] else 0.0,
                        volume=int(fields[8]) if fields[8] else 0,
                        amount=float(fields[9]) if fields[9] else 0.0,
                        timestamp=self.last_request_time,
                        source='sina'
                    )
                    
                    return stock_data
                    
        except Exception as e:
            logger.error(f"新浪财经数据解析失败: {e}")
            return None
