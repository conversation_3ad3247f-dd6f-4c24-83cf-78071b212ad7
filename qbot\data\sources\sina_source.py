#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
新浪财经数据源实现
"""

import aiohttp
import asyncio
from typing import Optional
from qbot.data.data_source_interface import IDataSource, StockData
from qbot.common.result import Result, ErrorCode, ErrorSeverity, success, failure, from_exception
from qbot.common.enhanced_logger import get_logger


class SinaDataSource(IDataSource):
    """新浪财经数据源"""
    
    def __init__(self, name: str, config: dict):
        super().__init__(name, config)
        self.base_url = config.get('base_url', 'http://hq.sinajs.cn')
    
    def supports_market(self, market: str) -> bool:
        """检查是否支持指定市场"""
        return market in ['CN_A']
    
    def is_available(self) -> bool:
        """检查数据源是否可用"""
        return self.enabled and 'CN_A' in self.supported_markets
    
    async def get_stock_data(self, symbol: str, market: str) -> Result[StockData]:
        """获取股票数据"""
        logger = get_logger(f"data_source.{self.name}")

        if not self.supports_market(market):
            return failure(
                ErrorCode.MARKET_NOT_SUPPORTED,
                f"新浪财经不支持市场: {market}",
                ErrorSeverity.MEDIUM,
                self.name,
                {'symbol': symbol, 'market': market}
            )

        try:
            result = await self._make_request_with_retry(self._fetch_sina_data, symbol)
            if result.is_success():
                return result
            else:
                return result  # 返回包含错误信息的结果

        except Exception as e:
            logger.log_exception(e,
                context={'symbol': symbol, 'market': market},
                error_code=ErrorCode.DATA_SOURCE_UNAVAILABLE,
                severity=ErrorSeverity.HIGH)
            return from_exception(e, ErrorCode.DATA_SOURCE_UNAVAILABLE, ErrorSeverity.HIGH, self.name)
    
    async def _fetch_sina_data(self, symbol: str) -> StockData:
        """从新浪财经获取数据"""
        logger = get_logger(f"data_source.{self.name}")

        # 处理股票代码格式
        if symbol.startswith('6'):
            full_code = f"sh{symbol}"
        elif symbol.startswith(('0', '3')):
            full_code = f"sz{symbol}"
        else:
            full_code = symbol

        url = f"{self.base_url}/list={full_code}"

        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
            async with session.get(url) as response:
                if response.status != 200:
                    raise Exception(f"HTTP错误: {response.status}")

                content = await response.text(encoding='gbk')

                if 'var hq_str_' not in content:
                    raise Exception("响应中没有找到股票数据")

                # 解析数据
                data_str = content.split('"')[1]
                if not data_str:
                    raise Exception("股票数据为空")

                fields = data_str.split(',')
                if len(fields) < 32:
                    raise Exception(f"数据字段不完整，期望32个字段，实际{len(fields)}个")

                # 构建标准化数据
                stock_data = StockData(
                    symbol=symbol,
                    market='CN_A',
                    name=fields[0],
                    open_price=float(fields[1]) if fields[1] else 0.0,
                    close_prev=float(fields[2]) if fields[2] else 0.0,
                    current_price=float(fields[3]) if fields[3] else 0.0,
                    high_price=float(fields[4]) if fields[4] else 0.0,
                    low_price=float(fields[5]) if fields[5] else 0.0,
                    volume=int(fields[8]) if fields[8] else 0,
                    amount=float(fields[9]) if fields[9] else 0.0,
                    timestamp=self.last_request_time,
                    source='sina'
                )

                # 数据验证
                if stock_data.current_price <= 0:
                    raise Exception(f"股票价格异常: {stock_data.current_price}")

                logger.debug(f"成功解析新浪财经数据: {symbol}",
                           symbol=symbol, price=stock_data.current_price)

                return stock_data
