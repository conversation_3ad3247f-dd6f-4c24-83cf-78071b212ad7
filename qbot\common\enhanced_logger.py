#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强的日志记录系统
支持结构化日志、错误追踪和性能监控
"""

import logging
import json
import time
import traceback
from typing import Dict, Any, Optional, Union
from datetime import datetime
from pathlib import Path
from qbot.common.result import ErrorInfo, ErrorCode, ErrorSeverity


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        # 基础日志信息
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__ if record.exc_info[0] else None,
                'message': str(record.exc_info[1]) if record.exc_info[1] else None,
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # 添加自定义字段
        if hasattr(record, 'extra_data'):
            log_data.update(record.extra_data)
        
        return json.dumps(log_data, ensure_ascii=False, default=str)


class EnhancedLogger:
    """增强的日志记录器"""
    
    def __init__(self, name: str, log_dir: str = "logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 创建logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加handler
        if not self.logger.handlers:
            self._setup_handlers()
        
        # 性能监控
        self._operation_times = {}
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器（普通日志）
        file_handler = logging.FileHandler(
            self.log_dir / f"{self.name}.log",
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # 结构化日志处理器
        structured_handler = logging.FileHandler(
            self.log_dir / f"{self.name}_structured.jsonl",
            encoding='utf-8'
        )
        structured_handler.setLevel(logging.DEBUG)
        structured_handler.setFormatter(StructuredFormatter())
        self.logger.addHandler(structured_handler)
        
        # 错误日志处理器
        error_handler = logging.FileHandler(
            self.log_dir / f"{self.name}_errors.log",
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        self.logger.addHandler(error_handler)
    
    def _log_with_extra(self, level: int, message: str, 
                       extra_data: Optional[Dict[str, Any]] = None,
                       exc_info: bool = False):
        """带额外数据的日志记录"""
        if extra_data:
            # 创建LogRecord并添加额外数据
            record = self.logger.makeRecord(
                self.logger.name, level, "", 0, message, (), exc_info
            )
            record.extra_data = extra_data
            self.logger.handle(record)
        else:
            self.logger.log(level, message, exc_info=exc_info)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self._log_with_extra(logging.DEBUG, message, kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self._log_with_extra(logging.INFO, message, kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self._log_with_extra(logging.WARNING, message, kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self._log_with_extra(logging.ERROR, message, kwargs, exc_info=True)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self._log_with_extra(logging.CRITICAL, message, kwargs, exc_info=True)
    
    def log_exception(self, exception: Exception, 
                     context: Optional[Dict[str, Any]] = None,
                     error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
                     severity: ErrorSeverity = ErrorSeverity.MEDIUM):
        """记录异常日志"""
        extra_data = {
            'error_code': error_code.value,
            'severity': severity.value,
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'context': context or {}
        }
        
        self._log_with_extra(
            logging.ERROR,
            f"异常发生: {type(exception).__name__}: {str(exception)}",
            extra_data,
            exc_info=True
        )
    
    def log_error_info(self, error_info: ErrorInfo):
        """记录ErrorInfo对象"""
        extra_data = {
            'error_code': error_info.code.value,
            'severity': error_info.severity.value,
            'source': error_info.source,
            'context': error_info.context,
            'details': error_info.details
        }
        
        level = self._severity_to_log_level(error_info.severity)
        self._log_with_extra(level, error_info.message, extra_data)
    
    def _severity_to_log_level(self, severity: ErrorSeverity) -> int:
        """将错误严重程度转换为日志级别"""
        mapping = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }
        return mapping.get(severity, logging.WARNING)
    
    def start_operation(self, operation_name: str, **context):
        """开始操作计时"""
        start_time = time.time()
        self._operation_times[operation_name] = start_time
        
        self.debug(f"开始操作: {operation_name}", 
                  operation=operation_name, 
                  start_time=start_time,
                  **context)
        
        return start_time
    
    def end_operation(self, operation_name: str, success: bool = True, **context):
        """结束操作计时"""
        end_time = time.time()
        start_time = self._operation_times.pop(operation_name, end_time)
        duration = end_time - start_time
        
        extra_data = {
            'operation': operation_name,
            'duration_seconds': round(duration, 4),
            'success': success,
            'start_time': start_time,
            'end_time': end_time,
            **context
        }
        
        if success:
            self.info(f"操作完成: {operation_name} (耗时: {duration:.4f}秒)", **extra_data)
        else:
            self.warning(f"操作失败: {operation_name} (耗时: {duration:.4f}秒)", **extra_data)
        
        return duration
    
    def log_api_request(self, url: str, method: str = "GET", 
                       status_code: Optional[int] = None,
                       response_time: Optional[float] = None,
                       **kwargs):
        """记录API请求日志"""
        extra_data = {
            'api_url': url,
            'http_method': method,
            'status_code': status_code,
            'response_time_ms': round(response_time * 1000, 2) if response_time else None,
            **kwargs
        }
        
        if status_code and 200 <= status_code < 300:
            self.debug(f"API请求成功: {method} {url}", **extra_data)
        elif status_code and status_code >= 400:
            self.warning(f"API请求失败: {method} {url} (状态码: {status_code})", **extra_data)
        else:
            self.debug(f"API请求: {method} {url}", **extra_data)
    
    def log_data_quality(self, symbol: str, market: str, 
                        quality_score: float, issues: Optional[list] = None):
        """记录数据质量日志"""
        extra_data = {
            'symbol': symbol,
            'market': market,
            'quality_score': quality_score,
            'issues': issues or []
        }
        
        if quality_score >= 0.8:
            self.debug(f"数据质量良好: {symbol} (评分: {quality_score})", **extra_data)
        elif quality_score >= 0.5:
            self.warning(f"数据质量一般: {symbol} (评分: {quality_score})", **extra_data)
        else:
            self.error(f"数据质量较差: {symbol} (评分: {quality_score})", **extra_data)
    
    def log_performance_metrics(self, metrics: Dict[str, Any]):
        """记录性能指标"""
        self.info("性能指标", **metrics)


# 日志管理器
class LoggerManager:
    """日志管理器"""
    
    _loggers: Dict[str, EnhancedLogger] = {}
    
    @classmethod
    def get_logger(cls, name: str) -> EnhancedLogger:
        """获取或创建日志记录器"""
        if name not in cls._loggers:
            cls._loggers[name] = EnhancedLogger(name)
        return cls._loggers[name]
    
    @classmethod
    def get_data_source_logger(cls, source_name: str) -> EnhancedLogger:
        """获取数据源专用日志记录器"""
        return cls.get_logger(f"data_source.{source_name}")
    
    @classmethod
    def get_screener_logger(cls) -> EnhancedLogger:
        """获取股票筛选器日志记录器"""
        return cls.get_logger("stock_screener")
    
    @classmethod
    def get_trading_logger(cls) -> EnhancedLogger:
        """获取交易日志记录器"""
        return cls.get_logger("trading")


# 便捷函数
def get_logger(name: str) -> EnhancedLogger:
    """获取日志记录器的便捷函数"""
    return LoggerManager.get_logger(name)
