#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
质量感知的股票筛选器
集成数据质量管理的股票筛选系统
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import asyncio
from dataclasses import dataclass

from qbot.config.constants import MarketType, DATA_QUALITY, SCREENING
from qbot.data.data_quality_manager import DataQualityManager, DataQualityReport
from qbot.agents.refactored_stock_screener import (
    RefactoredStockScreener, IStockDataProvider, StockListProvider
)
from qbot.common.result import Result, ErrorCode, ErrorSeverity, success, failure
from qbot.common.enhanced_logger import get_logger


@dataclass
class QualityAwareScreeningResult:
    """质量感知筛选结果"""
    recommendations: List[Dict[str, Any]]
    quality_summary: Dict[str, Any]
    data_source_performance: Dict[str, Dict[str, float]]
    quality_filtered_count: int
    total_processed_count: int
    avg_data_quality: float


class QualityAwareDataProvider(IStockDataProvider):
    """质量感知的数据提供者"""
    
    def __init__(self, base_provider: IStockDataProvider):
        self.base_provider = base_provider
        self.quality_manager = DataQualityManager()
        self.logger = get_logger("quality_aware_provider")
        
        # 数据源优先级（质量越高优先级越高）
        self.source_priority = {
            'primary': 1.0,
            'secondary': 0.8,
            'backup': 0.6,
            'fallback': 0.4
        }
    
    async def get_stock_data(self, symbol: str, market: MarketType) -> Result[Dict[str, Any]]:
        """获取单只股票数据并进行质量处理"""
        operation_name = f"get_quality_stock_data_{symbol}"
        self.logger.start_operation(operation_name, symbol=symbol, market=market.value)
        
        try:
            # 1. 从基础提供者获取原始数据
            raw_result = await self.base_provider.get_stock_data(symbol, market)
            
            if not raw_result.is_success():
                self.logger.end_operation(operation_name, success=False, 
                                        error="base_provider_failed")
                return raw_result
            
            raw_data = raw_result.data
            source = raw_data.get('source', 'unknown')
            
            # 2. 进行质量处理
            quality_result = self.quality_manager.process_stock_data(
                raw_data, source, market, symbol
            )
            
            if quality_result.is_success():
                processed_data = quality_result.data
                quality_report = quality_result.metadata.get('quality_report')
                
                # 3. 添加质量元数据
                processed_data['data_quality_score'] = quality_report.overall_score
                processed_data['quality_issues_count'] = len(quality_report.issues)
                processed_data['is_quality_verified'] = True
                
                self.logger.end_operation(operation_name, success=True,
                                        quality_score=quality_report.overall_score,
                                        issues_count=len(quality_report.issues))
                
                result = success(processed_data)
                result.add_metadata('quality_report', quality_report)
                return result
            else:
                self.logger.end_operation(operation_name, success=False, 
                                        error="quality_processing_failed")
                return quality_result
                
        except Exception as e:
            self.logger.log_exception(e,
                context={'symbol': symbol, 'market': market.value},
                error_code=ErrorCode.DATA_QUALITY_LOW,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error=str(e))
            return failure(ErrorCode.DATA_QUALITY_LOW, 
                         f"获取{symbol}质量数据失败: {str(e)}")
    
    async def get_batch_stock_data(self, symbols: List[str], market: MarketType) -> Result[List[Dict[str, Any]]]:
        """批量获取股票数据并进行质量处理"""
        operation_name = f"get_batch_quality_data_{market.value}"
        self.logger.start_operation(operation_name, 
                                   symbol_count=len(symbols),
                                   market=market.value)
        
        try:
            # 1. 从基础提供者获取原始数据
            raw_result = await self.base_provider.get_batch_stock_data(symbols, market)
            
            if not raw_result.is_success():
                return raw_result
            
            raw_data_list = raw_result.data
            
            # 2. 并行进行质量处理
            quality_processed_data = []
            quality_reports = []
            failed_count = 0
            
            for raw_data in raw_data_list:
                symbol = raw_data.get('symbol', 'UNKNOWN')
                source = raw_data.get('source', 'unknown')
                
                try:
                    quality_result = self.quality_manager.process_stock_data(
                        raw_data, source, market, symbol
                    )
                    
                    if quality_result.is_success():
                        processed_data = quality_result.data
                        quality_report = quality_result.metadata.get('quality_report')
                        
                        # 添加质量元数据
                        processed_data['data_quality_score'] = quality_report.overall_score
                        processed_data['quality_issues_count'] = len(quality_report.issues)
                        processed_data['is_quality_verified'] = True
                        
                        quality_processed_data.append(processed_data)
                        quality_reports.append(quality_report)
                    else:
                        failed_count += 1
                        self.logger.warning(f"股票{symbol}质量处理失败: {quality_result.get_error_message()}")
                        
                except Exception as e:
                    failed_count += 1
                    self.logger.warning(f"股票{symbol}质量处理异常: {e}")
            
            # 3. 计算质量统计
            success_rate = len(quality_processed_data) / len(symbols) if symbols else 0
            avg_quality = (sum(data['data_quality_score'] for data in quality_processed_data) / 
                          len(quality_processed_data) if quality_processed_data else 0)
            
            self.logger.end_operation(operation_name, success=True,
                                    input_count=len(symbols),
                                    output_count=len(quality_processed_data),
                                    failed_count=failed_count,
                                    success_rate=success_rate,
                                    avg_quality=avg_quality)
            
            result = success(quality_processed_data)
            result.add_metadata('success_rate', success_rate)
            result.add_metadata('failed_count', failed_count)
            result.add_metadata('avg_quality', avg_quality)
            result.add_metadata('quality_reports', quality_reports)
            
            return result
            
        except Exception as e:
            self.logger.log_exception(e,
                context={'symbol_count': len(symbols), 'market': market.value},
                error_code=ErrorCode.DATA_QUALITY_LOW,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error=str(e))
            return failure(ErrorCode.DATA_QUALITY_LOW, 
                         f"批量获取质量数据失败: {str(e)}")


class QualityAwareStockScreener:
    """质量感知的股票筛选器"""
    
    def __init__(self, base_data_provider: IStockDataProvider):
        self.logger = get_logger("quality_aware_screener")
        
        # 使用质量感知的数据提供者
        self.quality_provider = QualityAwareDataProvider(base_data_provider)
        
        # 基础筛选器
        self.base_screener = RefactoredStockScreener(self.quality_provider)
        
        # 质量管理器
        self.quality_manager = self.quality_provider.quality_manager
    
    async def screen_stocks_with_quality_control(
        self, 
        market: MarketType, 
        criteria: Dict,
        limit: Optional[int] = None,
        min_quality_score: float = DATA_QUALITY.MIN_QUALITY_SCORE,
        quality_weight: float = 0.1
    ) -> Result[QualityAwareScreeningResult]:
        """执行质量感知的股票筛选"""
        operation_name = f"quality_aware_screening_{market.value}"
        self.logger.start_operation(operation_name, 
                                   market=market.value,
                                   limit=limit,
                                   min_quality_score=min_quality_score)
        
        try:
            # 1. 执行基础筛选
            base_result = await self.base_screener.screen_stocks(market, criteria, limit)
            
            if not base_result.is_success():
                return base_result
            
            base_recommendations = base_result.data
            
            # 2. 应用质量过滤
            quality_filtered_recommendations = self._apply_quality_filter(
                base_recommendations, min_quality_score
            )
            
            # 3. 调整评分（考虑数据质量）
            quality_adjusted_recommendations = self._adjust_scores_by_quality(
                quality_filtered_recommendations, quality_weight
            )
            
            # 4. 重新排序
            final_recommendations = sorted(
                quality_adjusted_recommendations,
                key=lambda x: x.get('adjusted_total_score', x.get('total_score', 0)),
                reverse=True
            )
            
            # 更新排名
            for i, rec in enumerate(final_recommendations, 1):
                rec['rank'] = i
            
            # 5. 生成质量摘要
            quality_summary = self._generate_quality_summary(base_recommendations)
            
            # 6. 获取数据源性能统计
            source_performance = self.quality_manager.get_source_quality_stats()
            
            # 7. 创建结果
            screening_result = QualityAwareScreeningResult(
                recommendations=final_recommendations,
                quality_summary=quality_summary,
                data_source_performance=source_performance,
                quality_filtered_count=len(base_recommendations) - len(quality_filtered_recommendations),
                total_processed_count=len(base_recommendations),
                avg_data_quality=quality_summary.get('avg_quality_score', 0)
            )
            
            self.logger.end_operation(operation_name, success=True,
                                    base_count=len(base_recommendations),
                                    quality_filtered_count=len(quality_filtered_recommendations),
                                    final_count=len(final_recommendations),
                                    avg_quality=screening_result.avg_data_quality)
            
            return success(screening_result)
            
        except Exception as e:
            self.logger.log_exception(e,
                context={'market': market.value, 'limit': limit},
                error_code=ErrorCode.UNKNOWN_ERROR,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error=str(e))
            return failure(ErrorCode.UNKNOWN_ERROR, 
                         f"质量感知筛选失败: {str(e)}")
    
    def _apply_quality_filter(self, recommendations: List[Dict[str, Any]], 
                            min_quality_score: float) -> List[Dict[str, Any]]:
        """应用质量过滤"""
        filtered = []
        
        for rec in recommendations:
            quality_score = rec.get('data_quality_score', 0)
            
            if quality_score >= min_quality_score:
                filtered.append(rec)
            else:
                self.logger.debug(f"股票{rec.get('symbol', 'UNKNOWN')}因质量评分{quality_score:.3f}低于阈值{min_quality_score}被过滤")
        
        return filtered
    
    def _adjust_scores_by_quality(self, recommendations: List[Dict[str, Any]], 
                                quality_weight: float) -> List[Dict[str, Any]]:
        """根据数据质量调整评分"""
        for rec in recommendations:
            original_score = rec.get('total_score', 50)
            quality_score = rec.get('data_quality_score', 0.5)
            
            # 质量调整：高质量数据获得加分，低质量数据被扣分
            quality_adjustment = (quality_score - 0.5) * 100 * quality_weight
            adjusted_score = original_score + quality_adjustment
            
            # 确保评分在合理范围内
            adjusted_score = max(0, min(100, adjusted_score))
            
            rec['quality_adjustment'] = quality_adjustment
            rec['adjusted_total_score'] = adjusted_score
        
        return recommendations
    
    def _generate_quality_summary(self, recommendations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成质量摘要"""
        if not recommendations:
            return {'total_count': 0}
        
        quality_scores = [rec.get('data_quality_score', 0) for rec in recommendations]
        issues_counts = [rec.get('quality_issues_count', 0) for rec in recommendations]
        
        # 质量分级统计
        excellent_count = sum(1 for score in quality_scores if score >= DATA_QUALITY.EXCELLENT_QUALITY_SCORE)
        good_count = sum(1 for score in quality_scores if DATA_QUALITY.GOOD_QUALITY_SCORE <= score < DATA_QUALITY.EXCELLENT_QUALITY_SCORE)
        acceptable_count = sum(1 for score in quality_scores if DATA_QUALITY.MIN_QUALITY_SCORE <= score < DATA_QUALITY.GOOD_QUALITY_SCORE)
        poor_count = sum(1 for score in quality_scores if score < DATA_QUALITY.MIN_QUALITY_SCORE)
        
        return {
            'total_count': len(recommendations),
            'avg_quality_score': sum(quality_scores) / len(quality_scores),
            'min_quality_score': min(quality_scores),
            'max_quality_score': max(quality_scores),
            'avg_issues_count': sum(issues_counts) / len(issues_counts),
            'quality_distribution': {
                'excellent': excellent_count,
                'good': good_count,
                'acceptable': acceptable_count,
                'poor': poor_count
            },
            'quality_percentages': {
                'excellent': excellent_count / len(recommendations) * 100,
                'good': good_count / len(recommendations) * 100,
                'acceptable': acceptable_count / len(recommendations) * 100,
                'poor': poor_count / len(recommendations) * 100
            }
        }
    
    def get_quality_insights(self) -> Dict[str, Any]:
        """获取质量洞察"""
        quality_summary = self.quality_manager.get_quality_summary()
        source_stats = self.quality_manager.get_source_quality_stats()
        
        # 分析最佳和最差数据源
        best_source = None
        worst_source = None
        best_score = 0
        worst_score = 1
        
        for source, stats in source_stats.items():
            score = stats['avg_quality_score']
            if score > best_score:
                best_score = score
                best_source = source
            if score < worst_score:
                worst_score = score
                worst_source = source
        
        # 生成改进建议
        improvement_suggestions = []
        
        if quality_summary.get('avg_quality_score', 0) < DATA_QUALITY.GOOD_QUALITY_SCORE:
            improvement_suggestions.append("整体数据质量偏低，建议加强数据源监控")
        
        if quality_summary.get('usable_rate', 0) < 0.9:
            improvement_suggestions.append("数据可用率偏低，建议优化数据清洗流程")
        
        if worst_source and worst_score < DATA_QUALITY.MIN_QUALITY_SCORE:
            improvement_suggestions.append(f"数据源'{worst_source}'质量过低，建议考虑替换")
        
        return {
            'overall_summary': quality_summary,
            'source_performance': source_stats,
            'best_source': {'name': best_source, 'score': best_score},
            'worst_source': {'name': worst_source, 'score': worst_score},
            'improvement_suggestions': improvement_suggestions
        }
