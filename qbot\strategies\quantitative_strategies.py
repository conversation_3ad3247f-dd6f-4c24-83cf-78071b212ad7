"""
量化交易策略模块
包含各种常用的量化交易策略和技术指标
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import logging

# 使用pandas-ta替代talib
try:
    import pandas_ta as ta
    HAS_PANDAS_TA = True
    print("✅ 使用pandas-ta进行技术指标计算")
except ImportError:
    HAS_PANDAS_TA = False
    print("警告: 未安装pandas-ta库，将使用简化的技术指标计算")

logger = logging.getLogger(__name__)

# 简化的技术指标实现（当没有talib时使用）
def simple_sma(data, period):
    """简单移动平均"""
    return pd.Series(data).rolling(window=period).mean().values

def simple_ema(data, period):
    """指数移动平均"""
    return pd.Series(data).ewm(span=period).mean().values

def simple_rsi(data, period=14):
    """相对强弱指数"""
    close = pd.Series(data)
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

    # 处理零除错误
    rs = gain / loss.replace(0, np.nan)  # 将0替换为NaN避免零除

    # 处理特殊情况
    rs = rs.fillna(100)  # 如果loss为0，设置rs为一个大值，使RSI接近100

    rsi = 100 - (100 / (1 + rs))

    # 确保RSI在合理范围内
    rsi = rsi.clip(0, 100)

    return rsi.values

def simple_macd(data, fast=12, slow=26, signal=9):
    """MACD指标"""
    close = pd.Series(data)
    ema_fast = close.ewm(span=fast).mean()
    ema_slow = close.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    macd_signal = macd.ewm(span=signal).mean()
    macd_hist = macd - macd_signal
    return macd.values, macd_signal.values, macd_hist.values

def simple_bollinger_bands(data, period=20, std_dev=2):
    """布林带"""
    close = pd.Series(data)
    sma = close.rolling(window=period).mean()
    std = close.rolling(window=period).std()
    upper = sma + (std * std_dev)
    lower = sma - (std * std_dev)
    return upper.values, sma.values, lower.values

def simple_stoch(high, low, close, k_period=14, d_period=3):
    """随机指标"""
    high_series = pd.Series(high)
    low_series = pd.Series(low)
    close_series = pd.Series(close)

    lowest_low = low_series.rolling(window=k_period).min()
    highest_high = high_series.rolling(window=k_period).max()

    k_percent = 100 * ((close_series - lowest_low) / (highest_high - lowest_low))
    d_percent = k_percent.rolling(window=d_period).mean()

    return k_percent.values, d_percent.values

class SignalType(Enum):
    """交易信号类型"""
    BUY = "买入"
    SELL = "卖出"
    HOLD = "持有"
    STRONG_BUY = "强烈买入"
    STRONG_SELL = "强烈卖出"

@dataclass
class TradingSignal:
    """交易信号数据结构"""
    signal_type: SignalType
    confidence: float  # 信号强度 0-1
    price: float
    timestamp: str
    strategy_name: str
    reason: str
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None

class QuantitativeStrategy:
    """量化策略基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.signals = []
        
    def calculate_indicators(self, data: pd.DataFrame) -> Dict:
        """计算技术指标"""
        raise NotImplementedError
        
    def generate_signals(self, data: pd.DataFrame) -> List[TradingSignal]:
        """生成交易信号"""
        raise NotImplementedError

class MACDStrategy(QuantitativeStrategy):
    """MACD策略"""
    
    def __init__(self, fast_period=12, slow_period=26, signal_period=9):
        super().__init__("MACD策略")
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        
    def calculate_indicators(self, data: pd.DataFrame) -> Dict:
        """计算MACD指标"""
        if HAS_PANDAS_TA:
            # 使用pandas-ta计算MACD
            macd_result = data.ta.macd(
                fast=self.fast_period,
                slow=self.slow_period,
                signal=self.signal_period
            )
            macd = macd_result[f'MACD_{self.fast_period}_{self.slow_period}_{self.signal_period}'].values
            macd_signal = macd_result[f'MACDs_{self.fast_period}_{self.slow_period}_{self.signal_period}'].values
            macd_hist = macd_result[f'MACDh_{self.fast_period}_{self.slow_period}_{self.signal_period}'].values
        else:
            # 使用简化实现
            close = data['close'].values
            macd, macd_signal, macd_hist = simple_macd(
                close,
                fast=self.fast_period,
                slow=self.slow_period,
                signal=self.signal_period
            )

        return {
            'macd': macd,
            'macd_signal': macd_signal,
            'macd_hist': macd_hist
        }
        
    def generate_signals(self, data: pd.DataFrame) -> List[TradingSignal]:
        """生成MACD交易信号"""
        indicators = self.calculate_indicators(data)
        signals = []
        
        macd = indicators['macd']
        macd_signal = indicators['macd_signal']
        macd_hist = indicators['macd_hist']
        
        for i in range(1, len(data)):
            if np.isnan(macd[i]) or np.isnan(macd_signal[i]):
                continue
                
            current_price = data.iloc[i]['close']
            timestamp = str(data.index[i])
            
            # MACD金叉买入信号
            if (macd[i] > macd_signal[i] and macd[i-1] <= macd_signal[i-1] and 
                macd[i] > 0):
                confidence = min(abs(macd[i] - macd_signal[i]) / current_price * 1000, 1.0)
                signals.append(TradingSignal(
                    signal_type=SignalType.BUY,
                    confidence=confidence,
                    price=current_price,
                    timestamp=timestamp,
                    strategy_name=self.name,
                    reason="MACD金叉，趋势向上",
                    stop_loss=current_price * 0.95,
                    take_profit=current_price * 1.08
                ))
                
            # MACD死叉卖出信号
            elif (macd[i] < macd_signal[i] and macd[i-1] >= macd_signal[i-1] and 
                  macd[i] < 0):
                confidence = min(abs(macd[i] - macd_signal[i]) / current_price * 1000, 1.0)
                signals.append(TradingSignal(
                    signal_type=SignalType.SELL,
                    confidence=confidence,
                    price=current_price,
                    timestamp=timestamp,
                    strategy_name=self.name,
                    reason="MACD死叉，趋势向下",
                    stop_loss=current_price * 1.05,
                    take_profit=current_price * 0.92
                ))
                
        return signals

class RSIStrategy(QuantitativeStrategy):
    """RSI策略"""
    
    def __init__(self, period=14, oversold=30, overbought=70):
        super().__init__("RSI策略")
        self.period = period
        self.oversold = oversold
        self.overbought = overbought
        
    def calculate_indicators(self, data: pd.DataFrame) -> Dict:
        """计算RSI指标"""
        if HAS_PANDAS_TA:
            # 使用pandas-ta计算RSI
            rsi = data.ta.rsi(length=self.period).values
        else:
            # 使用简化实现
            close = data['close'].values
            rsi = simple_rsi(close, period=self.period)

        return {'rsi': rsi}
        
    def generate_signals(self, data: pd.DataFrame) -> List[TradingSignal]:
        """生成RSI交易信号"""
        indicators = self.calculate_indicators(data)
        signals = []
        
        rsi = indicators['rsi']
        
        for i in range(1, len(data)):
            if np.isnan(rsi[i]):
                continue
                
            current_price = data.iloc[i]['close']
            timestamp = str(data.index[i])
            
            # RSI超卖买入信号
            if rsi[i] < self.oversold and rsi[i-1] >= self.oversold:
                confidence = (self.oversold - rsi[i]) / self.oversold
                signals.append(TradingSignal(
                    signal_type=SignalType.BUY,
                    confidence=confidence,
                    price=current_price,
                    timestamp=timestamp,
                    strategy_name=self.name,
                    reason=f"RSI超卖反弹，RSI={rsi[i]:.2f}",
                    stop_loss=current_price * 0.96,
                    take_profit=current_price * 1.06
                ))
                
            # RSI超买卖出信号
            elif rsi[i] > self.overbought and rsi[i-1] <= self.overbought:
                confidence = (rsi[i] - self.overbought) / (100 - self.overbought)
                signals.append(TradingSignal(
                    signal_type=SignalType.SELL,
                    confidence=confidence,
                    price=current_price,
                    timestamp=timestamp,
                    strategy_name=self.name,
                    reason=f"RSI超买回调，RSI={rsi[i]:.2f}",
                    stop_loss=current_price * 1.04,
                    take_profit=current_price * 0.94
                ))
                
        return signals

class BollingerBandsStrategy(QuantitativeStrategy):
    """布林带策略"""
    
    def __init__(self, period=20, std_dev=2):
        super().__init__("布林带策略")
        self.period = period
        self.std_dev = std_dev
        
    def calculate_indicators(self, data: pd.DataFrame) -> Dict:
        """计算布林带指标"""
        close = data['close'].values

        if HAS_PANDAS_TA:
            # 使用pandas-ta计算布林带
            bb_result = data.ta.bbands(length=self.period, std=self.std_dev)
            upper = bb_result[f'BBU_{self.period}_{self.std_dev}'].values
            middle = bb_result[f'BBM_{self.period}_{self.std_dev}'].values
            lower = bb_result[f'BBL_{self.period}_{self.std_dev}'].values
        else:
            # 使用简化实现
            upper, middle, lower = simple_bollinger_bands(
                close,
                period=self.period,
                std_dev=self.std_dev
            )

        return {
            'bb_upper': upper,
            'bb_middle': middle,
            'bb_lower': lower
        }
        
    def generate_signals(self, data: pd.DataFrame) -> List[TradingSignal]:
        """生成布林带交易信号"""
        indicators = self.calculate_indicators(data)
        signals = []
        
        bb_upper = indicators['bb_upper']
        bb_middle = indicators['bb_middle']
        bb_lower = indicators['bb_lower']
        
        for i in range(1, len(data)):
            if np.isnan(bb_upper[i]) or np.isnan(bb_lower[i]):
                continue
                
            current_price = data.iloc[i]['close']
            prev_price = data.iloc[i-1]['close']
            timestamp = str(data.index[i])
            
            # 价格触及下轨买入信号
            if (current_price <= bb_lower[i] and prev_price > bb_lower[i-1]):
                confidence = (bb_lower[i] - current_price) / bb_lower[i]
                signals.append(TradingSignal(
                    signal_type=SignalType.BUY,
                    confidence=abs(confidence),
                    price=current_price,
                    timestamp=timestamp,
                    strategy_name=self.name,
                    reason="价格触及布林带下轨，超卖反弹",
                    stop_loss=current_price * 0.97,
                    take_profit=bb_middle[i]
                ))
                
            # 价格触及上轨卖出信号
            elif (current_price >= bb_upper[i] and prev_price < bb_upper[i-1]):
                confidence = (current_price - bb_upper[i]) / bb_upper[i]
                signals.append(TradingSignal(
                    signal_type=SignalType.SELL,
                    confidence=abs(confidence),
                    price=current_price,
                    timestamp=timestamp,
                    strategy_name=self.name,
                    reason="价格触及布林带上轨，超买回调",
                    stop_loss=current_price * 1.03,
                    take_profit=bb_middle[i]
                ))
                
        return signals

class KDJStrategy(QuantitativeStrategy):
    """KDJ策略"""
    
    def __init__(self, k_period=9, d_period=3, j_period=3):
        super().__init__("KDJ策略")
        self.k_period = k_period
        self.d_period = d_period
        self.j_period = j_period
        
    def calculate_indicators(self, data: pd.DataFrame) -> Dict:
        """计算KDJ指标"""
        high = data['high'].values
        low = data['low'].values
        close = data['close'].values

        if HAS_PANDAS_TA:
            # 使用pandas-ta计算KDJ
            stoch_result = data.ta.stoch(k=self.k_period, d=self.d_period)
            k = stoch_result[f'STOCHk_{self.k_period}_{self.d_period}'].values
            d = stoch_result[f'STOCHd_{self.k_period}_{self.d_period}'].values
        else:
            # 使用简化实现
            k, d = simple_stoch(high, low, close,
                               k_period=self.k_period,
                               d_period=self.d_period)

        j = 3 * k - 2 * d

        return {'k': k, 'd': d, 'j': j}
        
    def generate_signals(self, data: pd.DataFrame) -> List[TradingSignal]:
        """生成KDJ交易信号"""
        indicators = self.calculate_indicators(data)
        signals = []
        
        k = indicators['k']
        d = indicators['d']
        j = indicators['j']
        
        for i in range(1, len(data)):
            if np.isnan(k[i]) or np.isnan(d[i]):
                continue
                
            current_price = data.iloc[i]['close']
            timestamp = str(data.index[i])
            
            # KDJ金叉买入信号
            if (k[i] > d[i] and k[i-1] <= d[i-1] and k[i] < 80):
                confidence = min((k[i] - d[i]) / 20, 1.0)
                signals.append(TradingSignal(
                    signal_type=SignalType.BUY,
                    confidence=confidence,
                    price=current_price,
                    timestamp=timestamp,
                    strategy_name=self.name,
                    reason=f"KDJ金叉，K={k[i]:.2f}, D={d[i]:.2f}",
                    stop_loss=current_price * 0.96,
                    take_profit=current_price * 1.05
                ))
                
            # KDJ死叉卖出信号
            elif (k[i] < d[i] and k[i-1] >= d[i-1] and k[i] > 20):
                confidence = min((d[i] - k[i]) / 20, 1.0)
                signals.append(TradingSignal(
                    signal_type=SignalType.SELL,
                    confidence=confidence,
                    price=current_price,
                    timestamp=timestamp,
                    strategy_name=self.name,
                    reason=f"KDJ死叉，K={k[i]:.2f}, D={d[i]:.2f}",
                    stop_loss=current_price * 1.04,
                    take_profit=current_price * 0.95
                ))
                
        return signals

class MovingAverageStrategy(QuantitativeStrategy):
    """移动平均线策略"""

    def __init__(self, short_period=5, long_period=20):
        super().__init__("移动平均线策略")
        self.short_period = short_period
        self.long_period = long_period

    def calculate_indicators(self, data: pd.DataFrame) -> Dict:
        """计算移动平均线指标"""
        close = data['close'].values

        if HAS_PANDAS_TA:
            # 使用pandas-ta计算移动平均
            ma_short = data.ta.sma(length=self.short_period).values
            ma_long = data.ta.sma(length=self.long_period).values
        else:
            # 使用简化实现
            ma_short = simple_sma(close, period=self.short_period)
            ma_long = simple_sma(close, period=self.long_period)

        return {
            'ma_short': ma_short,
            'ma_long': ma_long
        }

    def generate_signals(self, data: pd.DataFrame) -> List[TradingSignal]:
        """生成移动平均线交易信号"""
        indicators = self.calculate_indicators(data)
        signals = []

        ma_short = indicators['ma_short']
        ma_long = indicators['ma_long']

        for i in range(1, len(data)):
            if np.isnan(ma_short[i]) or np.isnan(ma_long[i]):
                continue

            current_price = data.iloc[i]['close']
            timestamp = str(data.index[i])

            # 短期均线上穿长期均线买入信号
            if (ma_short[i] > ma_long[i] and ma_short[i-1] <= ma_long[i-1]):
                confidence = (ma_short[i] - ma_long[i]) / ma_long[i]
                signals.append(TradingSignal(
                    signal_type=SignalType.BUY,
                    confidence=min(confidence * 10, 1.0),
                    price=current_price,
                    timestamp=timestamp,
                    strategy_name=self.name,
                    reason=f"短期均线({self.short_period})上穿长期均线({self.long_period})",
                    stop_loss=ma_long[i] * 0.98,
                    take_profit=current_price * 1.08
                ))

            # 短期均线下穿长期均线卖出信号
            elif (ma_short[i] < ma_long[i] and ma_short[i-1] >= ma_long[i-1]):
                confidence = (ma_long[i] - ma_short[i]) / ma_long[i]
                signals.append(TradingSignal(
                    signal_type=SignalType.SELL,
                    confidence=min(confidence * 10, 1.0),
                    price=current_price,
                    timestamp=timestamp,
                    strategy_name=self.name,
                    reason=f"短期均线({self.short_period})下穿长期均线({self.long_period})",
                    stop_loss=ma_long[i] * 1.02,
                    take_profit=current_price * 0.92
                ))

        return signals

class GridTradingStrategy(QuantitativeStrategy):
    """网格交易策略"""

    def __init__(self, grid_size=0.02, max_grids=10, base_amount=1000):
        super().__init__("网格交易策略")
        self.grid_size = grid_size  # 网格间距（百分比）
        self.max_grids = max_grids  # 最大网格数
        self.base_amount = base_amount  # 基础交易金额
        self.grid_levels = []

    def setup_grids(self, current_price: float):
        """设置网格价位"""
        self.grid_levels = []
        for i in range(-self.max_grids//2, self.max_grids//2 + 1):
            grid_price = current_price * (1 + i * self.grid_size)
            self.grid_levels.append(grid_price)
        self.grid_levels.sort()

    def generate_signals(self, data: pd.DataFrame) -> List[TradingSignal]:
        """生成网格交易信号"""
        signals = []

        if not self.grid_levels:
            # 初始化网格
            self.setup_grids(data.iloc[0]['close'])

        for i in range(1, len(data)):
            current_price = data.iloc[i]['close']
            prev_price = data.iloc[i-1]['close']
            timestamp = str(data.index[i])

            # 检查是否触及网格线
            for grid_price in self.grid_levels:
                # 价格向上突破网格线 - 卖出信号
                if prev_price <= grid_price < current_price:
                    signals.append(TradingSignal(
                        signal_type=SignalType.SELL,
                        confidence=0.8,
                        price=grid_price,
                        timestamp=timestamp,
                        strategy_name=self.name,
                        reason=f"价格突破网格线{grid_price:.2f}，执行卖出",
                        take_profit=grid_price * (1 + self.grid_size)
                    ))

                # 价格向下突破网格线 - 买入信号
                elif prev_price >= grid_price > current_price:
                    signals.append(TradingSignal(
                        signal_type=SignalType.BUY,
                        confidence=0.8,
                        price=grid_price,
                        timestamp=timestamp,
                        strategy_name=self.name,
                        reason=f"价格跌破网格线{grid_price:.2f}，执行买入",
                        take_profit=grid_price * (1 + self.grid_size)
                    ))

        return signals
