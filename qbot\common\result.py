#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一的结果和错误处理系统
"""

from typing import Generic, TypeVar, Optional, Any, Dict, List
from dataclasses import dataclass, field
from enum import Enum
import traceback
import time
from datetime import datetime

T = TypeVar('T')


class ErrorCode(Enum):
    """错误代码枚举"""
    # 通用错误
    SUCCESS = "SUCCESS"
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    INVALID_PARAMETER = "INVALID_PARAMETER"
    TIMEOUT = "TIMEOUT"
    NETWORK_ERROR = "NETWORK_ERROR"
    
    # 数据源错误
    DATA_SOURCE_UNAVAILABLE = "DATA_SOURCE_UNAVAILABLE"
    DATA_SOURCE_RATE_LIMITED = "DATA_SOURCE_RATE_LIMITED"
    DATA_SOURCE_AUTH_FAILED = "DATA_SOURCE_AUTH_FAILED"
    DATA_SOURCE_INVALID_RESPONSE = "DATA_SOURCE_INVALID_RESPONSE"
    DATA_SOURCE_NO_DATA = "DATA_SOURCE_NO_DATA"
    
    # 股票数据错误
    STOCK_NOT_FOUND = "STOCK_NOT_FOUND"
    STOCK_DELISTED = "STOCK_DELISTED"
    STOCK_SUSPENDED = "STOCK_SUSPENDED"
    INVALID_STOCK_CODE = "INVALID_STOCK_CODE"
    MARKET_NOT_SUPPORTED = "MARKET_NOT_SUPPORTED"
    
    # 配置错误
    CONFIG_MISSING = "CONFIG_MISSING"
    CONFIG_INVALID = "CONFIG_INVALID"
    API_KEY_MISSING = "API_KEY_MISSING"
    API_KEY_INVALID = "API_KEY_INVALID"
    
    # 数据质量错误
    DATA_QUALITY_LOW = "DATA_QUALITY_LOW"
    DATA_INCOMPLETE = "DATA_INCOMPLETE"
    DATA_INCONSISTENT = "DATA_INCONSISTENT"


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "LOW"          # 轻微错误，不影响主要功能
    MEDIUM = "MEDIUM"    # 中等错误，影响部分功能
    HIGH = "HIGH"        # 严重错误，影响主要功能
    CRITICAL = "CRITICAL" # 致命错误，系统无法正常运行


@dataclass
class ErrorInfo:
    """错误信息类"""
    code: ErrorCode
    message: str
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
    details: Optional[str] = None
    traceback_info: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    source: Optional[str] = None  # 错误来源
    context: Dict[str, Any] = field(default_factory=dict)  # 错误上下文
    
    def __post_init__(self):
        """后处理"""
        if self.details is None:
            self.details = self.message
    
    @classmethod
    def from_exception(cls, exception: Exception, code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
                      severity: ErrorSeverity = ErrorSeverity.MEDIUM, 
                      source: Optional[str] = None,
                      context: Optional[Dict[str, Any]] = None) -> 'ErrorInfo':
        """从异常创建错误信息"""
        return cls(
            code=code,
            message=str(exception),
            severity=severity,
            details=f"{type(exception).__name__}: {str(exception)}",
            traceback_info=traceback.format_exc(),
            source=source,
            context=context or {}
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'code': self.code.value,
            'message': self.message,
            'severity': self.severity.value,
            'details': self.details,
            'timestamp': self.timestamp,
            'datetime': datetime.fromtimestamp(self.timestamp).isoformat(),
            'source': self.source,
            'context': self.context,
            'has_traceback': self.traceback_info is not None
        }
    
    def __str__(self) -> str:
        return f"[{self.severity.value}] {self.code.value}: {self.message}"


@dataclass
class Result(Generic[T]):
    """统一的结果对象"""
    success: bool
    data: Optional[T] = None
    error: Optional[ErrorInfo] = None
    warnings: List[ErrorInfo] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def success_with_data(cls, data: T, metadata: Optional[Dict[str, Any]] = None) -> 'Result[T]':
        """创建成功结果"""
        return cls(
            success=True,
            data=data,
            metadata=metadata or {}
        )
    
    @classmethod
    def failure(cls, error: ErrorInfo) -> 'Result[T]':
        """创建失败结果"""
        return cls(
            success=False,
            error=error
        )
    
    @classmethod
    def failure_from_exception(cls, exception: Exception, 
                              code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
                              severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                              source: Optional[str] = None,
                              context: Optional[Dict[str, Any]] = None) -> 'Result[T]':
        """从异常创建失败结果"""
        error = ErrorInfo.from_exception(exception, code, severity, source, context)
        return cls.failure(error)
    
    def add_warning(self, warning: ErrorInfo):
        """添加警告"""
        self.warnings.append(warning)
    
    def add_metadata(self, key: str, value: Any):
        """添加元数据"""
        self.metadata[key] = value
    
    def is_success(self) -> bool:
        """检查是否成功"""
        return self.success
    
    def is_failure(self) -> bool:
        """检查是否失败"""
        return not self.success
    
    def get_data_or_none(self) -> Optional[T]:
        """获取数据，失败时返回None"""
        return self.data if self.success else None
    
    def get_data_or_default(self, default: T) -> T:
        """获取数据，失败时返回默认值"""
        return self.data if self.success else default
    
    def get_error_message(self) -> str:
        """获取错误消息"""
        if self.error:
            return self.error.message
        return "未知错误"
    
    def get_error_code(self) -> Optional[ErrorCode]:
        """获取错误代码"""
        return self.error.code if self.error else None
    
    def has_warnings(self) -> bool:
        """检查是否有警告"""
        return len(self.warnings) > 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result_dict = {
            'success': self.success,
            'metadata': self.metadata,
            'has_warnings': self.has_warnings(),
            'warnings_count': len(self.warnings)
        }
        
        if self.success and self.data is not None:
            # 如果数据有to_dict方法，使用它
            if hasattr(self.data, 'to_dict'):
                result_dict['data'] = self.data.to_dict()
            else:
                result_dict['data'] = self.data
        
        if self.error:
            result_dict['error'] = self.error.to_dict()
        
        if self.warnings:
            result_dict['warnings'] = [w.to_dict() for w in self.warnings]
        
        return result_dict
    
    def __str__(self) -> str:
        if self.success:
            return f"Success: {type(self.data).__name__ if self.data else 'No data'}"
        else:
            return f"Failure: {self.error}"


class ResultBuilder(Generic[T]):
    """结果构建器"""
    
    def __init__(self):
        self.result = Result[T](success=True)
    
    def with_data(self, data: T) -> 'ResultBuilder[T]':
        """设置数据"""
        self.result.data = data
        return self
    
    def with_error(self, error: ErrorInfo) -> 'ResultBuilder[T]':
        """设置错误"""
        self.result.success = False
        self.result.error = error
        return self
    
    def with_exception(self, exception: Exception, 
                      code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
                      severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                      source: Optional[str] = None) -> 'ResultBuilder[T]':
        """从异常设置错误"""
        error = ErrorInfo.from_exception(exception, code, severity, source)
        return self.with_error(error)
    
    def add_warning(self, warning: ErrorInfo) -> 'ResultBuilder[T]':
        """添加警告"""
        self.result.add_warning(warning)
        return self
    
    def add_metadata(self, key: str, value: Any) -> 'ResultBuilder[T]':
        """添加元数据"""
        self.result.add_metadata(key, value)
        return self
    
    def build(self) -> Result[T]:
        """构建结果"""
        return self.result


# 便捷函数
def success(data: T, metadata: Optional[Dict[str, Any]] = None) -> Result[T]:
    """创建成功结果的便捷函数"""
    return Result.success_with_data(data, metadata)


def failure(code: ErrorCode, message: str, 
           severity: ErrorSeverity = ErrorSeverity.MEDIUM,
           source: Optional[str] = None,
           context: Optional[Dict[str, Any]] = None) -> Result[Any]:
    """创建失败结果的便捷函数"""
    error = ErrorInfo(
        code=code,
        message=message,
        severity=severity,
        source=source,
        context=context or {}
    )
    return Result.failure(error)


def from_exception(exception: Exception, 
                  code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
                  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                  source: Optional[str] = None,
                  context: Optional[Dict[str, Any]] = None) -> Result[Any]:
    """从异常创建失败结果的便捷函数"""
    return Result.failure_from_exception(exception, code, severity, source, context)
