#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据质量管理器
处理数据清洗、标准化、验证和质量评估
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
import re

from qbot.config.constants import MarketType, DATA_QUALITY, SCREENING
from qbot.common.result import Result, ErrorCode, ErrorSeverity, success, failure
from qbot.common.enhanced_logger import get_logger


@dataclass
class DataQualityIssue:
    """数据质量问题"""
    field: str
    issue_type: str
    severity: str
    description: str
    original_value: Any
    suggested_value: Any = None
    auto_fixable: bool = False


@dataclass
class DataQualityReport:
    """数据质量报告"""
    symbol: str
    source: str
    timestamp: datetime
    overall_score: float
    issues: List[DataQualityIssue] = field(default_factory=list)
    field_scores: Dict[str, float] = field(default_factory=dict)
    is_usable: bool = True
    
    def add_issue(self, issue: DataQualityIssue):
        """添加质量问题"""
        self.issues.append(issue)
        
        # 根据严重程度调整整体评分
        severity_impact = {
            'critical': -0.3,
            'high': -0.2,
            'medium': -0.1,
            'low': -0.05
        }
        
        impact = severity_impact.get(issue.severity, -0.1)
        self.overall_score = max(0.0, self.overall_score + impact)
        
        # 如果有严重问题，标记为不可用
        if issue.severity in ['critical', 'high'] and not issue.auto_fixable:
            self.is_usable = False


class IDataValidator(ABC):
    """数据验证器接口"""
    
    @abstractmethod
    def validate(self, data: Dict[str, Any], symbol: str) -> List[DataQualityIssue]:
        """验证数据并返回问题列表"""
        pass


class IDataCleaner(ABC):
    """数据清洗器接口"""
    
    @abstractmethod
    def clean(self, data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """清洗数据并返回清洗后的数据"""
        pass


class IDataNormalizer(ABC):
    """数据标准化器接口"""
    
    @abstractmethod
    def normalize(self, data: Dict[str, Any], source: str, market: MarketType) -> Dict[str, Any]:
        """标准化数据格式"""
        pass


class BasicDataValidator(IDataValidator):
    """基础数据验证器"""
    
    def __init__(self):
        self.logger = get_logger("basic_data_validator")
        
        # 必需字段定义
        self.required_fields = {
            'basic': ['symbol', 'current_price'],
            'extended': ['name', 'market', 'volume'],
            'financial': ['market_cap', 'pe_ratio', 'pb_ratio'],
            'technical': ['rsi', 'ma_trend']
        }
        
        # 字段范围定义
        self.field_ranges = {
            'current_price': (0.01, 10000.0),
            'volume': (0, 1e12),
            'market_cap': (1e6, 1e15),
            'pe_ratio': (-1000, 1000),
            'pb_ratio': (0.01, 100),
            'roe': (-10, 10),
            'debt_ratio': (0, 10),
            'rsi': (0, 100),
            'volatility': (0, 5),
            'liquidity_ratio': (0, 1)
        }
    
    def validate(self, data: Dict[str, Any], symbol: str) -> List[DataQualityIssue]:
        """验证数据质量"""
        issues = []
        
        # 1. 必需字段检查
        issues.extend(self._validate_required_fields(data, symbol))
        
        # 2. 数据类型检查
        issues.extend(self._validate_data_types(data, symbol))
        
        # 3. 数值范围检查
        issues.extend(self._validate_ranges(data, symbol))
        
        # 4. 逻辑一致性检查
        issues.extend(self._validate_consistency(data, symbol))
        
        # 5. 异常值检查
        issues.extend(self._validate_outliers(data, symbol))
        
        return issues
    
    def _validate_required_fields(self, data: Dict[str, Any], symbol: str) -> List[DataQualityIssue]:
        """验证必需字段"""
        issues = []
        
        for field in self.required_fields['basic']:
            if field not in data or data[field] is None:
                issues.append(DataQualityIssue(
                    field=field,
                    issue_type='missing_field',
                    severity='critical',
                    description=f'缺少必需字段: {field}',
                    original_value=None,
                    auto_fixable=False
                ))
        
        return issues
    
    def _validate_data_types(self, data: Dict[str, Any], symbol: str) -> List[DataQualityIssue]:
        """验证数据类型"""
        issues = []
        
        # 数值字段类型检查
        numeric_fields = [
            'current_price', 'volume', 'market_cap', 'pe_ratio', 'pb_ratio',
            'roe', 'debt_ratio', 'rsi', 'volatility', 'liquidity_ratio'
        ]
        
        for field in numeric_fields:
            if field in data and data[field] is not None:
                try:
                    float(data[field])
                except (ValueError, TypeError):
                    issues.append(DataQualityIssue(
                        field=field,
                        issue_type='invalid_type',
                        severity='high',
                        description=f'{field}应为数值类型',
                        original_value=data[field],
                        suggested_value=0.0,
                        auto_fixable=True
                    ))
        
        return issues
    
    def _validate_ranges(self, data: Dict[str, Any], symbol: str) -> List[DataQualityIssue]:
        """验证数值范围"""
        issues = []
        
        for field, (min_val, max_val) in self.field_ranges.items():
            if field in data and data[field] is not None:
                try:
                    value = float(data[field])
                    if not (min_val <= value <= max_val):
                        severity = 'high' if field in ['current_price', 'volume'] else 'medium'
                        issues.append(DataQualityIssue(
                            field=field,
                            issue_type='out_of_range',
                            severity=severity,
                            description=f'{field}值{value}超出合理范围[{min_val}, {max_val}]',
                            original_value=value,
                            suggested_value=np.clip(value, min_val, max_val),
                            auto_fixable=True
                        ))
                except (ValueError, TypeError):
                    pass  # 类型错误已在上一步检查
        
        return issues
    
    def _validate_consistency(self, data: Dict[str, Any], symbol: str) -> List[DataQualityIssue]:
        """验证逻辑一致性"""
        issues = []
        
        # 价格一致性检查
        if all(field in data for field in ['current_price', 'high_price', 'low_price']):
            current = data['current_price']
            high = data['high_price']
            low = data['low_price']
            
            if not (low <= current <= high):
                issues.append(DataQualityIssue(
                    field='price_consistency',
                    issue_type='logical_inconsistency',
                    severity='medium',
                    description=f'价格不一致: 当前价{current}不在最高价{high}和最低价{low}之间',
                    original_value={'current': current, 'high': high, 'low': low},
                    auto_fixable=False
                ))
        
        # 市值与价格一致性检查
        if all(field in data for field in ['market_cap', 'current_price']) and 'shares_outstanding' in data:
            market_cap = data['market_cap']
            price = data['current_price']
            shares = data['shares_outstanding']
            
            expected_market_cap = price * shares
            if abs(market_cap - expected_market_cap) / expected_market_cap > 0.1:  # 10%误差
                issues.append(DataQualityIssue(
                    field='market_cap_consistency',
                    issue_type='logical_inconsistency',
                    severity='medium',
                    description=f'市值{market_cap}与价格*股数{expected_market_cap}不一致',
                    original_value=market_cap,
                    suggested_value=expected_market_cap,
                    auto_fixable=True
                ))
        
        return issues
    
    def _validate_outliers(self, data: Dict[str, Any], symbol: str) -> List[DataQualityIssue]:
        """验证异常值"""
        issues = []
        
        # PE比率异常值检查
        if 'pe_ratio' in data and data['pe_ratio'] is not None:
            pe = float(data['pe_ratio'])
            if pe < 0:
                issues.append(DataQualityIssue(
                    field='pe_ratio',
                    issue_type='negative_value',
                    severity='low',
                    description=f'PE比率为负值: {pe}',
                    original_value=pe,
                    auto_fixable=False
                ))
            elif pe > 200:
                issues.append(DataQualityIssue(
                    field='pe_ratio',
                    issue_type='extreme_value',
                    severity='medium',
                    description=f'PE比率过高: {pe}',
                    original_value=pe,
                    auto_fixable=False
                ))
        
        # ROE异常值检查
        if 'roe' in data and data['roe'] is not None:
            roe = float(data['roe'])
            if abs(roe) > 2:  # ROE超过200%
                issues.append(DataQualityIssue(
                    field='roe',
                    issue_type='extreme_value',
                    severity='medium',
                    description=f'ROE值异常: {roe:.2%}',
                    original_value=roe,
                    auto_fixable=False
                ))
        
        return issues


class DataCleaner(IDataCleaner):
    """数据清洗器"""
    
    def __init__(self):
        self.logger = get_logger("data_cleaner")
    
    def clean(self, data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """清洗数据"""
        cleaned_data = data.copy()
        
        # 1. 移除空白字符
        cleaned_data = self._clean_strings(cleaned_data)
        
        # 2. 处理缺失值
        cleaned_data = self._handle_missing_values(cleaned_data, symbol)
        
        # 3. 数据类型转换
        cleaned_data = self._convert_data_types(cleaned_data)
        
        # 4. 异常值处理
        cleaned_data = self._handle_outliers(cleaned_data)
        
        # 5. 单位标准化
        cleaned_data = self._standardize_units(cleaned_data)
        
        return cleaned_data
    
    def _clean_strings(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """清理字符串字段"""
        string_fields = ['symbol', 'name', 'market', 'sector', 'industry', 'ma_trend']
        
        for field in string_fields:
            if field in data and isinstance(data[field], str):
                # 移除前后空白
                data[field] = data[field].strip()
                
                # 标准化股票代码格式
                if field == 'symbol':
                    data[field] = self._standardize_symbol(data[field])
                
                # 标准化市场名称
                elif field == 'market':
                    data[field] = self._standardize_market(data[field])
        
        return data
    
    def _standardize_symbol(self, symbol: str) -> str:
        """标准化股票代码"""
        # 移除非字母数字字符
        symbol = re.sub(r'[^A-Za-z0-9]', '', symbol)
        
        # 转换为大写
        symbol = symbol.upper()
        
        # 中国股票代码补零
        if symbol.isdigit() and len(symbol) < 6:
            symbol = symbol.zfill(6)
        
        return symbol
    
    def _standardize_market(self, market: str) -> str:
        """标准化市场名称"""
        market_mapping = {
            'sh': 'CN_A', 'sz': 'CN_A', 'china': 'CN_A', '中国': 'CN_A',
            'nasdaq': 'US', 'nyse': 'US', 'usa': 'US', 'america': 'US',
            'hk': 'HK', 'hongkong': 'HK', '香港': 'HK',
            'uk': 'UK', 'london': 'UK', 'britain': 'UK',
            'jp': 'JP', 'japan': 'JP', 'tokyo': 'JP', '日本': 'JP'
        }
        
        market_lower = market.lower()
        return market_mapping.get(market_lower, market)
    
    def _handle_missing_values(self, data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """处理缺失值"""
        # 数值字段的默认值
        numeric_defaults = {
            'volume': 0,
            'market_cap': 0,
            'pe_ratio': 0,
            'pb_ratio': 0,
            'roe': 0,
            'debt_ratio': 0.5,  # 中等负债率
            'rsi': 50,  # 中性RSI
            'volatility': 0.3,  # 中等波动率
            'liquidity_ratio': 0.01  # 低流动性
        }
        
        for field, default_value in numeric_defaults.items():
            if field in data and (data[field] is None or data[field] == ''):
                data[field] = default_value
                self.logger.debug(f"{symbol}: {field}缺失，使用默认值{default_value}")
        
        # 字符串字段的默认值
        string_defaults = {
            'name': f'股票{symbol}',
            'sector': '未知',
            'industry': '未知',
            'ma_trend': '震荡'
        }
        
        for field, default_value in string_defaults.items():
            if field in data and (data[field] is None or data[field] == ''):
                data[field] = default_value
        
        return data
    
    def _convert_data_types(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """转换数据类型"""
        # 数值字段转换
        numeric_fields = [
            'current_price', 'open_price', 'high_price', 'low_price', 'close_prev',
            'volume', 'amount', 'market_cap', 'pe_ratio', 'pb_ratio', 'roe',
            'debt_ratio', 'rsi', 'volatility', 'liquidity_ratio', 'daily_volume'
        ]
        
        for field in numeric_fields:
            if field in data and data[field] is not None:
                try:
                    # 处理百分比格式
                    if isinstance(data[field], str) and '%' in data[field]:
                        data[field] = float(data[field].replace('%', '')) / 100
                    else:
                        data[field] = float(data[field])
                except (ValueError, TypeError):
                    self.logger.warning(f"无法转换{field}为数值: {data[field]}")
                    data[field] = 0.0
        
        # 整数字段转换
        integer_fields = ['volume', 'shares_outstanding']
        for field in integer_fields:
            if field in data and data[field] is not None:
                try:
                    data[field] = int(float(data[field]))
                except (ValueError, TypeError):
                    data[field] = 0
        
        return data
    
    def _handle_outliers(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理异常值"""
        # PE比率异常值处理
        if 'pe_ratio' in data and data['pe_ratio'] is not None:
            pe = data['pe_ratio']
            if pe < -1000:
                data['pe_ratio'] = -1000
            elif pe > 1000:
                data['pe_ratio'] = 1000
        
        # PB比率异常值处理
        if 'pb_ratio' in data and data['pb_ratio'] is not None:
            pb = data['pb_ratio']
            if pb < 0:
                data['pb_ratio'] = 0
            elif pb > 100:
                data['pb_ratio'] = 100
        
        # ROE异常值处理
        if 'roe' in data and data['roe'] is not None:
            roe = data['roe']
            if roe < -10:
                data['roe'] = -10
            elif roe > 10:
                data['roe'] = 10
        
        return data
    
    def _standardize_units(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化单位"""
        # 市值单位标准化（统一为元）
        if 'market_cap' in data and data['market_cap'] is not None:
            market_cap = data['market_cap']
            
            # 如果市值小于1亿，可能是以万元或亿元为单位
            if market_cap < 1e8:
                if market_cap > 1000:  # 可能是万元
                    data['market_cap'] = market_cap * 10000
                elif market_cap > 1:  # 可能是亿元
                    data['market_cap'] = market_cap * 1e8
        
        # 成交量单位标准化
        if 'volume' in data and data['volume'] is not None:
            volume = data['volume']
            
            # 如果成交量异常小，可能单位不对
            if volume < 100 and volume > 0:
                data['volume'] = volume * 100  # 可能是手为单位
        
        return data


class DataNormalizer(IDataNormalizer):
    """数据标准化器 - 统一不同数据源的格式"""

    def __init__(self):
        self.logger = get_logger("data_normalizer")

        # 字段映射表 - 将不同数据源的字段名映射到标准字段名
        self.field_mappings = {
            'sina': {
                'code': 'symbol',
                'name': 'name',
                'price': 'current_price',
                'open': 'open_price',
                'high': 'high_price',
                'low': 'low_price',
                'prev_close': 'close_prev',
                'vol': 'volume',
                'amount': 'amount'
            },
            'tencent': {
                'code': 'symbol',
                'name': 'name',
                'cur_pri': 'current_price',
                'open_pri': 'open_price',
                'high_pri': 'high_price',
                'low_pri': 'low_price',
                'pre_close': 'close_prev',
                'volume': 'volume',
                'turnover': 'amount'
            },
            'eastmoney': {
                'f12': 'symbol',
                'f14': 'name',
                'f2': 'current_price',
                'f17': 'open_price',
                'f15': 'high_price',
                'f16': 'low_price',
                'f18': 'close_prev',
                'f5': 'volume',
                'f6': 'amount'
            },
            'finnhub': {
                'symbol': 'symbol',
                'c': 'current_price',
                'o': 'open_price',
                'h': 'high_price',
                'l': 'low_price',
                'pc': 'close_prev'
            },
            'yahoo': {
                'symbol': 'symbol',
                'regularMarketPrice': 'current_price',
                'regularMarketOpen': 'open_price',
                'regularMarketDayHigh': 'high_price',
                'regularMarketDayLow': 'low_price',
                'regularMarketPreviousClose': 'close_prev',
                'regularMarketVolume': 'volume'
            }
        }

    def normalize(self, data: Dict[str, Any], source: str, market: MarketType) -> Dict[str, Any]:
        """标准化数据格式"""
        normalized_data = {}

        # 1. 字段名标准化
        normalized_data = self._normalize_field_names(data, source)

        # 2. 数值格式标准化
        normalized_data = self._normalize_numeric_formats(normalized_data, market)

        # 3. 时间格式标准化
        normalized_data = self._normalize_time_formats(normalized_data)

        # 4. 添加标准化元数据
        normalized_data['source'] = source
        normalized_data['market'] = market.value
        normalized_data['normalized_at'] = datetime.now().isoformat()

        return normalized_data

    def _normalize_field_names(self, data: Dict[str, Any], source: str) -> Dict[str, Any]:
        """标准化字段名"""
        if source not in self.field_mappings:
            self.logger.warning(f"未知数据源: {source}，使用原始字段名")
            return data.copy()

        mapping = self.field_mappings[source]
        normalized = {}

        # 映射已知字段
        for original_field, standard_field in mapping.items():
            if original_field in data:
                normalized[standard_field] = data[original_field]

        # 保留未映射的字段
        for field, value in data.items():
            if field not in mapping and field not in normalized:
                normalized[field] = value

        return normalized

    def _normalize_numeric_formats(self, data: Dict[str, Any], market: MarketType) -> Dict[str, Any]:
        """标准化数值格式"""
        # 价格精度标准化
        price_fields = ['current_price', 'open_price', 'high_price', 'low_price', 'close_prev']

        # 不同市场的价格精度
        price_decimals = {
            MarketType.CN_A: 2,    # 中国A股保留2位小数
            MarketType.US: 2,      # 美股保留2位小数
            MarketType.HK: 3,      # 港股保留3位小数
            MarketType.UK: 2,      # 英股保留2位小数
            MarketType.JP: 0       # 日股通常为整数
        }

        decimals = price_decimals.get(market, 2)

        for field in price_fields:
            if field in data and data[field] is not None:
                try:
                    data[field] = round(float(data[field]), decimals)
                except (ValueError, TypeError):
                    pass

        # 成交量标准化（统一为股数）
        if 'volume' in data and data['volume'] is not None:
            try:
                volume = float(data['volume'])

                # 中国A股成交量可能以手为单位（1手=100股）
                if market == MarketType.CN_A and volume < 1000000:
                    data['volume'] = int(volume * 100)
                else:
                    data['volume'] = int(volume)
            except (ValueError, TypeError):
                data['volume'] = 0

        # 市值标准化（统一为本币）
        if 'market_cap' in data and data['market_cap'] is not None:
            try:
                market_cap = float(data['market_cap'])

                # 检测单位并转换
                if market_cap < 1000:  # 可能是亿为单位
                    if market == MarketType.CN_A:
                        data['market_cap'] = market_cap * 1e8  # 亿元
                    else:
                        data['market_cap'] = market_cap * 1e9  # 十亿美元/其他货币
                elif market_cap < 1000000:  # 可能是万为单位
                    data['market_cap'] = market_cap * 10000

            except (ValueError, TypeError):
                data['market_cap'] = 0

        return data

    def _normalize_time_formats(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化时间格式"""
        time_fields = ['timestamp', 'trade_time', 'update_time']

        for field in time_fields:
            if field in data and data[field] is not None:
                try:
                    # 尝试解析各种时间格式
                    time_str = str(data[field])

                    # Unix时间戳
                    if time_str.isdigit() and len(time_str) >= 10:
                        timestamp = int(time_str)
                        if len(time_str) == 13:  # 毫秒时间戳
                            timestamp = timestamp / 1000
                        data[field] = datetime.fromtimestamp(timestamp).isoformat()

                    # ISO格式时间
                    elif 'T' in time_str:
                        # 已经是ISO格式，保持不变
                        pass

                    # 其他格式尝试解析
                    else:
                        # 这里可以添加更多时间格式的解析
                        pass

                except (ValueError, TypeError):
                    # 解析失败，使用当前时间
                    data[field] = datetime.now().isoformat()

        return data


class DataQualityManager:
    """数据质量管理器 - 统一管理数据质量流程"""

    def __init__(self):
        self.logger = get_logger("data_quality_manager")
        self.validator = BasicDataValidator()
        self.cleaner = DataCleaner()
        self.normalizer = DataNormalizer()

        # 质量报告缓存
        self.quality_reports: Dict[str, DataQualityReport] = {}

        # 数据源质量统计
        self.source_quality_stats: Dict[str, Dict[str, float]] = {}

    def process_stock_data(self, raw_data: Dict[str, Any], source: str,
                          market: MarketType, symbol: str) -> Result[Dict[str, Any]]:
        """处理股票数据 - 完整的质量管理流程"""
        operation_name = f"process_stock_data_{symbol}"
        self.logger.start_operation(operation_name, symbol=symbol, source=source)

        try:
            # 1. 数据标准化
            normalized_data = self.normalizer.normalize(raw_data, source, market)

            # 2. 数据清洗
            cleaned_data = self.cleaner.clean(normalized_data, symbol)

            # 3. 数据验证
            quality_issues = self.validator.validate(cleaned_data, symbol)

            # 4. 生成质量报告
            quality_report = self._generate_quality_report(
                cleaned_data, symbol, source, quality_issues
            )

            # 5. 缓存质量报告
            self.quality_reports[symbol] = quality_report

            # 6. 更新数据源质量统计
            self._update_source_quality_stats(source, quality_report)

            # 7. 决定是否使用数据
            if not quality_report.is_usable:
                self.logger.end_operation(operation_name, success=False,
                                        reason="data_quality_too_low")
                return failure(
                    ErrorCode.DATA_QUALITY_LOW,
                    f"股票{symbol}数据质量过低，不可使用",
                    ErrorSeverity.MEDIUM,
                    source,
                    {'quality_score': quality_report.overall_score, 'issues': len(quality_issues)}
                )

            # 8. 添加质量元数据
            cleaned_data['quality_score'] = quality_report.overall_score
            cleaned_data['quality_issues_count'] = len(quality_issues)
            cleaned_data['data_source'] = source

            self.logger.end_operation(operation_name, success=True,
                                    quality_score=quality_report.overall_score,
                                    issues_count=len(quality_issues))

            result = success(cleaned_data)
            result.add_metadata('quality_report', quality_report)
            return result

        except Exception as e:
            self.logger.log_exception(e,
                context={'symbol': symbol, 'source': source, 'market': market.value},
                error_code=ErrorCode.DATA_QUALITY_LOW,
                severity=ErrorSeverity.HIGH)

            self.logger.end_operation(operation_name, success=False, error=str(e))
            return failure(ErrorCode.DATA_QUALITY_LOW,
                         f"处理股票{symbol}数据时发生错误: {str(e)}")

    def _generate_quality_report(self, data: Dict[str, Any], symbol: str,
                               source: str, issues: List[DataQualityIssue]) -> DataQualityReport:
        """生成数据质量报告"""
        # 计算基础质量评分
        base_score = 1.0

        # 计算各字段评分
        field_scores = self._calculate_field_scores(data)

        # 创建质量报告
        report = DataQualityReport(
            symbol=symbol,
            source=source,
            timestamp=datetime.now(),
            overall_score=base_score,
            field_scores=field_scores
        )

        # 添加质量问题
        for issue in issues:
            report.add_issue(issue)

        # 根据字段评分调整整体评分
        if field_scores:
            avg_field_score = sum(field_scores.values()) / len(field_scores)
            report.overall_score = min(report.overall_score, avg_field_score)

        return report

    def _calculate_field_scores(self, data: Dict[str, Any]) -> Dict[str, float]:
        """计算各字段质量评分"""
        field_scores = {}

        # 必需字段评分
        required_fields = ['symbol', 'current_price', 'name']
        for field in required_fields:
            if field in data and data[field] is not None:
                field_scores[field] = 1.0
            else:
                field_scores[field] = 0.0

        # 数值字段评分
        numeric_fields = ['volume', 'market_cap', 'pe_ratio', 'pb_ratio', 'roe']
        for field in numeric_fields:
            if field in data and data[field] is not None:
                try:
                    value = float(data[field])
                    # 根据数值合理性评分
                    if field == 'pe_ratio':
                        if 0 < value < 100:
                            field_scores[field] = 1.0
                        elif value > 0:
                            field_scores[field] = 0.7
                        else:
                            field_scores[field] = 0.3
                    elif field == 'volume':
                        if value > 0:
                            field_scores[field] = 1.0
                        else:
                            field_scores[field] = 0.5
                    else:
                        field_scores[field] = 0.8  # 默认评分
                except (ValueError, TypeError):
                    field_scores[field] = 0.0
            else:
                field_scores[field] = 0.5  # 缺失字段

        return field_scores

    def _update_source_quality_stats(self, source: str, report: DataQualityReport):
        """更新数据源质量统计"""
        if source not in self.source_quality_stats:
            self.source_quality_stats[source] = {
                'total_count': 0,
                'avg_quality_score': 0.0,
                'usable_rate': 0.0,
                'avg_issues_count': 0.0
            }

        stats = self.source_quality_stats[source]

        # 更新统计
        old_count = stats['total_count']
        new_count = old_count + 1

        # 加权平均更新
        stats['avg_quality_score'] = (
            stats['avg_quality_score'] * old_count + report.overall_score
        ) / new_count

        stats['usable_rate'] = (
            stats['usable_rate'] * old_count + (1.0 if report.is_usable else 0.0)
        ) / new_count

        stats['avg_issues_count'] = (
            stats['avg_issues_count'] * old_count + len(report.issues)
        ) / new_count

        stats['total_count'] = new_count

    def get_quality_report(self, symbol: str) -> Optional[DataQualityReport]:
        """获取股票的质量报告"""
        return self.quality_reports.get(symbol)

    def get_source_quality_stats(self) -> Dict[str, Dict[str, float]]:
        """获取数据源质量统计"""
        return self.source_quality_stats.copy()

    def get_quality_summary(self) -> Dict[str, Any]:
        """获取质量管理摘要"""
        total_reports = len(self.quality_reports)
        if total_reports == 0:
            return {'total_reports': 0}

        # 计算整体统计
        total_score = sum(report.overall_score for report in self.quality_reports.values())
        avg_score = total_score / total_reports

        usable_count = sum(1 for report in self.quality_reports.values() if report.is_usable)
        usable_rate = usable_count / total_reports

        total_issues = sum(len(report.issues) for report in self.quality_reports.values())
        avg_issues = total_issues / total_reports

        return {
            'total_reports': total_reports,
            'avg_quality_score': round(avg_score, 3),
            'usable_rate': round(usable_rate, 3),
            'avg_issues_count': round(avg_issues, 1),
            'source_stats': self.source_quality_stats
        }


# 全局数据质量管理器实例
data_quality_manager = DataQualityManager()
