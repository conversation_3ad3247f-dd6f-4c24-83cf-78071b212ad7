#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能对比测试脚本
对比原始版本和优化版本的性能差异
"""

import asyncio
import time
import pandas as pd
import numpy as np
from typing import Dict, List
import matplotlib.pyplot as plt
import seaborn as sns
from qbot.agents.optimized_stock_screener import OptimizedStockScreener
from qbot.common.enhanced_logger import get_logger


class PerformanceTestSuite:
    """性能测试套件"""
    
    def __init__(self):
        self.logger = get_logger("performance_test")
        self.optimized_screener = OptimizedStockScreener(batch_size=500, max_workers=20)
        
    def generate_test_data(self, size: int) -> pd.DataFrame:
        """生成测试数据"""
        np.random.seed(42)  # 确保可重复性
        
        data = {
            'symbol': [f'TEST{i:06d}' for i in range(size)],
            'market': ['CN_A'] * size,
            'current_price': np.random.uniform(1, 200, size),
            'volume': np.random.randint(1000, 10000000, size),
            'market_cap': np.random.uniform(1e8, 1e12, size),
            'pe_ratio': np.random.uniform(1, 100, size),
            'pb_ratio': np.random.uniform(0.1, 20, size),
            'roe': np.random.uniform(-0.5, 0.5, size),
            'debt_ratio': np.random.uniform(0, 1, size),
            'rsi': np.random.uniform(0, 100, size),
            'ma_trend': np.random.choice(['上升', '下降', '震荡'], size),
            'liquidity_ratio': np.random.uniform(0.001, 0.2, size),
            'volatility': np.random.uniform(0.05, 1.0, size),
            'daily_volume': np.random.uniform(1e5, 1e10, size),
            'short_interest': np.random.uniform(0, 0.3, size)
        }
        
        return pd.DataFrame(data)
    
    def test_screening_criteria_performance(self, data_sizes: List[int]) -> Dict:
        """测试筛选条件性能"""
        results = {
            'data_sizes': data_sizes,
            'iterrows_times': [],
            'vectorized_times': [],
            'speedup_ratios': []
        }
        
        criteria = {
            'fundamental': {
                'pe_ratio': {'min': 5, 'max': 30},
                'pb_ratio': {'min': 0.5, 'max': 5},
                'roe': {'min': 0.05, 'max': 1.0},
                'debt_ratio': {'min': 0, 'max': 0.6}
            },
            'technical': {
                'rsi_min': 30,
                'rsi_max': 70,
                'ma_trend': '上升'
            },
            'leverage': {
                'short_interest_max': 0.1,
                'liquidity_min': 0.02
            }
        }
        
        for size in data_sizes:
            print(f"\n📊 测试数据大小: {size:,} 行")
            
            # 生成测试数据
            test_data = self.generate_test_data(size)
            
            # 测试iterrows版本（模拟原始方法）
            iterrows_time = self._test_iterrows_screening(test_data.copy(), criteria)
            
            # 测试向量化版本
            vectorized_time = self._test_vectorized_screening(test_data.copy(), criteria)
            
            # 计算加速比
            speedup = iterrows_time / vectorized_time if vectorized_time > 0 else 0
            
            results['iterrows_times'].append(iterrows_time)
            results['vectorized_times'].append(vectorized_time)
            results['speedup_ratios'].append(speedup)
            
            print(f"  🐌 iterrows方法: {iterrows_time:.4f}秒")
            print(f"  🚀 向量化方法: {vectorized_time:.4f}秒")
            print(f"  ⚡ 加速比: {speedup:.2f}x")
        
        return results
    
    def _test_iterrows_screening(self, data: pd.DataFrame, criteria: Dict) -> float:
        """测试iterrows版本的筛选（模拟原始方法）"""
        start_time = time.time()
        
        # 模拟原始的iterrows方法
        filtered_indices = []
        
        for idx, row in data.iterrows():
            # 基本面筛选
            fundamental = criteria.get('fundamental', {})
            passes_fundamental = True
            
            for key, value in fundamental.items():
                if key in data.columns:
                    min_val = value.get('min', -np.inf)
                    max_val = value.get('max', np.inf)
                    if not (min_val <= row[key] <= max_val):
                        passes_fundamental = False
                        break
            
            if not passes_fundamental:
                continue
            
            # 技术面筛选
            technical = criteria.get('technical', {})
            
            # RSI筛选
            rsi_min = technical.get('rsi_min', 0)
            rsi_max = technical.get('rsi_max', 100)
            if not (rsi_min <= row['rsi'] <= rsi_max):
                continue
            
            # 均线趋势筛选
            ma_trend = technical.get('ma_trend', '不限')
            if ma_trend != '不限' and row['ma_trend'] != ma_trend:
                continue
            
            # 融资融券筛选
            leverage = criteria.get('leverage', {})
            
            if leverage.get('short_interest_max'):
                if row['short_interest'] > leverage['short_interest_max']:
                    continue
            
            if leverage.get('liquidity_min'):
                if row['liquidity_ratio'] < leverage['liquidity_min']:
                    continue
            
            filtered_indices.append(idx)
        
        # 应用筛选
        filtered_data = data.loc[filtered_indices]
        
        return time.time() - start_time
    
    def _test_vectorized_screening(self, data: pd.DataFrame, criteria: Dict) -> float:
        """测试向量化版本的筛选"""
        start_time = time.time()
        
        # 使用优化的向量化方法
        filtered_data = self.optimized_screener.apply_screening_criteria_vectorized(data, criteria)
        
        return time.time() - start_time
    
    def test_scoring_performance(self, data_sizes: List[int]) -> Dict:
        """测试评分计算性能"""
        results = {
            'data_sizes': data_sizes,
            'iterrows_times': [],
            'vectorized_times': [],
            'speedup_ratios': []
        }
        
        criteria = {
            'min_market_cap': 1e9,
            'min_daily_volume': 1e6,
            'min_price': 2.0,
            'max_volatility': 0.6,
            'min_liquidity_ratio': 0.02
        }
        
        for size in data_sizes:
            print(f"\n📈 测试评分计算 - 数据大小: {size:,} 行")
            
            # 生成测试数据
            test_data = self.generate_test_data(size)
            
            # 测试iterrows版本
            iterrows_time = self._test_iterrows_scoring(test_data.copy(), criteria)
            
            # 测试向量化版本
            vectorized_time = self._test_vectorized_scoring(test_data.copy(), criteria)
            
            # 计算加速比
            speedup = iterrows_time / vectorized_time if vectorized_time > 0 else 0
            
            results['iterrows_times'].append(iterrows_time)
            results['vectorized_times'].append(vectorized_time)
            results['speedup_ratios'].append(speedup)
            
            print(f"  🐌 iterrows方法: {iterrows_time:.4f}秒")
            print(f"  🚀 向量化方法: {vectorized_time:.4f}秒")
            print(f"  ⚡ 加速比: {speedup:.2f}x")
        
        return results
    
    def _test_iterrows_scoring(self, data: pd.DataFrame, criteria: Dict) -> float:
        """测试iterrows版本的评分计算"""
        start_time = time.time()
        
        scores = []
        for idx, row in data.iterrows():
            score = 0
            
            # 市值评分 (30%)
            market_cap_score = min(row['market_cap'] / criteria['min_market_cap'], 5) * 0.3
            
            # 流动性评分 (25%)
            liquidity_score = min(row['liquidity_ratio'] / criteria['min_liquidity_ratio'], 3) * 0.25
            
            # 价格稳定性评分 (20%)
            volatility_score = max(0, (criteria['max_volatility'] - row['volatility']) / criteria['max_volatility']) * 0.2
            
            # 成交量评分 (15%)
            volume_score = min(row['daily_volume'] / criteria['min_daily_volume'], 3) * 0.15
            
            # 基本面评分 (10%)
            fundamental_score = 0.1
            if 0 < row['pe_ratio'] < 30:
                fundamental_score *= 1.5
            if row['roe'] > 0.1:
                fundamental_score *= 1.5
            
            score = market_cap_score + liquidity_score + volatility_score + volume_score + fundamental_score
            scores.append(score)
        
        data['margin_score'] = scores
        
        return time.time() - start_time
    
    def _test_vectorized_scoring(self, data: pd.DataFrame, criteria: Dict) -> float:
        """测试向量化版本的评分计算"""
        start_time = time.time()
        
        # 使用优化的向量化方法
        scores = self.optimized_screener.calculate_margin_score_vectorized(data, criteria)
        data['margin_score'] = scores
        
        return time.time() - start_time
    
    async def test_batch_processing(self, total_stocks: int, batch_sizes: List[int]) -> Dict:
        """测试分批处理性能"""
        results = {
            'batch_sizes': batch_sizes,
            'processing_times': [],
            'memory_usage': [],
            'success_rates': []
        }
        
        # 生成股票列表
        stock_list = [f'TEST{i:06d}' for i in range(total_stocks)]
        
        for batch_size in batch_sizes:
            print(f"\n📦 测试批处理 - 批次大小: {batch_size}, 总股票数: {total_stocks}")
            
            # 设置批次大小
            self.optimized_screener.batch_size = batch_size
            
            start_time = time.time()
            
            # 执行批处理
            results_list = await self.optimized_screener.batch_process_stocks(
                stock_list, 'CN_A', self.optimized_screener.get_stock_data_batch
            )
            
            processing_time = time.time() - start_time
            success_rate = len(results_list) / total_stocks
            
            results['processing_times'].append(processing_time)
            results['success_rates'].append(success_rate)
            
            print(f"  ⏱️  处理时间: {processing_time:.2f}秒")
            print(f"  ✅ 成功率: {success_rate:.2%}")
            print(f"  🏃 处理速度: {total_stocks/processing_time:.1f} 股票/秒")
        
        return results
    
    def plot_performance_results(self, screening_results: Dict, scoring_results: Dict):
        """绘制性能测试结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 筛选性能对比
        axes[0, 0].plot(screening_results['data_sizes'], screening_results['iterrows_times'], 
                       'o-', label='iterrows方法', color='red')
        axes[0, 0].plot(screening_results['data_sizes'], screening_results['vectorized_times'], 
                       'o-', label='向量化方法', color='green')
        axes[0, 0].set_xlabel('数据大小')
        axes[0, 0].set_ylabel('处理时间 (秒)')
        axes[0, 0].set_title('筛选条件性能对比')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 筛选加速比
        axes[0, 1].plot(screening_results['data_sizes'], screening_results['speedup_ratios'], 
                       'o-', color='blue')
        axes[0, 1].set_xlabel('数据大小')
        axes[0, 1].set_ylabel('加速比')
        axes[0, 1].set_title('筛选条件加速比')
        axes[0, 1].grid(True)
        
        # 评分性能对比
        axes[1, 0].plot(scoring_results['data_sizes'], scoring_results['iterrows_times'], 
                       'o-', label='iterrows方法', color='red')
        axes[1, 0].plot(scoring_results['data_sizes'], scoring_results['vectorized_times'], 
                       'o-', label='向量化方法', color='green')
        axes[1, 0].set_xlabel('数据大小')
        axes[1, 0].set_ylabel('处理时间 (秒)')
        axes[1, 0].set_title('评分计算性能对比')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # 评分加速比
        axes[1, 1].plot(scoring_results['data_sizes'], scoring_results['speedup_ratios'], 
                       'o-', color='orange')
        axes[1, 1].set_xlabel('数据大小')
        axes[1, 1].set_ylabel('加速比')
        axes[1, 1].set_title('评分计算加速比')
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.savefig('performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()


async def main():
    """主测试函数"""
    print("🚀 股票筛选器性能优化测试")
    print("=" * 60)
    
    test_suite = PerformanceTestSuite()
    
    # 测试数据大小
    data_sizes = [1000, 5000, 10000, 20000, 50000]
    
    print("\n📊 开始筛选条件性能测试...")
    screening_results = test_suite.test_screening_criteria_performance(data_sizes)
    
    print("\n📈 开始评分计算性能测试...")
    scoring_results = test_suite.test_scoring_performance(data_sizes)
    
    print("\n📦 开始批处理性能测试...")
    batch_results = await test_suite.test_batch_processing(10000, [100, 500, 1000, 2000])
    
    # 性能总结
    print("\n" + "=" * 60)
    print("🎉 性能测试总结")
    print("=" * 60)
    
    print("\n📊 筛选条件优化效果:")
    avg_speedup_screening = sum(screening_results['speedup_ratios']) / len(screening_results['speedup_ratios'])
    print(f"  平均加速比: {avg_speedup_screening:.2f}x")
    print(f"  最大加速比: {max(screening_results['speedup_ratios']):.2f}x")
    
    print("\n📈 评分计算优化效果:")
    avg_speedup_scoring = sum(scoring_results['speedup_ratios']) / len(scoring_results['speedup_ratios'])
    print(f"  平均加速比: {avg_speedup_scoring:.2f}x")
    print(f"  最大加速比: {max(scoring_results['speedup_ratios']):.2f}x")
    
    print("\n📦 批处理性能:")
    best_batch_idx = batch_results['processing_times'].index(min(batch_results['processing_times']))
    best_batch_size = batch_results['batch_sizes'][best_batch_idx]
    best_time = batch_results['processing_times'][best_batch_idx]
    print(f"  最优批次大小: {best_batch_size}")
    print(f"  最快处理时间: {best_time:.2f}秒")
    print(f"  最高处理速度: {10000/best_time:.1f} 股票/秒")
    
    # 绘制性能图表
    try:
        test_suite.plot_performance_results(screening_results, scoring_results)
        print("\n📈 性能图表已保存为 performance_comparison.png")
    except ImportError:
        print("\n⚠️  matplotlib未安装，跳过图表生成")
    
    print("\n🎯 优化建议:")
    print("  1. 使用向量化操作替代iterrows()循环")
    print("  2. 合理设置批处理大小以平衡内存和性能")
    print("  3. 对大数据集启用分批处理")
    print("  4. 监控内存使用情况，避免内存溢出")


if __name__ == "__main__":
    asyncio.run(main())
