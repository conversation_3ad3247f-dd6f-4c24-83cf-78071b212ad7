#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
系统常量定义
将所有硬编码的数值定义为常量，提高可读性和维护性
"""

from dataclasses import dataclass
from typing import Dict, List, Tuple
from enum import Enum


class MarketType(Enum):
    """市场类型枚举"""
    CN_A = "CN_A"
    US = "US"
    HK = "HK"
    UK = "UK"
    JP = "JP"


class StockBoard(Enum):
    """股票板块枚举"""
    MAIN_BOARD = "主板"
    SME_BOARD = "中小板"
    GROWTH_BOARD = "创业板"
    STAR_BOARD = "科创板"
    BEIJING_BOARD = "北交所"


@dataclass(frozen=True)
class NetworkConstants:
    """网络相关常量"""
    # 超时设置
    DEFAULT_TIMEOUT: int = 10
    FAST_TIMEOUT: int = 5
    SLOW_TIMEOUT: int = 30
    
    # 重试设置
    DEFAULT_RETRY_COUNT: int = 3
    MAX_RETRY_COUNT: int = 5
    RETRY_DELAY_BASE: float = 1.0  # 指数退避基数
    
    # 速率限制
    DEFAULT_RATE_LIMIT: int = 60  # 每分钟请求数
    CONSERVATIVE_RATE_LIMIT: int = 30
    AGGRESSIVE_RATE_LIMIT: int = 120
    
    # 批处理设置
    DEFAULT_BATCH_SIZE: int = 500
    MIN_BATCH_SIZE: int = 50
    MAX_BATCH_SIZE: int = 2000
    
    # 并发设置
    DEFAULT_MAX_WORKERS: int = 20
    MIN_WORKERS: int = 5
    MAX_WORKERS: int = 50
    
    # 等待时间
    BATCH_INTERVAL: float = 0.1  # 批次间等待时间
    RATE_LIMIT_WAIT: float = 1.0  # 速率限制等待时间
    ERROR_RECOVERY_WAIT: float = 2.0  # 错误恢复等待时间


@dataclass(frozen=True)
class ScoringWeights:
    """评分权重常量"""
    # 综合评分权重
    FUNDAMENTAL_WEIGHT: float = 0.4  # 基本面权重 40%
    TECHNICAL_WEIGHT: float = 0.3    # 技术面权重 30%
    LIQUIDITY_WEIGHT: float = 0.2    # 流动性权重 20%
    MARGIN_WEIGHT: float = 0.1       # 融资融券权重 10%
    
    # 基本面评分权重
    PE_RATIO_WEIGHT: float = 0.3     # PE比率权重
    PB_RATIO_WEIGHT: float = 0.2     # PB比率权重
    ROE_WEIGHT: float = 0.3          # ROE权重
    DEBT_RATIO_WEIGHT: float = 0.2   # 负债率权重
    
    # 技术面评分权重
    RSI_WEIGHT: float = 0.4          # RSI权重
    MA_TREND_WEIGHT: float = 0.6     # 均线趋势权重
    
    # 融资融券评分权重
    MARKET_CAP_WEIGHT: float = 0.3   # 市值权重
    LIQUIDITY_RATIO_WEIGHT: float = 0.25  # 流动性比率权重
    VOLATILITY_WEIGHT: float = 0.2   # 波动率权重
    VOLUME_WEIGHT: float = 0.15      # 成交量权重
    FUNDAMENTAL_MARGIN_WEIGHT: float = 0.1  # 基本面权重


@dataclass(frozen=True)
class ScreeningThresholds:
    """筛选阈值常量"""
    # 基本面阈值
    PE_RATIO_MIN: float = 0.0
    PE_RATIO_MAX: float = 100.0
    PE_RATIO_GOOD_MAX: float = 25.0
    
    PB_RATIO_MIN: float = 0.0
    PB_RATIO_MAX: float = 20.0
    PB_RATIO_GOOD_MAX: float = 3.0
    
    ROE_MIN: float = -1.0
    ROE_MAX: float = 1.0
    ROE_GOOD_MIN: float = 0.1
    
    DEBT_RATIO_MIN: float = 0.0
    DEBT_RATIO_MAX: float = 1.0
    DEBT_RATIO_GOOD_MAX: float = 0.5
    
    # 技术面阈值
    RSI_MIN: float = 0.0
    RSI_MAX: float = 100.0
    RSI_OVERSOLD: float = 30.0
    RSI_OVERBOUGHT: float = 70.0
    
    # 价格阈值
    MIN_PRICE: float = 1.0
    MAX_PRICE: float = 1000.0
    PENNY_STOCK_THRESHOLD: float = 5.0
    
    # 成交量阈值
    MIN_VOLUME: int = 1000
    MIN_DAILY_VOLUME: float = 1e6
    
    # 市值阈值
    MIN_MARKET_CAP: float = 1e8      # 1亿
    LARGE_CAP_THRESHOLD: float = 1e11  # 1000亿
    
    # 流动性阈值
    MIN_LIQUIDITY_RATIO: float = 0.01
    GOOD_LIQUIDITY_RATIO: float = 0.05
    
    # 波动率阈值
    MIN_VOLATILITY: float = 0.05
    MAX_VOLATILITY: float = 1.0
    HIGH_VOLATILITY_THRESHOLD: float = 0.6


@dataclass(frozen=True)
class RecommendationThresholds:
    """投资建议阈值常量"""
    STRONG_BUY_THRESHOLD: float = 80.0
    BUY_THRESHOLD: float = 65.0
    HOLD_THRESHOLD: float = 50.0
    SELL_THRESHOLD: float = 35.0
    # 低于35分为强烈卖出
    
    # 风险等级阈值
    LOW_RISK_THRESHOLD: float = 0.4
    HIGH_RISK_THRESHOLD: float = 0.6


@dataclass(frozen=True)
class DataQualityThresholds:
    """数据质量阈值常量"""
    MIN_QUALITY_SCORE: float = 0.5
    GOOD_QUALITY_SCORE: float = 0.8
    EXCELLENT_QUALITY_SCORE: float = 0.95
    
    # 数据完整性权重
    PRICE_DATA_WEIGHT: float = 0.4
    VOLUME_DATA_WEIGHT: float = 0.2
    FINANCIAL_DATA_WEIGHT: float = 0.3
    CONSISTENCY_WEIGHT: float = 0.1


@dataclass(frozen=True)
class StockCodeRanges:
    """股票代码范围常量"""
    # 上海主板
    SH_MAIN_PREFIXES: Tuple[str, ...] = ('600', '601', '603', '605')
    SH_MAIN_RANGE: Tuple[int, int] = (0, 999)
    
    # 深圳主板
    SZ_MAIN_PREFIXES: Tuple[str, ...] = ('000', '001')
    SZ_MAIN_RANGE: Tuple[int, int] = (0, 999)
    
    # 中小板
    SME_PREFIX: str = '002'
    SME_RANGE: Tuple[int, int] = (0, 999)
    
    # 创业板
    GROWTH_PREFIXES: Tuple[str, ...] = ('300', '301')
    GROWTH_RANGE: Tuple[int, int] = (0, 999)
    
    # 科创板
    STAR_PREFIX: str = '688'
    STAR_RANGE: Tuple[int, int] = (0, 999)
    
    # 北交所
    BEIJING_PREFIXES: Tuple[str, ...] = ('430', '831', '832', '833', '834', '835', '836', '837', '838', '839')
    BEIJING_RANGE: Tuple[int, int] = (0, 999)
    
    # 代码生成步长
    MAIN_BOARD_STEP: int = 10
    GROWTH_BOARD_STEP: int = 20
    BEIJING_BOARD_STEP: int = 50


@dataclass(frozen=True)
class PerformanceConstants:
    """性能相关常量"""
    # 内存管理
    DEFAULT_MEMORY_THRESHOLD_MB: float = 1000.0
    CHUNK_TARGET_MEMORY_MB: float = 100.0
    MIN_CHUNK_SIZE: int = 100
    MAX_CHUNK_SIZE: int = 50000
    
    # 缓存设置
    DEFAULT_CACHE_TTL: int = 300  # 5分钟
    FAST_CACHE_TTL: int = 60      # 1分钟
    SLOW_CACHE_TTL: int = 3600    # 1小时
    
    # 性能监控
    SLOW_OPERATION_THRESHOLD: float = 5.0  # 慢操作阈值（秒）
    MEMORY_WARNING_THRESHOLD: float = 80.0  # 内存警告阈值（%）


@dataclass(frozen=True)
class LoggingConstants:
    """日志相关常量"""
    # 日志级别
    DEFAULT_LOG_LEVEL: str = "INFO"
    DEBUG_LOG_LEVEL: str = "DEBUG"
    PRODUCTION_LOG_LEVEL: str = "WARNING"
    
    # 日志文件大小
    MAX_LOG_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5
    
    # 日志格式
    CONSOLE_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    FILE_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"


class MarginTradingConstants:
    """融资融券常量"""
    
    # 中国A股融资融券标准
    CN_A_CRITERIA = {
        'min_market_cap': 3e9,        # 30亿市值
        'min_daily_volume': 5e7,      # 5000万日成交额
        'min_price': 5.0,             # 最低价格5元
        'max_volatility': 0.8,        # 最大波动率80%
        'min_liquidity_ratio': 0.02,  # 最低流动性比率2%
        'min_listing_days': 90        # 最少上市90天
    }
    
    # 美股融资融券标准
    US_CRITERIA = {
        'min_market_cap': 1e9,        # 10亿美元市值
        'min_daily_volume': 1e7,      # 1000万美元日成交额
        'min_price': 5.0,             # 最低价格5美元
        'max_volatility': 1.0,        # 最大波动率100%
        'min_liquidity_ratio': 0.01,  # 最低流动性比率1%
        'min_listing_days': 30        # 最少上市30天
    }
    
    # 港股融资融券标准
    HK_CRITERIA = {
        'min_market_cap': 5e9,        # 50亿港币市值
        'min_daily_volume': 2e7,      # 2000万港币日成交额
        'min_price': 1.0,             # 最低价格1港币
        'max_volatility': 0.9,        # 最大波动率90%
        'min_liquidity_ratio': 0.015, # 最低流动性比率1.5%
        'min_listing_days': 60        # 最少上市60天
    }


# 全局常量实例
NETWORK = NetworkConstants()
SCORING = ScoringWeights()
SCREENING = ScreeningThresholds()
RECOMMENDATION = RecommendationThresholds()
DATA_QUALITY = DataQualityThresholds()
STOCK_CODES = StockCodeRanges()
PERFORMANCE = PerformanceConstants()
LOGGING = LoggingConstants()
MARGIN_TRADING = MarginTradingConstants()


# 市场特定配置映射
MARKET_CONFIGS = {
    MarketType.CN_A: {
        'margin_criteria': MARGIN_TRADING.CN_A_CRITERIA,
        'trading_hours': {'start': '09:30', 'end': '15:00', 'timezone': 'Asia/Shanghai'},
        'currency': 'CNY',
        'decimal_places': 2
    },
    MarketType.US: {
        'margin_criteria': MARGIN_TRADING.US_CRITERIA,
        'trading_hours': {'start': '09:30', 'end': '16:00', 'timezone': 'America/New_York'},
        'currency': 'USD',
        'decimal_places': 2
    },
    MarketType.HK: {
        'margin_criteria': MARGIN_TRADING.HK_CRITERIA,
        'trading_hours': {'start': '09:30', 'end': '16:00', 'timezone': 'Asia/Hong_Kong'},
        'currency': 'HKD',
        'decimal_places': 3
    }
}
