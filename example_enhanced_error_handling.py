#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强错误处理和日志记录系统使用示例
演示如何使用新的Result对象和结构化日志
"""

import asyncio
from typing import Dict, Any
from qbot.common.result import Result, ErrorCode, ErrorSeverity, success, failure, from_exception
from qbot.common.enhanced_logger import get_logger


class StockDataService:
    """股票数据服务示例类"""
    
    def __init__(self):
        self.logger = get_logger("stock_data_service")
    
    async def get_stock_price(self, symbol: str, market: str) -> Result[Dict[str, Any]]:
        """获取股票价格 - 使用新的Result系统"""
        operation_name = f"get_stock_price_{symbol}"
        self.logger.start_operation(operation_name, symbol=symbol, market=market)
        
        try:
            # 参数验证
            if not symbol or not market:
                return failure(
                    ErrorCode.INVALID_PARAMETER,
                    "股票代码和市场不能为空",
                    ErrorSeverity.MEDIUM,
                    "StockDataService",
                    {'symbol': symbol, 'market': market}
                )
            
            # 市场支持检查
            if market not in ['CN_A', 'US', 'HK']:
                return failure(
                    ErrorCode.MARKET_NOT_SUPPORTED,
                    f"不支持的市场: {market}",
                    ErrorSeverity.HIGH,
                    "StockDataService",
                    {'symbol': symbol, 'market': market}
                )
            
            # 模拟数据获取
            if symbol == "ERROR_TEST":
                raise ValueError("模拟网络错误")
            elif symbol == "TIMEOUT_TEST":
                raise asyncio.TimeoutError("模拟超时错误")
            elif symbol == "NOT_FOUND":
                return failure(
                    ErrorCode.STOCK_NOT_FOUND,
                    f"股票 {symbol} 在 {market} 市场未找到",
                    ErrorSeverity.MEDIUM,
                    "StockDataService",
                    {'symbol': symbol, 'market': market}
                )
            
            # 模拟成功获取数据
            stock_data = {
                'symbol': symbol,
                'market': market,
                'price': 100.50,
                'volume': 1000000,
                'timestamp': '2024-01-01T10:00:00Z'
            }
            
            # 数据质量检查
            quality_issues = []
            if stock_data['price'] <= 0:
                quality_issues.append("价格异常")
            if stock_data['volume'] <= 0:
                quality_issues.append("成交量异常")
            
            # 创建成功结果
            result = success(stock_data)
            result.add_metadata('data_source', 'mock_api')
            result.add_metadata('quality_score', 0.95)
            
            # 添加警告（如果有质量问题）
            if quality_issues:
                from qbot.common.result import ErrorInfo
                warning = ErrorInfo(
                    code=ErrorCode.DATA_QUALITY_LOW,
                    message=f"数据质量问题: {', '.join(quality_issues)}",
                    severity=ErrorSeverity.LOW,
                    source="StockDataService",
                    context={'symbol': symbol, 'issues': quality_issues}
                )
                result.add_warning(warning)
            
            # 记录数据质量
            self.logger.log_data_quality(
                symbol, market, 
                result.metadata.get('quality_score', 0.0),
                quality_issues
            )
            
            self.logger.end_operation(operation_name, success=True, 
                                    price=stock_data['price'], 
                                    quality_score=result.metadata.get('quality_score'))
            
            return result
            
        except asyncio.TimeoutError as e:
            self.logger.log_exception(e,
                context={'symbol': symbol, 'market': market},
                error_code=ErrorCode.TIMEOUT,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error="timeout")
            
            return from_exception(e, ErrorCode.TIMEOUT, ErrorSeverity.HIGH, "StockDataService")
            
        except ValueError as e:
            self.logger.log_exception(e,
                context={'symbol': symbol, 'market': market},
                error_code=ErrorCode.NETWORK_ERROR,
                severity=ErrorSeverity.MEDIUM)
            
            self.logger.end_operation(operation_name, success=False, error="network_error")
            
            return from_exception(e, ErrorCode.NETWORK_ERROR, ErrorSeverity.MEDIUM, "StockDataService")
            
        except Exception as e:
            self.logger.log_exception(e,
                context={'symbol': symbol, 'market': market},
                error_code=ErrorCode.UNKNOWN_ERROR,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error="unknown")
            
            return from_exception(e, ErrorCode.UNKNOWN_ERROR, ErrorSeverity.HIGH, "StockDataService")
    
    async def batch_get_stock_prices(self, symbols: list, market: str) -> Result[Dict[str, Any]]:
        """批量获取股票价格"""
        operation_name = "batch_get_stock_prices"
        self.logger.start_operation(operation_name, 
                                   symbol_count=len(symbols), 
                                   market=market)
        
        try:
            results = {}
            errors = []
            warnings = []
            
            for symbol in symbols:
                result = await self.get_stock_price(symbol, market)
                
                if result.is_success():
                    results[symbol] = result.data
                    # 收集警告
                    warnings.extend(result.warnings)
                else:
                    errors.append({
                        'symbol': symbol,
                        'error_code': result.get_error_code().value if result.get_error_code() else 'UNKNOWN',
                        'error_message': result.get_error_message()
                    })
            
            # 创建批量结果
            batch_result = success({
                'successful_count': len(results),
                'failed_count': len(errors),
                'results': results,
                'errors': errors
            })
            
            # 添加元数据
            batch_result.add_metadata('total_symbols', len(symbols))
            batch_result.add_metadata('success_rate', len(results) / len(symbols))
            
            # 添加警告
            for warning in warnings:
                batch_result.add_warning(warning)
            
            self.logger.end_operation(operation_name, success=True,
                                    successful_count=len(results),
                                    failed_count=len(errors),
                                    success_rate=len(results) / len(symbols))
            
            return batch_result
            
        except Exception as e:
            self.logger.log_exception(e,
                context={'symbols': symbols, 'market': market},
                error_code=ErrorCode.UNKNOWN_ERROR,
                severity=ErrorSeverity.HIGH)
            
            self.logger.end_operation(operation_name, success=False, error="batch_failed")
            
            return from_exception(e, ErrorCode.UNKNOWN_ERROR, ErrorSeverity.HIGH, "StockDataService")


async def demonstrate_error_handling():
    """演示错误处理和日志记录"""
    print("🚀 演示增强错误处理和日志记录系统")
    print("=" * 60)
    
    service = StockDataService()
    
    # 测试用例
    test_cases = [
        ("AAPL", "US", "正常股票"),
        ("ERROR_TEST", "US", "模拟网络错误"),
        ("TIMEOUT_TEST", "US", "模拟超时错误"),
        ("NOT_FOUND", "US", "股票未找到"),
        ("", "US", "无效参数"),
        ("AAPL", "INVALID", "不支持的市场"),
    ]
    
    print("\n📊 单个股票测试:")
    print("-" * 40)
    
    for symbol, market, description in test_cases:
        print(f"\n🔍 测试: {description} ({symbol}, {market})")
        
        result = await service.get_stock_price(symbol, market)
        
        if result.is_success():
            print(f"  ✅ 成功: 价格 {result.data['price']}")
            if result.has_warnings():
                print(f"  ⚠️  警告数量: {len(result.warnings)}")
                for warning in result.warnings:
                    print(f"     - {warning.message}")
        else:
            print(f"  ❌ 失败: {result.get_error_code().value} - {result.get_error_message()}")
            if result.error and result.error.traceback_info:
                print(f"     详细错误: {result.error.details}")
    
    print("\n📈 批量获取测试:")
    print("-" * 40)
    
    batch_symbols = ["AAPL", "MSFT", "ERROR_TEST", "NOT_FOUND", "GOOGL"]
    batch_result = await service.batch_get_stock_prices(batch_symbols, "US")
    
    if batch_result.is_success():
        data = batch_result.data
        print(f"  ✅ 批量获取完成:")
        print(f"     成功: {data['successful_count']}/{batch_result.metadata['total_symbols']}")
        print(f"     成功率: {batch_result.metadata['success_rate']:.2%}")
        print(f"     警告数量: {len(batch_result.warnings)}")
        
        if data['errors']:
            print(f"  ❌ 失败的股票:")
            for error in data['errors']:
                print(f"     - {error['symbol']}: {error['error_code']} - {error['error_message']}")
    else:
        print(f"  ❌ 批量获取失败: {batch_result.get_error_message()}")
    
    print("\n📋 结果对象特性演示:")
    print("-" * 40)
    
    # 演示Result对象的各种方法
    result = await service.get_stock_price("AAPL", "US")
    
    print(f"  is_success(): {result.is_success()}")
    print(f"  is_failure(): {result.is_failure()}")
    print(f"  get_data_or_none(): {result.get_data_or_none()}")
    print(f"  get_data_or_default({}): {result.get_data_or_default({})}")
    print(f"  has_warnings(): {result.has_warnings()}")
    
    # 转换为字典
    result_dict = result.to_dict()
    print(f"  to_dict() keys: {list(result_dict.keys())}")
    
    print("\n🎉 演示完成!")


if __name__ == "__main__":
    asyncio.run(demonstrate_error_handling())
