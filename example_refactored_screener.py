#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重构股票筛选器使用示例
演示如何使用新的模块化、配置化的股票筛选系统
"""

import asyncio
import yaml
from pathlib import Path
from typing import Dict, Any, List
from qbot.config.constants import MarketType, NETWORK, SCORING, SCREENING
from qbot.agents.refactored_stock_screener import (
    RefactoredStockScreener, IStockDataProvider,
    StockListProvider, FundamentalFilter, TechnicalFilter
)
from qbot.data.data_source_interface import StockData
from qbot.common.result import Result, success, failure
from qbot.common.enhanced_logger import get_logger


class ConfigManager:
    """配置管理器 - 从YAML文件加载配置"""
    
    def __init__(self, config_file: str = "qbot/config/screening_config.yaml"):
        self.config_file = Path(config_file)
        self.config = self._load_config()
        self.logger = get_logger("config_manager")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            else:
                print(f"配置文件不存在: {self.config_file}")
                return self._get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'fundamental_screening': {
                'pe_ratio': {'min': 5.0, 'max': 30.0},
                'pb_ratio': {'min': 0.5, 'max': 5.0},
                'roe': {'min': 0.05, 'max': 1.0},
                'debt_ratio': {'min': 0.0, 'max': 0.6}
            },
            'technical_screening': {
                'rsi': {'min': 30.0, 'max': 70.0},
                'price': {'min': 2.0, 'max': 1000.0}
            },
            'scoring_weights': {
                'fundamental': 0.4,
                'technical': 0.3,
                'liquidity': 0.2,
                'margin': 0.1
            },
            'network_config': {
                'default_batch_size': 500,
                'max_workers': 20,
                'default_timeout': 10
            }
        }
    
    def get_screening_criteria(self, strategy_name: str = None) -> Dict[str, Any]:
        """获取筛选条件"""
        if strategy_name and 'preset_strategies' in self.config:
            strategy = self.config['preset_strategies'].get(strategy_name)
            if strategy:
                return {
                    'fundamental': strategy.get('fundamental', {}),
                    'technical': strategy.get('technical', {}),
                    'liquidity': strategy.get('liquidity', {})
                }
        
        # 返回默认筛选条件
        return {
            'fundamental': self.config.get('fundamental_screening', {}),
            'technical': self.config.get('technical_screening', {}),
            'liquidity': self.config.get('liquidity_screening', {})
        }
    
    def get_network_config(self) -> Dict[str, Any]:
        """获取网络配置"""
        return self.config.get('network_config', {})
    
    def get_scoring_weights(self) -> Dict[str, float]:
        """获取评分权重"""
        return self.config.get('scoring_weights', {})
    
    def list_preset_strategies(self) -> List[str]:
        """列出预设策略"""
        if 'preset_strategies' in self.config:
            return list(self.config['preset_strategies'].keys())
        return []


class MockStockDataProvider(IStockDataProvider):
    """模拟股票数据提供者 - 用于演示"""
    
    def __init__(self):
        self.logger = get_logger("mock_data_provider")
    
    async def get_stock_data(self, symbol: str, market: MarketType) -> Result[Dict[str, Any]]:
        """获取单只股票数据"""
        # 模拟网络延迟
        await asyncio.sleep(0.01)
        
        # 模拟数据
        import random
        data = {
            'symbol': symbol,
            'name': f'股票{symbol}',
            'market': market.value,
            'current_price': round(random.uniform(10, 200), 2),
            'volume': random.randint(10000, 10000000),
            'market_cap': random.uniform(1e8, 1e12),
            'pe_ratio': round(random.uniform(5, 50), 2),
            'pb_ratio': round(random.uniform(0.5, 10), 2),
            'roe': round(random.uniform(-0.2, 0.3), 4),
            'debt_ratio': round(random.uniform(0.1, 0.8), 2),
            'rsi': round(random.uniform(20, 80), 1),
            'ma_trend': random.choice(['上升', '下降', '震荡']),
            'liquidity_ratio': round(random.uniform(0.01, 0.1), 4),
            'volatility': round(random.uniform(0.1, 0.8), 2),
            'daily_volume': random.uniform(1e6, 1e9)
        }
        
        return success(data)
    
    async def get_batch_stock_data(self, symbols: List[str], market: MarketType) -> Result[List[Dict[str, Any]]]:
        """批量获取股票数据"""
        results = []
        for symbol in symbols:
            result = await self.get_stock_data(symbol, market)
            if result.is_success():
                results.append(result.data)
        
        return success(results)


class RefactoredScreenerDemo:
    """重构筛选器演示"""
    
    def __init__(self):
        self.logger = get_logger("screener_demo")
        self.config_manager = ConfigManager()
        self.data_provider = MockStockDataProvider()
        self.screener = RefactoredStockScreener(self.data_provider)
    
    def demonstrate_constants_usage(self):
        """演示常量使用"""
        print("🔧 常量和配置使用演示")
        print("=" * 50)
        
        print("📊 筛选阈值常量:")
        print(f"  PE比率范围: {SCREENING.PE_RATIO_MIN} - {SCREENING.PE_RATIO_MAX}")
        print(f"  优质PE阈值: {SCREENING.PE_RATIO_GOOD_MAX}")
        print(f"  RSI超卖线: {SCREENING.RSI_OVERSOLD}")
        print(f"  RSI超买线: {SCREENING.RSI_OVERBOUGHT}")
        print(f"  最小市值: {SCREENING.MIN_MARKET_CAP:,.0f}")
        
        print("\n⚖️ 评分权重常量:")
        print(f"  基本面权重: {SCORING.FUNDAMENTAL_WEIGHT}")
        print(f"  技术面权重: {SCORING.TECHNICAL_WEIGHT}")
        print(f"  流动性权重: {SCORING.LIQUIDITY_WEIGHT}")
        print(f"  PE比率权重: {SCORING.PE_RATIO_WEIGHT}")
        print(f"  RSI权重: {SCORING.RSI_WEIGHT}")
        
        print("\n🌐 网络配置常量:")
        print(f"  默认超时: {NETWORK.DEFAULT_TIMEOUT}秒")
        print(f"  默认批次大小: {NETWORK.DEFAULT_BATCH_SIZE}")
        print(f"  最大并发数: {NETWORK.DEFAULT_MAX_WORKERS}")
        print(f"  重试次数: {NETWORK.DEFAULT_RETRY_COUNT}")
    
    def demonstrate_config_loading(self):
        """演示配置加载"""
        print("\n📁 配置文件加载演示")
        print("=" * 50)
        
        # 显示预设策略
        strategies = self.config_manager.list_preset_strategies()
        print(f"可用的预设策略: {strategies}")
        
        # 显示各策略的配置
        for strategy in strategies:
            print(f"\n📋 {strategy}策略配置:")
            criteria = self.config_manager.get_screening_criteria(strategy)
            
            if 'fundamental' in criteria:
                print("  基本面条件:")
                for key, value in criteria['fundamental'].items():
                    print(f"    {key}: {value}")
            
            if 'technical' in criteria:
                print("  技术面条件:")
                for key, value in criteria['technical'].items():
                    print(f"    {key}: {value}")
        
        # 显示网络配置
        network_config = self.config_manager.get_network_config()
        print(f"\n🌐 网络配置: {network_config}")
    
    def demonstrate_single_responsibility(self):
        """演示单一职责原则"""
        print("\n🎯 单一职责原则演示")
        print("=" * 50)
        
        # 演示各个组件的独立功能
        import pandas as pd
        import numpy as np
        
        # 生成测试数据
        test_data = pd.DataFrame({
            'symbol': ['TEST001', 'TEST002', 'TEST003'],
            'pe_ratio': [15.0, 35.0, 8.0],
            'pb_ratio': [2.0, 6.0, 1.5],
            'roe': [0.12, 0.05, 0.18],
            'debt_ratio': [0.3, 0.7, 0.2],
            'rsi': [45.0, 75.0, 35.0],
            'ma_trend': ['上升', '下降', '上升'],
            'current_price': [50.0, 120.0, 25.0],
            'liquidity_ratio': [0.03, 0.01, 0.05]
        })
        
        print("📊 原始测试数据:")
        print(test_data[['symbol', 'pe_ratio', 'pb_ratio', 'roe', 'rsi']].to_string(index=False))
        
        # 1. 基本面筛选器
        fundamental_filter = FundamentalFilter()
        fundamental_criteria = {
            'fundamental': {
                'pe_ratio': {'min': 5.0, 'max': 30.0},
                'pb_ratio': {'min': 0.5, 'max': 5.0},
                'roe': {'min': 0.1, 'max': 1.0}
            }
        }
        
        filtered_fundamental = fundamental_filter.apply_filter(test_data, fundamental_criteria)
        print(f"\n🔍 基本面筛选后: {len(filtered_fundamental)}/{len(test_data)} 只股票通过")
        if not filtered_fundamental.empty:
            print(filtered_fundamental[['symbol', 'pe_ratio', 'pb_ratio', 'roe']].to_string(index=False))
        
        # 2. 技术面筛选器
        technical_filter = TechnicalFilter()
        technical_criteria = {
            'technical': {
                'rsi_min': 30.0,
                'rsi_max': 70.0,
                'ma_trend': '上升'
            }
        }
        
        filtered_technical = technical_filter.apply_filter(test_data, technical_criteria)
        print(f"\n📈 技术面筛选后: {len(filtered_technical)}/{len(test_data)} 只股票通过")
        if not filtered_technical.empty:
            print(filtered_technical[['symbol', 'rsi', 'ma_trend']].to_string(index=False))
    
    async def demonstrate_complete_workflow(self):
        """演示完整的筛选流程"""
        print("\n🔄 完整筛选流程演示")
        print("=" * 50)
        
        # 使用价值投资策略
        strategy_name = "value_investing"
        criteria = self.config_manager.get_screening_criteria(strategy_name)
        
        print(f"📋 使用策略: {strategy_name}")
        print(f"筛选条件: {criteria}")
        
        # 执行筛选
        print("\n🚀 开始执行股票筛选...")
        result = await self.screener.screen_stocks(
            market=MarketType.CN_A,
            criteria=criteria,
            limit=100  # 限制100只股票用于演示
        )
        
        if result.is_success():
            recommendations = result.data
            metadata = result.metadata
            
            print(f"✅ 筛选完成!")
            print(f"📊 处理统计:")
            print(f"  总股票数: {metadata.get('total_symbols', 0)}")
            print(f"  筛选后数量: {metadata.get('filtered_count', 0)}")
            print(f"  数据获取成功率: {metadata.get('success_rate', 0):.2%}")
            print(f"  最终推荐数量: {len(recommendations)}")
            
            # 显示前10个推荐
            if recommendations:
                print(f"\n🏆 前10个推荐股票:")
                print("-" * 80)
                print(f"{'排名':<4} {'代码':<8} {'评分':<6} {'建议':<8} {'风险':<8} {'PE':<6} {'ROE':<8}")
                print("-" * 80)
                
                for i, stock in enumerate(recommendations[:10]):
                    print(f"{stock['rank']:<4} {stock['symbol']:<8} {stock['total_score']:<6.1f} "
                          f"{stock['recommendation']:<8} {stock['risk_level']:<8} "
                          f"{stock['pe_ratio']:<6.1f} {stock['roe']:<8.2%}")
        else:
            print(f"❌ 筛选失败: {result.get_error_message()}")
    
    async def demonstrate_different_strategies(self):
        """演示不同策略的效果"""
        print("\n🎯 不同策略效果对比")
        print("=" * 50)
        
        strategies = self.config_manager.list_preset_strategies()
        
        for strategy in strategies[:3]:  # 演示前3个策略
            print(f"\n📋 测试策略: {strategy}")
            criteria = self.config_manager.get_screening_criteria(strategy)
            
            result = await self.screener.screen_stocks(
                market=MarketType.CN_A,
                criteria=criteria,
                limit=50  # 小样本测试
            )
            
            if result.is_success():
                recommendations = result.data
                metadata = result.metadata
                
                print(f"  筛选结果: {metadata.get('filtered_count', 0)} 只股票通过筛选")
                print(f"  推荐数量: {len(recommendations)}")
                
                if recommendations:
                    avg_score = sum(r['total_score'] for r in recommendations) / len(recommendations)
                    print(f"  平均评分: {avg_score:.1f}")
                    
                    # 统计推荐分布
                    rec_counts = {}
                    for r in recommendations:
                        rec = r['recommendation']
                        rec_counts[rec] = rec_counts.get(rec, 0) + 1
                    
                    print(f"  推荐分布: {rec_counts}")
            else:
                print(f"  ❌ 策略执行失败: {result.get_error_message()}")


async def main():
    """主演示函数"""
    print("🚀 重构股票筛选器演示")
    print("=" * 60)
    
    demo = RefactoredScreenerDemo()
    
    # 1. 常量使用演示
    demo.demonstrate_constants_usage()
    
    # 2. 配置加载演示
    demo.demonstrate_config_loading()
    
    # 3. 单一职责原则演示
    demo.demonstrate_single_responsibility()
    
    # 4. 完整流程演示
    await demo.demonstrate_complete_workflow()
    
    # 5. 不同策略对比
    await demo.demonstrate_different_strategies()
    
    print("\n" + "=" * 60)
    print("🎉 重构演示完成!")
    print("\n💡 重构改进总结:")
    print("  1. ✅ 消除魔术数字 - 所有数值都定义为常量")
    print("  2. ✅ 外部配置管理 - 从YAML文件加载配置")
    print("  3. ✅ 单一职责原则 - 每个类只负责一个功能")
    print("  4. ✅ 依赖注入 - 组件间松耦合")
    print("  5. ✅ 接口抽象 - 易于扩展和测试")
    print("  6. ✅ 配置化策略 - 预设多种投资策略")
    print("  7. ✅ 模块化设计 - 组件可独立使用和测试")


if __name__ == "__main__":
    asyncio.run(main())
