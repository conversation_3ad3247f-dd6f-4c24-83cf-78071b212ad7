# 股票筛选配置文件
# 将所有硬编码的筛选条件和参数定义为配置

# 基本面筛选配置
fundamental_screening:
  # PE比率筛选
  pe_ratio:
    min: 5.0          # 最小PE比率
    max: 30.0         # 最大PE比率
    good_threshold: 25.0  # 优质股票PE阈值
    
  # PB比率筛选
  pb_ratio:
    min: 0.5          # 最小PB比率
    max: 5.0          # 最大PB比率
    good_threshold: 3.0   # 优质股票PB阈值
    
  # ROE筛选
  roe:
    min: 0.05         # 最小ROE (5%)
    max: 1.0          # 最大ROE (100%)
    good_threshold: 0.1   # 优质股票ROE阈值 (10%)
    
  # 负债率筛选
  debt_ratio:
    min: 0.0          # 最小负债率
    max: 0.6          # 最大负债率
    good_threshold: 0.5   # 优质股票负债率阈值

# 技术面筛选配置
technical_screening:
  # RSI筛选
  rsi:
    min: 30.0         # RSI最小值（超卖线）
    max: 70.0         # RSI最大值（超买线）
    oversold: 30.0    # 超卖阈值
    overbought: 70.0  # 超买阈值
    
  # 价格筛选
  price:
    min: 2.0          # 最低价格（避免仙股）
    max: 1000.0       # 最高价格
    penny_stock_threshold: 5.0  # 仙股阈值
    
  # 均线趋势
  ma_trend:
    allowed_values: ["上升", "下降", "震荡", "不限"]
    default: "不限"

# 流动性筛选配置
liquidity_screening:
  # 成交量筛选
  volume:
    min: 10000        # 最小成交量
    daily_min: 1000000  # 最小日成交额
    
  # 市值筛选
  market_cap:
    min: 100000000    # 最小市值（1亿）
    large_cap_threshold: 100000000000  # 大盘股阈值（1000亿）
    
  # 流动性比率
  liquidity_ratio:
    min: 0.01         # 最小流动性比率
    good_threshold: 0.05  # 良好流动性阈值

# 评分权重配置
scoring_weights:
  # 主要权重分配
  fundamental: 0.4    # 基本面权重 40%
  technical: 0.3      # 技术面权重 30%
  liquidity: 0.2      # 流动性权重 20%
  margin: 0.1         # 融资融券权重 10%
  
  # 基本面细分权重
  fundamental_details:
    pe_ratio: 0.3     # PE比率权重
    pb_ratio: 0.2     # PB比率权重
    roe: 0.3          # ROE权重
    debt_ratio: 0.2   # 负债率权重
    
  # 技术面细分权重
  technical_details:
    rsi: 0.4          # RSI权重
    ma_trend: 0.6     # 均线趋势权重

# 投资建议阈值配置
recommendation_thresholds:
  strong_buy: 80.0    # 强烈买入阈值
  buy: 65.0           # 买入阈值
  hold: 50.0          # 持有阈值
  sell: 35.0          # 卖出阈值
  # 低于35分为强烈卖出

# 风险评估配置
risk_assessment:
  # 风险等级阈值
  low_risk: 0.4       # 低风险阈值
  high_risk: 0.6      # 高风险阈值
  
  # 风险因子权重
  volatility_weight: 0.6    # 波动率权重
  debt_weight: 0.4          # 负债率权重

# 数据质量配置
data_quality:
  min_quality_score: 0.5      # 最低数据质量要求
  good_quality_score: 0.8     # 良好数据质量阈值
  excellent_quality_score: 0.95  # 优秀数据质量阈值
  
  # 数据完整性权重
  price_data_weight: 0.4      # 价格数据权重
  volume_data_weight: 0.2     # 成交量数据权重
  financial_data_weight: 0.3  # 财务数据权重
  consistency_weight: 0.1     # 一致性权重

# 网络和性能配置
network_config:
  # 超时设置
  default_timeout: 10         # 默认超时时间（秒）
  fast_timeout: 5             # 快速超时时间（秒）
  slow_timeout: 30            # 慢速超时时间（秒）
  
  # 重试设置
  max_retry_count: 3          # 最大重试次数
  retry_delay_base: 1.0       # 重试延迟基数（秒）
  
  # 批处理设置
  default_batch_size: 500     # 默认批处理大小
  min_batch_size: 50          # 最小批处理大小
  max_batch_size: 2000        # 最大批处理大小
  
  # 并发设置
  max_workers: 20             # 最大并发数
  
  # 等待时间
  batch_interval: 0.1         # 批次间等待时间（秒）
  rate_limit_wait: 1.0        # 速率限制等待时间（秒）

# 融资融券配置
margin_trading:
  # 中国A股标准
  cn_a:
    min_market_cap: 3000000000      # 最小市值（30亿）
    min_daily_volume: 50000000      # 最小日成交额（5000万）
    min_price: 5.0                  # 最低价格（5元）
    max_volatility: 0.8             # 最大波动率（80%）
    min_liquidity_ratio: 0.02       # 最低流动性比率（2%）
    min_listing_days: 90            # 最少上市天数
    
  # 美股标准
  us:
    min_market_cap: 1000000000      # 最小市值（10亿美元）
    min_daily_volume: 10000000      # 最小日成交额（1000万美元）
    min_price: 5.0                  # 最低价格（5美元）
    max_volatility: 1.0             # 最大波动率（100%）
    min_liquidity_ratio: 0.01       # 最低流动性比率（1%）
    min_listing_days: 30            # 最少上市天数
    
  # 港股标准
  hk:
    min_market_cap: 5000000000      # 最小市值（50亿港币）
    min_daily_volume: 20000000      # 最小日成交额（2000万港币）
    min_price: 1.0                  # 最低价格（1港币）
    max_volatility: 0.9             # 最大波动率（90%）
    min_liquidity_ratio: 0.015      # 最低流动性比率（1.5%）
    min_listing_days: 60            # 最少上市天数

# 预设筛选策略
preset_strategies:
  # 价值投资策略
  value_investing:
    name: "价值投资策略"
    description: "寻找低估值、高ROE的优质股票"
    fundamental:
      pe_ratio: {min: 5.0, max: 20.0}
      pb_ratio: {min: 0.5, max: 3.0}
      roe: {min: 0.1, max: 1.0}
      debt_ratio: {min: 0.0, max: 0.5}
    technical:
      rsi_min: 20.0
      rsi_max: 80.0
    liquidity:
      market_cap_min: 1000000000
      volume_min: 100000
      
  # 成长投资策略
  growth_investing:
    name: "成长投资策略"
    description: "寻找高成长性的股票"
    fundamental:
      roe: {min: 0.15, max: 1.0}
      debt_ratio: {min: 0.0, max: 0.4}
    technical:
      ma_trend: "上升"
      rsi_min: 40.0
      rsi_max: 80.0
    liquidity:
      market_cap_min: 500000000
      
  # 稳健投资策略
  conservative_investing:
    name: "稳健投资策略"
    description: "寻找稳定、低风险的股票"
    fundamental:
      pe_ratio: {min: 8.0, max: 25.0}
      pb_ratio: {min: 0.8, max: 4.0}
      roe: {min: 0.08, max: 1.0}
      debt_ratio: {min: 0.0, max: 0.4}
    technical:
      rsi_min: 35.0
      rsi_max: 65.0
    liquidity:
      market_cap_min: 5000000000
      liquidity_min: 0.03
      
  # 技术分析策略
  technical_analysis:
    name: "技术分析策略"
    description: "基于技术指标的短期交易策略"
    technical:
      rsi_min: 30.0
      rsi_max: 70.0
      ma_trend: "上升"
    liquidity:
      volume_min: 500000
      liquidity_min: 0.02

# 市场特定配置
market_specific:
  # 中国A股特定配置
  cn_a:
    trading_hours:
      start: "09:30"
      end: "15:00"
      timezone: "Asia/Shanghai"
    currency: "CNY"
    decimal_places: 2
    
  # 美股特定配置
  us:
    trading_hours:
      start: "09:30"
      end: "16:00"
      timezone: "America/New_York"
    currency: "USD"
    decimal_places: 2
    
  # 港股特定配置
  hk:
    trading_hours:
      start: "09:30"
      end: "16:00"
      timezone: "Asia/Hong_Kong"
    currency: "HKD"
    decimal_places: 3
