#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的安全导入管理器
避免ImportError隐藏问题
"""

import importlib
from typing import Dict, Any, Optional


class SafeImportManager:
    """简化的安全导入管理器"""

    def __init__(self):
        self._import_cache: Dict[str, Any] = {}

    def safe_import(self, module_name: str, attribute: Optional[str] = None) -> Optional[Any]:
        """安全导入模块或属性"""
        cache_key = f"{module_name}.{attribute}" if attribute else module_name

        # 检查缓存
        if cache_key in self._import_cache:
            return self._import_cache[cache_key]

        try:
            module = importlib.import_module(module_name)

            if attribute:
                if hasattr(module, attribute):
                    result = getattr(module, attribute)
                else:
                    result = None
            else:
                result = module

            self._import_cache[cache_key] = result
            return result

        except ImportError:
            self._import_cache[cache_key] = None
            return None
        except Exception:
            self._import_cache[cache_key] = None
            return None


# 全局实例
safe_import_manager = SafeImportManager()


# 便捷函数
def safe_import(module_name: str, attribute: Optional[str] = None) -> Optional[Any]:
    """便捷的安全导入函数"""
    return safe_import_manager.safe_import(module_name, attribute)


# 预定义的安全导入函数
def get_tdx_reader():
    """安全获取通达信数据读取器"""
    return safe_import('qbot.data.tdx_data_reader', 'tdx_reader')


def get_online_provider():
    """安全获取在线数据提供者"""
    return safe_import('qbot.data.online_data_provider', 'online_data_provider')


def get_real_data_manager():
    """安全获取真实数据管理器"""
    return safe_import('qbot.data.real_data_sources', 'real_data_manager')


def should_use_tdx_local_data():
    """安全检查是否使用通达信本地数据"""
    func = safe_import('qbot.data.tdx_data_reader', 'should_use_tdx_local_data')
    return func() if func else False


def get_realtime_stock_data(symbol: str, market: str) -> Optional[Dict[str, Any]]:
    """安全获取实时股票数据"""
    func = safe_import('qbot.data.online_data_provider', 'get_realtime_stock_data')
    return func(symbol, market) if func else None


def is_market_open_now(market: str) -> bool:
    """安全检查市场是否开盘"""
    func = safe_import('qbot.data.online_data_provider', 'is_market_open_now')
    return func(market) if func else False
