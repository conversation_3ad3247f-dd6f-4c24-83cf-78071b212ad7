#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整的数据质量系统演示
展示从数据获取到质量感知筛选的完整流程
"""

import asyncio
import random
from typing import Dict, List, Any
from qbot.config.constants import MarketType
from qbot.agents.quality_aware_screener import QualityAwareStockScreener, IStockDataProvider
from qbot.common.result import Result, success, failure
from qbot.common.enhanced_logger import get_logger


class MultiSourceDataProvider(IStockDataProvider):
    """多数据源提供者 - 模拟不同质量的数据源"""
    
    def __init__(self):
        self.logger = get_logger("multi_source_provider")
        
        # 模拟不同数据源的质量特征
        self.data_sources = {
            'premium_api': {
                'quality': 0.95,
                'completeness': 0.98,
                'accuracy': 0.97,
                'format': 'standard'
            },
            'free_api': {
                'quality': 0.75,
                'completeness': 0.85,
                'accuracy': 0.80,
                'format': 'mixed'
            },
            'web_scraping': {
                'quality': 0.60,
                'completeness': 0.70,
                'accuracy': 0.75,
                'format': 'inconsistent'
            },
            'legacy_system': {
                'quality': 0.45,
                'completeness': 0.60,
                'accuracy': 0.65,
                'format': 'old'
            }
        }
    
    async def get_stock_data(self, symbol: str, market: MarketType) -> Result[Dict[str, Any]]:
        """获取单只股票数据"""
        # 随机选择数据源
        source_name = random.choice(list(self.data_sources.keys()))
        source_config = self.data_sources[source_name]
        
        # 模拟网络延迟
        await asyncio.sleep(0.01)
        
        # 生成模拟数据
        data = self._generate_stock_data(symbol, source_name, source_config)
        
        return success(data)
    
    async def get_batch_stock_data(self, symbols: List[str], market: MarketType) -> Result[List[Dict[str, Any]]]:
        """批量获取股票数据"""
        results = []
        
        for symbol in symbols:
            result = await self.get_stock_data(symbol, market)
            if result.is_success():
                results.append(result.data)
        
        return success(results)
    
    def _generate_stock_data(self, symbol: str, source: str, config: Dict) -> Dict[str, Any]:
        """生成模拟股票数据"""
        quality = config['quality']
        completeness = config['completeness']
        accuracy = config['accuracy']
        
        # 基础数据
        base_price = random.uniform(10, 200)
        
        data = {
            'source': source,
            'symbol': symbol,
            'name': f'股票{symbol}',
            'market': 'CN_A'
        }
        
        # 根据数据源格式生成不同的字段名
        if config['format'] == 'standard':
            data.update({
                'current_price': round(base_price, 2),
                'open_price': round(base_price * random.uniform(0.98, 1.02), 2),
                'high_price': round(base_price * random.uniform(1.00, 1.05), 2),
                'low_price': round(base_price * random.uniform(0.95, 1.00), 2),
                'volume': random.randint(100000, 10000000),
                'market_cap': random.uniform(1e9, 1e12)
            })
        elif config['format'] == 'mixed':
            # 混合格式，有些字段名不标准
            data.update({
                'price': base_price,  # 非标准字段名
                'vol': random.randint(100000, 10000000),
                'market_value': random.uniform(1e9, 1e12)
            })
        elif config['format'] == 'inconsistent':
            # 不一致格式，字段名混乱
            data.update({
                'current_pri': base_price,
                'volume_traded': random.randint(100000, 10000000),
                'cap': random.uniform(1e9, 1e12)
            })
        else:  # old format
            # 旧格式，字段名简化
            data.update({
                'p': base_price,
                'v': random.randint(100000, 10000000),
                'mc': random.uniform(1e9, 1e12)
            })
        
        # 根据完整性随机缺失一些字段
        if random.random() > completeness:
            # 随机移除一些字段
            fields_to_remove = random.sample(list(data.keys()), 
                                           min(2, len(data) - 3))  # 保留至少3个字段
            for field in fields_to_remove:
                if field not in ['source', 'symbol']:  # 保留关键字段
                    data[field] = None
        
        # 根据准确性添加一些错误数据
        if random.random() > accuracy:
            # 添加错误数据
            error_types = ['negative_price', 'extreme_value', 'wrong_type']
            error_type = random.choice(error_types)
            
            if error_type == 'negative_price' and 'current_price' in data:
                data['current_price'] = -abs(data['current_price'])
            elif error_type == 'extreme_value' and 'pe_ratio' in data:
                data['pe_ratio'] = random.uniform(1000, 10000)
            elif error_type == 'wrong_type' and 'volume' in data:
                data['volume'] = 'N/A'
        
        # 添加财务数据
        if random.random() < completeness:
            data.update({
                'pe_ratio': random.uniform(5, 50) if random.random() < accuracy else random.uniform(-100, 1000),
                'pb_ratio': random.uniform(0.5, 10) if random.random() < accuracy else random.uniform(-10, 100),
                'roe': random.uniform(0.05, 0.3) if random.random() < accuracy else random.uniform(-5, 5),
                'debt_ratio': random.uniform(0.1, 0.8) if random.random() < accuracy else random.uniform(-1, 10)
            })
        
        # 添加技术指标
        if random.random() < completeness:
            data.update({
                'rsi': random.uniform(20, 80) if random.random() < accuracy else random.uniform(-50, 150),
                'ma_trend': random.choice(['上升', '下降', '震荡']),
                'volatility': random.uniform(0.1, 0.8) if random.random() < accuracy else random.uniform(-1, 5)
            })
        
        return data


class CompleteQualitySystemDemo:
    """完整质量系统演示"""
    
    def __init__(self):
        self.logger = get_logger("quality_system_demo")
        
        # 创建多数据源提供者
        self.data_provider = MultiSourceDataProvider()
        
        # 创建质量感知筛选器
        self.quality_screener = QualityAwareStockScreener(self.data_provider)
    
    async def demonstrate_quality_aware_screening(self):
        """演示质量感知筛选"""
        print("🔍 质量感知股票筛选演示")
        print("=" * 60)
        
        # 定义筛选条件
        criteria = {
            'fundamental': {
                'pe_ratio': {'min': 5.0, 'max': 30.0},
                'pb_ratio': {'min': 0.5, 'max': 5.0},
                'roe': {'min': 0.05, 'max': 1.0}
            },
            'technical': {
                'rsi_min': 30.0,
                'rsi_max': 70.0
            },
            'liquidity': {
                'volume_min': 100000,
                'market_cap_min': 1e9
            }
        }
        
        print("📋 筛选条件:")
        print(f"  PE比率: 5.0 - 30.0")
        print(f"  PB比率: 0.5 - 5.0")
        print(f"  ROE: 5% - 100%")
        print(f"  RSI: 30 - 70")
        print(f"  最小成交量: 100,000")
        print(f"  最小市值: 10亿")
        
        # 执行质量感知筛选
        print(f"\n🚀 开始执行质量感知筛选...")
        
        result = await self.quality_screener.screen_stocks_with_quality_control(
            market=MarketType.CN_A,
            criteria=criteria,
            limit=100,  # 限制100只股票
            min_quality_score=0.6,  # 最低质量要求
            quality_weight=0.15  # 质量权重15%
        )
        
        if result.is_success():
            screening_result = result.data
            
            print(f"✅ 筛选完成!")
            print(f"\n📊 筛选统计:")
            print(f"  总处理数量: {screening_result.total_processed_count}")
            print(f"  质量过滤数量: {screening_result.quality_filtered_count}")
            print(f"  最终推荐数量: {len(screening_result.recommendations)}")
            print(f"  平均数据质量: {screening_result.avg_data_quality:.3f}")
            
            # 显示质量摘要
            quality_summary = screening_result.quality_summary
            print(f"\n📈 数据质量分布:")
            distribution = quality_summary.get('quality_distribution', {})
            percentages = quality_summary.get('quality_percentages', {})
            
            print(f"  优秀 (≥0.95): {distribution.get('excellent', 0)} 只 ({percentages.get('excellent', 0):.1f}%)")
            print(f"  良好 (0.8-0.95): {distribution.get('good', 0)} 只 ({percentages.get('good', 0):.1f}%)")
            print(f"  可接受 (0.5-0.8): {distribution.get('acceptable', 0)} 只 ({percentages.get('acceptable', 0):.1f}%)")
            print(f"  较差 (<0.5): {distribution.get('poor', 0)} 只 ({percentages.get('poor', 0):.1f}%)")
            
            # 显示数据源性能
            print(f"\n🔗 数据源性能:")
            for source, stats in screening_result.data_source_performance.items():
                print(f"  {source}:")
                print(f"    处理数量: {stats['total_count']}")
                print(f"    平均质量: {stats['avg_quality_score']:.3f}")
                print(f"    可用率: {stats['usable_rate']:.1%}")
                print(f"    平均问题数: {stats['avg_issues_count']:.1f}")
            
            # 显示前10个推荐
            if screening_result.recommendations:
                print(f"\n🏆 前10个推荐股票:")
                print("-" * 100)
                print(f"{'排名':<4} {'代码':<8} {'原始评分':<8} {'质量评分':<8} {'调整后评分':<10} {'质量调整':<8} {'推荐':<8}")
                print("-" * 100)
                
                for stock in screening_result.recommendations[:10]:
                    rank = stock.get('rank', 0)
                    symbol = stock.get('symbol', '')
                    original_score = stock.get('total_score', 0)
                    quality_score = stock.get('data_quality_score', 0)
                    adjusted_score = stock.get('adjusted_total_score', original_score)
                    quality_adjustment = stock.get('quality_adjustment', 0)
                    recommendation = stock.get('recommendation', '')
                    
                    print(f"{rank:<4} {symbol:<8} {original_score:<8.1f} {quality_score:<8.3f} "
                          f"{adjusted_score:<10.1f} {quality_adjustment:+<8.1f} {recommendation:<8}")
        else:
            print(f"❌ 筛选失败: {result.get_error_message()}")
    
    def demonstrate_quality_insights(self):
        """演示质量洞察"""
        print(f"\n💡 数据质量洞察分析")
        print("=" * 60)
        
        insights = self.quality_screener.get_quality_insights()
        
        # 整体质量摘要
        overall = insights['overall_summary']
        print(f"📊 整体质量状况:")
        print(f"  处理总数: {overall.get('total_reports', 0)}")
        print(f"  平均质量评分: {overall.get('avg_quality_score', 0):.3f}")
        print(f"  数据可用率: {overall.get('usable_rate', 0):.1%}")
        print(f"  平均问题数: {overall.get('avg_issues_count', 0):.1f}")
        
        # 最佳和最差数据源
        best_source = insights['best_source']
        worst_source = insights['worst_source']
        
        print(f"\n🥇 最佳数据源: {best_source['name']} (质量评分: {best_source['score']:.3f})")
        print(f"🥉 最差数据源: {worst_source['name']} (质量评分: {worst_source['score']:.3f})")
        
        # 改进建议
        suggestions = insights['improvement_suggestions']
        if suggestions:
            print(f"\n💡 改进建议:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"  {i}. {suggestion}")
        else:
            print(f"\n✅ 数据质量良好，无需特别改进")
    
    def demonstrate_quality_comparison(self):
        """演示质量对比"""
        print(f"\n⚖️ 质量感知 vs 传统筛选对比")
        print("=" * 60)
        
        print("🔍 质量感知筛选的优势:")
        print("  1. ✅ 自动过滤低质量数据")
        print("  2. ✅ 根据数据质量调整评分")
        print("  3. ✅ 提供数据源性能监控")
        print("  4. ✅ 识别和修复数据问题")
        print("  5. ✅ 提供质量改进建议")
        
        print("\n📈 传统筛选的问题:")
        print("  1. ❌ 无法识别数据质量问题")
        print("  2. ❌ 可能基于错误数据做决策")
        print("  3. ❌ 缺乏数据源质量监控")
        print("  4. ❌ 无法处理数据不一致性")
        print("  5. ❌ 筛选结果可能不准确")
        
        print("\n💰 质量感知筛选的价值:")
        print("  • 提高投资决策的准确性")
        print("  • 降低因数据错误导致的风险")
        print("  • 优化数据源配置和成本")
        print("  • 建立可信的数据基础设施")
        print("  • 支持合规和审计要求")


async def main():
    """主演示函数"""
    print("🔍 完整数据质量系统演示")
    print("=" * 80)
    
    demo = CompleteQualitySystemDemo()
    
    # 1. 质量感知筛选演示
    await demo.demonstrate_quality_aware_screening()
    
    # 2. 质量洞察演示
    demo.demonstrate_quality_insights()
    
    # 3. 质量对比演示
    demo.demonstrate_quality_comparison()
    
    print("\n" + "=" * 80)
    print("🎉 完整数据质量系统演示完成!")
    print("\n🎯 数据质量管理的核心价值:")
    print("  1. ✅ 数据标准化 - 统一不同数据源格式")
    print("  2. ✅ 自动清洗 - 处理缺失值和异常值")
    print("  3. ✅ 质量验证 - 确保数据准确性和完整性")
    print("  4. ✅ 质量评分 - 量化数据可信度")
    print("  5. ✅ 智能筛选 - 基于质量调整投资决策")
    print("  6. ✅ 源头监控 - 持续监控数据源性能")
    print("  7. ✅ 问题追踪 - 识别和解决数据质量问题")
    print("  8. ✅ 改进建议 - 提供数据质量优化方案")


if __name__ == "__main__":
    asyncio.run(main())
