#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据源抽象接口和策略模式实现
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
import asyncio
import time
import aiohttp
from qbot.common.result import Result, ErrorCode, ErrorSeverity, success, failure, from_exception
from qbot.common.enhanced_logger import get_logger


class DataSourceType(Enum):
    """数据源类型"""
    REAL_TIME = "real_time"
    HISTORICAL = "historical"
    FUNDAMENTAL = "fundamental"
    NEWS = "news"


class MarketType(Enum):
    """市场类型"""
    CN_A = "CN_A"
    US = "US"
    HK = "HK"
    UK = "UK"
    JP = "JP"


@dataclass
class StockData:
    """标准化股票数据结构"""
    symbol: str
    market: str
    name: str = ""
    current_price: float = 0.0
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_prev: float = 0.0
    volume: int = 0
    amount: float = 0.0
    market_cap: float = 0.0
    pe_ratio: float = 0.0
    pb_ratio: float = 0.0
    roe: float = 0.0
    timestamp: float = 0.0
    source: str = ""
    extra_data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.extra_data is None:
            self.extra_data = {}


class IDataSource(ABC):
    """数据源抽象接口"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.enabled = config.get('enabled', True)
        self.rate_limit = config.get('rate_limit', 60)
        self.timeout = config.get('timeout', 10)
        self.retry_count = config.get('retry_count', 3)
        self.supported_markets = config.get('markets', [])
        
        # 请求限制管理
        self.request_times = []
        self.last_request_time = 0
    
    @abstractmethod
    async def get_stock_data(self, symbol: str, market: str) -> Result[StockData]:
        """获取股票数据"""
        pass
    
    @abstractmethod
    def supports_market(self, market: str) -> bool:
        """检查是否支持指定市场"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查数据源是否可用"""
        pass
    
    def can_make_request(self) -> bool:
        """检查是否可以发起请求（速率限制）"""
        current_time = time.time()
        
        # 清理一分钟前的请求记录
        self.request_times = [t for t in self.request_times if current_time - t < 60]
        
        # 检查是否超过速率限制
        if len(self.request_times) >= self.rate_limit:
            return False
        
        return True
    
    def record_request(self):
        """记录请求时间"""
        self.request_times.append(time.time())
        self.last_request_time = time.time()
    
    async def _make_request_with_retry(self, request_func, *args, **kwargs) -> Result[Any]:
        """带重试的请求方法"""
        logger = get_logger(f"data_source.{self.name}")

        for attempt in range(self.retry_count):
            try:
                if not self.can_make_request():
                    await asyncio.sleep(1)  # 等待速率限制
                    continue

                self.record_request()
                result = await request_func(*args, **kwargs)
                return success(result)

            except asyncio.TimeoutError as e:
                logger.log_exception(Exception(f"请求超时: {e}"),
                    context={'attempt': attempt + 1, 'max_attempts': self.retry_count},
                    error_code=ErrorCode.TIMEOUT,
                    severity=ErrorSeverity.MEDIUM)

                if attempt < self.retry_count - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                else:
                    return from_exception(Exception(f"请求超时: {e}"), ErrorCode.TIMEOUT, ErrorSeverity.HIGH, self.name)

            except aiohttp.ClientError as e:
                logger.log_exception(Exception(f"网络错误: {e}"),
                    context={'attempt': attempt + 1, 'max_attempts': self.retry_count},
                    error_code=ErrorCode.NETWORK_ERROR,
                    severity=ErrorSeverity.MEDIUM)

                if attempt < self.retry_count - 1:
                    await asyncio.sleep(2 ** attempt)
                else:
                    return from_exception(Exception(f"网络错误: {e}"), ErrorCode.NETWORK_ERROR, ErrorSeverity.HIGH, self.name)

            except Exception as e:
                logger.log_exception(e,
                    context={'attempt': attempt + 1, 'max_attempts': self.retry_count},
                    error_code=ErrorCode.UNKNOWN_ERROR,
                    severity=ErrorSeverity.HIGH)

                if attempt < self.retry_count - 1:
                    await asyncio.sleep(2 ** attempt)
                else:
                    return from_exception(e, ErrorCode.UNKNOWN_ERROR, ErrorSeverity.HIGH, self.name)

        return failure(ErrorCode.DATA_SOURCE_UNAVAILABLE,
                      f"数据源 {self.name} 在 {self.retry_count} 次重试后仍然失败",
                      ErrorSeverity.HIGH, self.name)


class DataSourceRegistry:
    """数据源注册器"""
    
    def __init__(self):
        self._sources: Dict[str, type] = {}
        self._instances: Dict[str, IDataSource] = {}
    
    def register(self, name: str, source_class: type):
        """注册数据源类"""
        if not issubclass(source_class, IDataSource):
            raise ValueError(f"数据源类必须继承自 IDataSource: {source_class}")
        
        self._sources[name] = source_class
        logger.info(f"注册数据源: {name}")
    
    def create_instance(self, name: str, config: Dict[str, Any]) -> Optional[IDataSource]:
        """创建数据源实例"""
        if name not in self._sources:
            logger.error(f"未注册的数据源: {name}")
            return None
        
        try:
            source_class = self._sources[name]
            instance = source_class(name, config)
            self._instances[name] = instance
            return instance
        except Exception as e:
            logger.error(f"创建数据源实例失败 {name}: {e}")
            return None
    
    def get_instance(self, name: str) -> Optional[IDataSource]:
        """获取数据源实例"""
        return self._instances.get(name)
    
    def get_available_sources(self, market: str = None) -> List[IDataSource]:
        """获取可用的数据源"""
        available = []
        for instance in self._instances.values():
            if instance.is_available():
                if market is None or instance.supports_market(market):
                    available.append(instance)
        return available
    
    def get_registered_sources(self) -> List[str]:
        """获取已注册的数据源名称"""
        return list(self._sources.keys())


class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.registry = DataSourceRegistry()
        self._initialized = False
    
    def initialize(self):
        """初始化数据源管理器"""
        if self._initialized:
            return
        
        try:
            # 注册内置数据源
            self._register_builtin_sources()
            
            # 创建数据源实例
            self._create_instances()
            
            self._initialized = True
            logger.info("数据源管理器初始化完成")
            
        except Exception as e:
            logger.error(f"数据源管理器初始化失败: {e}")
    
    def _register_builtin_sources(self):
        """注册内置数据源"""
        try:
            # 注册新浪财经数据源
            from qbot.data.sources.sina_source import SinaDataSource
            self.registry.register('sina', SinaDataSource)

            # 注册Finnhub数据源
            from qbot.data.sources.finnhub_source import FinnhubDataSource
            self.registry.register('finnhub', FinnhubDataSource)

            # 注册其他数据源（如果可用）
            try:
                from qbot.data.sources.tencent_source import TencentDataSource
                self.registry.register('tencent', TencentDataSource)
            except ImportError:
                logger.debug("腾讯数据源未实现")

            try:
                from qbot.data.sources.yahoo_source import YahooDataSource
                self.registry.register('yahoo', YahooDataSource)
            except ImportError:
                logger.debug("Yahoo数据源未实现")

            logger.info("内置数据源注册完成")

        except Exception as e:
            logger.error(f"注册内置数据源失败: {e}")
    
    def _create_instances(self):
        """创建数据源实例"""
        for name, config in self.config_manager.configs.items():
            if config.enabled:
                config_dict = {
                    'enabled': config.enabled,
                    'api_key': config.api_key,
                    'api_secret': config.api_secret,
                    'base_url': config.base_url,
                    'rate_limit': config.rate_limit,
                    'timeout': config.timeout,
                    'retry_count': config.retry_count,
                    'markets': config.markets,
                    **config.extra_params
                }
                
                instance = self.registry.create_instance(name, config_dict)
                if instance:
                    logger.info(f"创建数据源实例: {name}")
    
    async def get_stock_data(self, symbol: str, market: str,
                           preferred_sources: List[str] = None) -> Result[StockData]:
        """获取股票数据（支持数据源优先级）"""
        logger = get_logger("data_source_manager")

        if not self._initialized:
            self.initialize()

        # 获取可用的数据源
        available_sources = self.registry.get_available_sources(market)

        if not available_sources:
            return failure(
                ErrorCode.MARKET_NOT_SUPPORTED,
                f"没有可用的数据源支持市场: {market}",
                ErrorSeverity.HIGH,
                "DataSourceManager"
            )

        # 按优先级排序
        if preferred_sources:
            # 使用指定的优先级
            sorted_sources = []
            for source_name in preferred_sources:
                for source in available_sources:
                    if source.name == source_name:
                        sorted_sources.append(source)
                        break
            # 添加剩余的数据源
            for source in available_sources:
                if source not in sorted_sources:
                    sorted_sources.append(source)
            available_sources = sorted_sources
        else:
            # 使用配置中的优先级
            available_sources.sort(key=lambda x: self.config_manager.get_config(x.name).priority)

        # 收集所有错误信息
        errors = []

        # 依次尝试数据源
        for source in available_sources:
            try:
                logger.debug(f"尝试从 {source.name} 获取 {symbol} 数据")
                result = await source.get_stock_data(symbol, market)

                if result.is_success():
                    logger.info(f"从 {source.name} 成功获取 {symbol} 数据")
                    result.add_metadata('source', source.name)
                    result.add_metadata('market', market)
                    return result
                else:
                    errors.append(f"{source.name}: {result.get_error_message()}")
                    logger.warning(f"从 {source.name} 获取 {symbol} 数据失败: {result.get_error_message()}")

            except Exception as e:
                error_msg = f"{source.name}: {str(e)}"
                errors.append(error_msg)
                logger.log_exception(e,
                    context={'symbol': symbol, 'market': market, 'source': source.name},
                    error_code=ErrorCode.DATA_SOURCE_UNAVAILABLE,
                    severity=ErrorSeverity.MEDIUM)

        # 所有数据源都失败
        return failure(
            ErrorCode.DATA_SOURCE_NO_DATA,
            f"所有数据源都无法获取 {symbol} 数据",
            ErrorSeverity.HIGH,
            "DataSourceManager",
            {'symbol': symbol, 'market': market, 'errors': errors}
        )
    
    def get_source_status(self) -> Dict[str, Dict[str, Any]]:
        """获取数据源状态"""
        status = {}
        for name, instance in self.registry._instances.items():
            config = self.config_manager.get_config(name)
            status[name] = {
                'enabled': config.enabled,
                'available': instance.is_available(),
                'supported_markets': instance.supported_markets,
                'last_request_time': instance.last_request_time,
                'request_count': len(instance.request_times),
                'rate_limit': instance.rate_limit
            }
        return status
    
    def register_custom_source(self, name: str, source_class: type, config: Dict[str, Any]):
        """注册自定义数据源"""
        self.registry.register(name, source_class)
        instance = self.registry.create_instance(name, config)
        if instance:
            logger.info(f"注册并创建自定义数据源: {name}")
            return instance
        return None


# 全局数据源注册器
global_registry = DataSourceRegistry()
