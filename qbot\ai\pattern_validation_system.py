#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模式验证系统
负责验证发现的交易模式，跟踪性能，决定是否加入模型库
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import json
import pickle
from collections import defaultdict, deque
import warnings
warnings.filterwarnings('ignore')

from .autonomous_learning_system import TradingPattern, PatternDiscoveryEngine

logger = logging.getLogger(__name__)

@dataclass
class PatternValidationResult:
    """模式验证结果"""
    pattern_id: str
    validation_period: int        # 验证期（天）
    total_signals: int           # 总信号数
    successful_signals: int      # 成功信号数
    failed_signals: int          # 失败信号数
    success_rate: float          # 成功率
    avg_return: float           # 平均收益率
    max_return: float           # 最大收益率
    min_return: float           # 最小收益率
    sharpe_ratio: float         # 夏普比率
    max_drawdown: float         # 最大回撤
    validation_status: str      # PASSED, FAILED, PENDING
    confidence_score: float     # 最终置信度
    recommendation: str         # ADD_TO_LIBRARY, REJECT, CONTINUE_TESTING
    metadata: Dict[str, Any]

@dataclass
class PatternSignal:
    """模式信号"""
    signal_id: str
    pattern_id: str
    symbol: str
    signal_time: datetime
    signal_price: float
    signal_type: str            # BUY, SELL
    target_price: float
    stop_loss: float
    actual_return: Optional[float] = None
    holding_period: Optional[int] = None
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    is_successful: Optional[bool] = None

class PatternValidationSystem:
    """模式验证系统"""
    
    def __init__(self, params: Dict[str, Any] = None):
        self.params = params or {
            'validation_period': 30,           # 验证期30天
            'min_signals_for_validation': 10,  # 最少信号数
            'success_rate_threshold': 0.6,     # 成功率阈值60%
            'min_avg_return': 0.02,            # 最小平均收益2%
            'max_drawdown_threshold': 0.15,    # 最大回撤阈值15%
            'min_sharpe_ratio': 0.5,           # 最小夏普比率
            'confidence_threshold': 0.75,      # 置信度阈值
            'signal_timeout': 5,               # 信号超时天数
            'max_holding_period': 10           # 最大持有期
        }
        
        self.testing_patterns = {}          # 测试中的模式
        self.pattern_signals = defaultdict(list)  # 模式信号记录
        self.validation_results = {}        # 验证结果
        self.model_library = {}            # 模型库
        self.performance_tracker = defaultdict(list)
        
    def start_pattern_testing(self, pattern: TradingPattern) -> bool:
        """开始模式测试"""
        try:
            if pattern.pattern_id in self.testing_patterns:
                logger.warning(f"模式 {pattern.pattern_id} 已在测试中")
                return False
                
            # 设置测试状态
            pattern.validation_status = 'TESTING'
            self.testing_patterns[pattern.pattern_id] = {
                'pattern': pattern,
                'start_time': datetime.now(),
                'end_time': datetime.now() + timedelta(days=self.params['validation_period']),
                'signals_generated': 0,
                'signals_completed': 0
            }
            
            logger.info(f"开始测试模式: {pattern.pattern_id} - {pattern.pattern_name}")
            return True
            
        except Exception as e:
            logger.error(f"开始模式测试失败: {e}")
            return False
            
    def check_pattern_signals(self, market_data: Dict[str, pd.DataFrame]) -> List[PatternSignal]:
        """检查模式信号"""
        new_signals = []
        
        try:
            for pattern_id, test_info in self.testing_patterns.items():
                pattern = test_info['pattern']
                
                # 检查是否超过测试期
                if datetime.now() > test_info['end_time']:
                    self._complete_pattern_testing(pattern_id)
                    continue
                    
                # 为每只股票检查信号
                for symbol, data in market_data.items():
                    if data.empty:
                        continue
                        
                    signal = self._check_pattern_match(pattern, symbol, data)
                    if signal:
                        new_signals.append(signal)
                        self.pattern_signals[pattern_id].append(signal)
                        test_info['signals_generated'] += 1
                        
                        logger.info(f"生成模式信号: {pattern_id} - {symbol} - {signal.signal_type}")
                        
        except Exception as e:
            logger.error(f"检查模式信号失败: {e}")
            
        return new_signals
        
    def _check_pattern_match(self, pattern: TradingPattern, symbol: str, data: pd.DataFrame) -> Optional[PatternSignal]:
        """检查模式匹配"""
        try:
            if len(data) < 20:
                return None
                
            latest = data.iloc[-1]
            
            # 检查模式条件
            conditions_met = 0
            total_conditions = len(pattern.conditions)
            
            if total_conditions == 0:
                return None
                
            # 提取当前特征
            current_features = self._extract_current_features(symbol, data)
            
            # 检查每个条件
            for condition in pattern.conditions:
                if self._evaluate_condition(condition, current_features, latest):
                    conditions_met += 1
                    
            # 计算匹配度
            match_ratio = conditions_met / total_conditions
            
            # 如果匹配度足够高，生成信号
            if match_ratio >= 0.8:  # 80%条件匹配
                signal_type = self._determine_signal_type(pattern, current_features)
                
                if signal_type:
                    signal_id = f"{pattern.pattern_id}_{symbol}_{int(datetime.now().timestamp())}"
                    
                    # 计算目标价和止损价
                    current_price = latest['close']
                    target_price, stop_loss = self._calculate_targets(pattern, current_price, signal_type)
                    
                    signal = PatternSignal(
                        signal_id=signal_id,
                        pattern_id=pattern.pattern_id,
                        symbol=symbol,
                        signal_time=datetime.now(),
                        signal_price=current_price,
                        signal_type=signal_type,
                        target_price=target_price,
                        stop_loss=stop_loss
                    )
                    
                    return signal
                    
        except Exception as e:
            logger.error(f"检查模式匹配失败: {e}")
            
        return None
        
    def _extract_current_features(self, symbol: str, data: pd.DataFrame) -> Dict[str, float]:
        """提取当前特征"""
        try:
            if len(data) < 20:
                return {}
                
            latest = data.iloc[-1]
            recent_20 = data.tail(20)
            
            features = {
                'change_pct': latest.get('change_pct', 0),
                'volume_ratio': latest.get('volume_ratio', 1),
                'turnover_rate': latest.get('turnover_rate', 0),
                'rsi': self._calculate_rsi(data['close'].values),
                'main_inflow_ratio': latest.get('main_inflow_ratio', 0),
                'price_momentum_5': (latest['close'] - recent_20.iloc[-6]['close']) / recent_20.iloc[-6]['close'] if len(recent_20) >= 6 else 0,
                'volatility_5': recent_20['close'].tail(5).pct_change().std()
            }
            
            return features
            
        except Exception as e:
            logger.error(f"提取当前特征失败: {e}")
            return {}
            
    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """计算RSI"""
        try:
            if len(prices) < period + 1:
                return 50.0

            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])

            # 处理零除错误和边界情况
            if np.isnan(avg_loss) or avg_loss == 0:
                # 如果没有损失，说明价格一直上涨
                return 100.0 if (not np.isnan(avg_gain) and avg_gain > 0) else 50.0

            if np.isnan(avg_gain) or avg_gain == 0:
                # 如果没有收益，说明价格一直下跌
                return 0.0

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            # 确保RSI在合理范围内
            rsi = max(0.0, min(100.0, rsi))

            return float(rsi) if not np.isnan(rsi) else 50.0

        except (ZeroDivisionError, ValueError) as e:
            self.logger.warning(f"RSI计算失败: {e}")
            return 50.0
        except Exception as e:
            self.logger.error(f"RSI计算异常: {e}")
            return 50.0
            
    def _evaluate_condition(self, condition: str, features: Dict[str, float], latest: pd.Series) -> bool:
        """评估条件"""
        try:
            # 简化的条件评估
            if "涨幅" in condition and ">=" in condition:
                threshold = float(condition.split(">=")[1].replace("%", "").strip())
                return features.get('change_pct', 0) >= threshold
                
            elif "量比" in condition and ">=" in condition:
                threshold = float(condition.split(">=")[1].strip())
                return features.get('volume_ratio', 1) >= threshold
                
            elif "RSI" in condition:
                if ">=" in condition:
                    threshold = float(condition.split(">=")[1].strip())
                    return features.get('rsi', 50) >= threshold
                elif "<=" in condition:
                    threshold = float(condition.split("<=")[1].strip())
                    return features.get('rsi', 50) <= threshold
                    
            elif "主力流入比例" in condition and ">=" in condition:
                threshold = float(condition.split(">=")[1].strip())
                return features.get('main_inflow_ratio', 0) >= threshold
                
            return False
            
        except:
            return False
            
    def _determine_signal_type(self, pattern: TradingPattern, features: Dict[str, float]) -> Optional[str]:
        """确定信号类型"""
        try:
            if pattern.pattern_type in ['LIMIT_UP', 'BREAKOUT']:
                return 'BUY'
            elif pattern.pattern_type in ['LIMIT_DOWN', 'REVERSAL']:
                return 'SELL'
            elif 'ACCUMULATION' in pattern.pattern_type:
                return 'BUY'
            elif 'DISTRIBUTION' in pattern.pattern_type:
                return 'SELL'
            else:
                # 基于特征判断
                if features.get('change_pct', 0) > 0 and features.get('main_inflow_ratio', 0) > 0:
                    return 'BUY'
                elif features.get('change_pct', 0) < 0 and features.get('main_inflow_ratio', 0) < 0:
                    return 'SELL'
                    
            return None
            
        except:
            return None
            
    def _calculate_targets(self, pattern: TradingPattern, current_price: float, signal_type: str) -> Tuple[float, float]:
        """计算目标价和止损价"""
        try:
            if signal_type == 'BUY':
                # 买入信号的目标价和止损价
                if pattern.risk_level == 'HIGH':
                    target_ratio = 0.08  # 8%目标
                    stop_ratio = 0.05    # 5%止损
                elif pattern.risk_level == 'MEDIUM':
                    target_ratio = 0.05  # 5%目标
                    stop_ratio = 0.03    # 3%止损
                else:
                    target_ratio = 0.03  # 3%目标
                    stop_ratio = 0.02    # 2%止损
                    
                target_price = current_price * (1 + target_ratio)
                stop_loss = current_price * (1 - stop_ratio)
                
            else:  # SELL
                if pattern.risk_level == 'HIGH':
                    target_ratio = 0.08
                    stop_ratio = 0.05
                elif pattern.risk_level == 'MEDIUM':
                    target_ratio = 0.05
                    stop_ratio = 0.03
                else:
                    target_ratio = 0.03
                    stop_ratio = 0.02
                    
                target_price = current_price * (1 - target_ratio)
                stop_loss = current_price * (1 + stop_ratio)
                
            return target_price, stop_loss
            
        except:
            return current_price, current_price
            
    def update_signal_results(self, market_data: Dict[str, pd.DataFrame]) -> None:
        """更新信号结果"""
        try:
            for pattern_id, signals in self.pattern_signals.items():
                for signal in signals:
                    if signal.is_successful is not None:  # 已经完成
                        continue
                        
                    if signal.symbol not in market_data:
                        continue
                        
                    data = market_data[signal.symbol]
                    if data.empty:
                        continue
                        
                    current_price = data.iloc[-1]['close']
                    current_time = datetime.now()
                    
                    # 检查是否达到目标或止损
                    if signal.signal_type == 'BUY':
                        if current_price >= signal.target_price:
                            # 达到目标价
                            signal.exit_price = signal.target_price
                            signal.exit_time = current_time
                            signal.actual_return = (signal.target_price - signal.signal_price) / signal.signal_price
                            signal.is_successful = True
                            signal.holding_period = (current_time - signal.signal_time).days
                            
                        elif current_price <= signal.stop_loss:
                            # 触发止损
                            signal.exit_price = signal.stop_loss
                            signal.exit_time = current_time
                            signal.actual_return = (signal.stop_loss - signal.signal_price) / signal.signal_price
                            signal.is_successful = False
                            signal.holding_period = (current_time - signal.signal_time).days
                            
                    else:  # SELL
                        if current_price <= signal.target_price:
                            # 达到目标价
                            signal.exit_price = signal.target_price
                            signal.exit_time = current_time
                            signal.actual_return = (signal.signal_price - signal.target_price) / signal.signal_price
                            signal.is_successful = True
                            signal.holding_period = (current_time - signal.signal_time).days
                            
                        elif current_price >= signal.stop_loss:
                            # 触发止损
                            signal.exit_price = signal.stop_loss
                            signal.exit_time = current_time
                            signal.actual_return = (signal.signal_price - signal.stop_loss) / signal.signal_price
                            signal.is_successful = False
                            signal.holding_period = (current_time - signal.signal_time).days
                            
                    # 检查超时
                    if signal.is_successful is None:
                        days_held = (current_time - signal.signal_time).days
                        if days_held >= self.params['max_holding_period']:
                            # 超时平仓
                            signal.exit_price = current_price
                            signal.exit_time = current_time
                            if signal.signal_type == 'BUY':
                                signal.actual_return = (current_price - signal.signal_price) / signal.signal_price
                            else:
                                signal.actual_return = (signal.signal_price - current_price) / signal.signal_price
                            signal.is_successful = signal.actual_return > 0
                            signal.holding_period = days_held
                            
                    # 更新测试信息
                    if signal.is_successful is not None and pattern_id in self.testing_patterns:
                        self.testing_patterns[pattern_id]['signals_completed'] += 1
                        
        except Exception as e:
            logger.error(f"更新信号结果失败: {e}")
            
    def _complete_pattern_testing(self, pattern_id: str) -> None:
        """完成模式测试"""
        try:
            if pattern_id not in self.testing_patterns:
                return
                
            test_info = self.testing_patterns[pattern_id]
            pattern = test_info['pattern']
            signals = self.pattern_signals[pattern_id]
            
            # 计算验证结果
            validation_result = self._calculate_validation_result(pattern_id, signals)
            
            # 保存验证结果
            self.validation_results[pattern_id] = validation_result
            
            # 决定是否加入模型库
            if validation_result.recommendation == 'ADD_TO_LIBRARY':
                self._add_to_model_library(pattern, validation_result)
                logger.info(f"✅ 模式 {pattern_id} 验证通过，已加入模型库")
            else:
                logger.info(f"❌ 模式 {pattern_id} 验证失败，已拒绝")
                
            # 清理测试数据
            del self.testing_patterns[pattern_id]
            
        except Exception as e:
            logger.error(f"完成模式测试失败: {e}")
            
    def _calculate_validation_result(self, pattern_id: str, signals: List[PatternSignal]) -> PatternValidationResult:
        """计算验证结果"""
        try:
            completed_signals = [s for s in signals if s.is_successful is not None]
            
            if len(completed_signals) < self.params['min_signals_for_validation']:
                return PatternValidationResult(
                    pattern_id=pattern_id,
                    validation_period=self.params['validation_period'],
                    total_signals=len(signals),
                    successful_signals=0,
                    failed_signals=0,
                    success_rate=0.0,
                    avg_return=0.0,
                    max_return=0.0,
                    min_return=0.0,
                    sharpe_ratio=0.0,
                    max_drawdown=0.0,
                    validation_status='FAILED',
                    confidence_score=0.0,
                    recommendation='REJECT',
                    metadata={'reason': 'insufficient_signals'}
                )
                
            successful_signals = [s for s in completed_signals if s.is_successful]
            failed_signals = [s for s in completed_signals if not s.is_successful]
            
            success_rate = len(successful_signals) / len(completed_signals)
            returns = [s.actual_return for s in completed_signals if s.actual_return is not None]
            
            if not returns:
                avg_return = 0.0
                max_return = 0.0
                min_return = 0.0
                sharpe_ratio = 0.0
                max_drawdown = 0.0
            else:
                avg_return = np.mean(returns)
                max_return = np.max(returns)
                min_return = np.min(returns)
                
                # 计算夏普比率
                if np.std(returns) > 0:
                    sharpe_ratio = avg_return / np.std(returns)
                else:
                    sharpe_ratio = 0.0
                    
                # 计算最大回撤
                cumulative_returns = np.cumprod(1 + np.array(returns))
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = (cumulative_returns - running_max) / running_max
                max_drawdown = np.min(drawdowns)
                
            # 计算最终置信度
            confidence_score = self._calculate_final_confidence(
                success_rate, avg_return, sharpe_ratio, max_drawdown, len(completed_signals)
            )
            
            # 决定验证状态和建议
            if (success_rate >= self.params['success_rate_threshold'] and
                avg_return >= self.params['min_avg_return'] and
                abs(max_drawdown) <= self.params['max_drawdown_threshold'] and
                confidence_score >= self.params['confidence_threshold']):
                validation_status = 'PASSED'
                recommendation = 'ADD_TO_LIBRARY'
            else:
                validation_status = 'FAILED'
                recommendation = 'REJECT'
                
            return PatternValidationResult(
                pattern_id=pattern_id,
                validation_period=self.params['validation_period'],
                total_signals=len(signals),
                successful_signals=len(successful_signals),
                failed_signals=len(failed_signals),
                success_rate=success_rate,
                avg_return=avg_return,
                max_return=max_return,
                min_return=min_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                validation_status=validation_status,
                confidence_score=confidence_score,
                recommendation=recommendation,
                metadata={
                    'total_completed_signals': len(completed_signals),
                    'avg_holding_period': np.mean([s.holding_period for s in completed_signals if s.holding_period is not None])
                }
            )
            
        except Exception as e:
            logger.error(f"计算验证结果失败: {e}")
            return PatternValidationResult(
                pattern_id=pattern_id,
                validation_period=0,
                total_signals=0,
                successful_signals=0,
                failed_signals=0,
                success_rate=0.0,
                avg_return=0.0,
                max_return=0.0,
                min_return=0.0,
                sharpe_ratio=0.0,
                max_drawdown=0.0,
                validation_status='FAILED',
                confidence_score=0.0,
                recommendation='REJECT',
                metadata={'error': str(e)}
            )
