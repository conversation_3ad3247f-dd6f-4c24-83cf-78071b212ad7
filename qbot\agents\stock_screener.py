#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全球股票筛选引擎
支持多市场股票筛选和融资融券分析
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import asyncio
import logging
from datetime import datetime, timedelta

try:
    from qbot.data.stock_names_database import stock_names_db, get_stock_display_name
    STOCK_NAMES_AVAILABLE = True
except ImportError:
    STOCK_NAMES_AVAILABLE = False
    def get_stock_display_name(symbol, market, language='auto'):
        return symbol

try:
    from qbot.data.tdx_data_reader import tdx_reader, should_use_tdx_local_data
    from qbot.data.online_data_provider import online_provider, get_realtime_stock_data, is_market_open_now
    REAL_DATA_AVAILABLE = True
except ImportError:
    REAL_DATA_AVAILABLE = False
    def should_use_tdx_local_data():
        return False
    def get_realtime_stock_data(symbol, market):
        return None
    def is_market_open_now(market):
        return False

class GlobalStockScreener:
    """全球股票筛选器"""
    
    def __init__(self):
        self.logger = logging.getLogger("GlobalStockScreener")
        
        # 市场数据源配置
        self.data_sources = {
            'CN_A': {
                'source': 'tushare',
                'api_key': None,
                'base_url': 'http://api.tushare.pro',
                'rate_limit': 200  # 每分钟请求数
            },
            'HK': {
                'source': 'yahoo',
                'api_key': None,
                'base_url': 'https://query1.finance.yahoo.com',
                'rate_limit': 2000
            },
            'US': {
                'source': 'alpha_vantage',
                'api_key': None,
                'base_url': 'https://www.alphavantage.co',
                'rate_limit': 500
            },
            'UK': {
                'source': 'yahoo',
                'api_key': None,
                'base_url': 'https://query1.finance.yahoo.com',
                'rate_limit': 2000
            },
            'JP': {
                'source': 'yahoo',
                'api_key': None,
                'base_url': 'https://query1.finance.yahoo.com',
                'rate_limit': 2000
            }
        }
        
        # 融资融券评估标准（更宽松的标准）
        self.margin_criteria = {
            'CN_A': {
                'min_market_cap': 1000000000,  # 10亿人民币（降低门槛）
                'min_daily_volume': 5000000,   # 500万元（降低门槛）
                'min_price': 2.0,              # 2元（降低门槛）
                'max_volatility': 0.6,         # 60%（提高容忍度）
                'min_liquidity_ratio': 0.02    # 2%（降低门槛）
            },
            'HK': {
                'min_market_cap': 500000000,   # 5亿港币（降低门槛）
                'min_daily_volume': 2000000,   # 200万港币（降低门槛）
                'min_price': 0.5,              # 0.5港币（降低门槛）
                'max_volatility': 0.7,         # 70%（提高容忍度）
                'min_liquidity_ratio': 0.02    # 2%（降低门槛）
            },
            'US': {
                'min_market_cap': 100000000,   # 1亿美元（降低门槛）
                'min_daily_volume': 500000,    # 50万美元（降低门槛）
                'min_price': 2.0,              # 2美元（降低门槛）
                'max_volatility': 0.8,         # 80%（提高容忍度）
                'min_liquidity_ratio': 0.01    # 1%（降低门槛）
            },
            'UK': {
                'min_market_cap': 100000000,   # 1亿英镑（降低门槛）
                'min_daily_volume': 200000,    # 20万英镑（降低门槛）
                'min_price': 0.5,              # 0.5英镑（降低门槛）
                'max_volatility': 0.7,         # 70%（提高容忍度）
                'min_liquidity_ratio': 0.01    # 1%（降低门槛）
            },
            'JP': {
                'min_market_cap': 10000000000, # 100亿日元（降低门槛）
                'min_daily_volume': 50000000,  # 5000万日元（降低门槛）
                'min_price': 50,               # 50日元（降低门槛）
                'max_volatility': 0.6,         # 60%（提高容忍度）
                'min_liquidity_ratio': 0.02    # 2%（降低门槛）
            }
        }
    
    async def screen_stocks(self, market: str, criteria: Dict) -> List[Dict]:
        """筛选股票 - 启动后台排队筛选"""
        try:
            from qbot.agents.background_screener import background_screener

            self.logger.info(f"启动{market}市场后台筛选")

            # 启动后台筛选任务
            task_id = background_screener.start_screening(market, criteria)

            # 等待一小段时间，让后台任务开始
            await asyncio.sleep(0.5)

            # 轮询等待结果（最多等待5分钟）
            max_wait_time = 300  # 5分钟
            wait_interval = 2    # 每2秒检查一次
            waited_time = 0

            while waited_time < max_wait_time:
                progress = background_screener.get_progress(task_id)

                if not progress:
                    break

                if progress.status.value == "completed":
                    results = background_screener.get_results(task_id)
                    if results:
                        self.logger.info(f"后台筛选完成，找到{len(results)}只符合条件的股票")
                        return results
                    else:
                        self.logger.warning(f"后台筛选完成，但无符合条件的股票")
                        return []

                elif progress.status.value in ["failed", "cancelled"]:
                    self.logger.error(f"后台筛选失败或被取消: {progress.status.value}")
                    break

                # 显示进度信息
                if progress.status.value == "running":
                    self.logger.info(f"筛选进度: {progress.progress_percentage:.1f}% "
                                   f"({progress.processed_stocks}/{progress.total_stocks}) "
                                   f"当前: {progress.current_symbol}")

                await asyncio.sleep(wait_interval)
                waited_time += wait_interval

            # 超时或失败
            self.logger.warning(f"筛选超时或失败，返回空结果")
            return []

        except Exception as e:
            self.logger.error(f"启动后台筛选失败: {e}")
            return []

    def start_background_screening(self, market: str, criteria: Dict,
                                 progress_callback=None) -> str:
        """启动后台筛选（不等待结果）"""
        try:
            from qbot.agents.background_screener import background_screener

            task_id = background_screener.start_screening(market, criteria, progress_callback)
            self.logger.info(f"启动后台筛选任务: {task_id}")
            return task_id

        except Exception as e:
            self.logger.error(f"启动后台筛选失败: {e}")
            return ""

    def get_screening_progress(self, task_id: str):
        """获取筛选进度"""
        try:
            from qbot.agents.background_screener import background_screener
            return background_screener.get_progress(task_id)
        except ImportError as e:
            self.logger.error(f"无法导入background_screener模块: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取筛选进度失败: {e}")
            return None

    def get_screening_results(self, task_id: str):
        """获取筛选结果"""
        try:
            from qbot.agents.background_screener import background_screener
            return background_screener.get_results(task_id)
        except ImportError as e:
            self.logger.error(f"无法导入background_screener模块: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取筛选结果失败: {e}")
            return None
    
    async def get_stock_list(self, market: str) -> List[str]:
        """获取股票列表"""
        try:
            if market == 'CN_A':
                return await self.get_china_stock_list()
            elif market == 'HK':
                return await self.get_hk_stock_list()
            elif market == 'US':
                return await self.get_us_stock_list()
            elif market == 'UK':
                return await self.get_uk_stock_list()
            elif market == 'JP':
                return await self.get_jp_stock_list()
            else:
                return []
        except Exception as e:
            self.logger.error(f"获取{market}股票列表失败: {e}")
            return []
    
    async def get_china_stock_list(self) -> List[str]:
        """获取中国A股列表"""
        if REAL_DATA_AVAILABLE and tdx_reader.is_available():
            # 使用通达信数据
            stock_list = tdx_reader.get_stock_list()
            if stock_list:
                symbols = [stock['symbol'] for stock in stock_list[:100]]  # 限制数量
                self.logger.info(f"从通达信获取到{len(symbols)}只A股")
                return symbols

        # 备用：使用股票名称数据库
        if STOCK_NAMES_AVAILABLE:
            symbols = list(stock_names_db.all_stocks['CN_A'].keys())
            self.logger.info(f"从名称数据库获取到{len(symbols)}只A股")
            return symbols
        else:
            # 最后备用数据
            return [
                '000001.SZ', '000002.SZ', '000858.SZ', '000876.SZ',
                '600000.SH', '600036.SH', '600519.SH', '600887.SH',
                '002415.SZ', '002594.SZ', '300059.SZ', '300750.SZ'
            ]
    
    async def get_hk_stock_list(self) -> List[str]:
        """获取港股列表"""
        if STOCK_NAMES_AVAILABLE:
            return list(stock_names_db.all_stocks['HK'].keys())
        else:
            # 扩展港股列表，涵盖恒生指数成分股和主要公司
            return [
                # 科技股
                '0700.HK', '9988.HK', '1024.HK', '9618.HK', '3690.HK', '1810.HK', '9999.HK',
                '0981.HK', '2382.HK', '1833.HK', '6060.HK', '2013.HK', '0268.HK',

                # 金融股
                '0005.HK', '0011.HK', '0388.HK', '1398.HK', '3988.HK', '2388.HK', '0001.HK',
                '0002.HK', '0003.HK', '0004.HK', '0023.HK', '0066.HK', '2318.HK', '2628.HK',

                # 地产股
                '1109.HK', '0016.HK', '0012.HK', '0083.HK', '0101.HK', '1113.HK',
                '0017.HK', '0688.HK', '1997.HK', '0813.HK', '1928.HK',

                # 电信股
                '0941.HK', '0762.HK', '0728.HK', '0315.HK',

                # 消费股
                '1299.HK', '2020.HK', '0291.HK', '6862.HK', '9633.HK', '1876.HK',
                '0027.HK', '0151.HK', '0322.HK', '0992.HK', '1044.HK',

                # 能源股
                '0883.HK', '0386.HK', '2628.HK', '0857.HK', '1088.HK', '0135.HK',

                # 工业股
                '0175.HK', '0144.HK', '0489.HK', '0669.HK', '1038.HK', '1972.HK',

                # 医疗股
                '1093.HK', '6185.HK', '2269.HK', '1177.HK', '1801.HK'
            ]

    async def get_us_stock_list(self) -> List[str]:
        """获取美股列表"""
        if STOCK_NAMES_AVAILABLE:
            return list(stock_names_db.all_stocks['US'].keys())
        else:
            # 扩展美股列表，涵盖主要行业
            return [
                # 科技股 - FAANG + 其他大型科技公司
                'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
                'CRM', 'ADBE', 'ORCL', 'IBM', 'INTC', 'AMD', 'QCOM', 'AVGO',
                'TXN', 'MU', 'AMAT', 'LRCX', 'KLAC', 'MRVL', 'MCHP', 'ADI',

                # 金融股
                'JPM', 'BAC', 'WFC', 'C', 'GS', 'MS', 'AXP', 'V', 'MA', 'PYPL',
                'BLK', 'SCHW', 'USB', 'PNC', 'TFC', 'COF', 'DFS', 'SYF',

                # 医疗保健
                'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'ABT', 'DHR', 'BMY', 'AMGN',
                'GILD', 'BIIB', 'REGN', 'VRTX', 'ISRG', 'DXCM', 'ZTS', 'ILMN',

                # 消费品
                'KO', 'PEP', 'WMT', 'HD', 'MCD', 'SBUX', 'NKE', 'DIS', 'COST', 'TGT',
                'LOW', 'TJX', 'ROST', 'ULTA', 'LULU', 'DECK', 'TPG', 'MAR',

                # 工业股
                'BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'RTX', 'LMT', 'NOC', 'GD',
                'FDX', 'UNP', 'CSX', 'NSC', 'DAL', 'UAL', 'AAL', 'LUV',

                # 能源股
                'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'PSX', 'VLO', 'MPC', 'OXY', 'DVN',
                'PXD', 'FANG', 'MRO', 'APA', 'HAL', 'BKR', 'OIH', 'XLE'
            ]

    async def get_uk_stock_list(self) -> List[str]:
        """获取英股列表"""
        if STOCK_NAMES_AVAILABLE:
            return list(stock_names_db.all_stocks['UK'].keys())
        else:
            # 扩展英股列表，涵盖FTSE 100主要成分股
            return [
                # 能源股
                'SHEL.L', 'BP.L', 'GLEN.L', 'RIO.L', 'AAL.L', 'ANTO.L', 'BHP.L',

                # 金融股
                'HSBA.L', 'LLOY.L', 'BARC.L', 'NWG.L', 'STAN.L', 'LSEG.L', 'III.L',
                'PRUD.L', 'AVVA.L', 'RSA.L', 'BNZL.L',

                # 消费股
                'ULVR.L', 'DGE.L', 'TSCO.L', 'SBRY.L', 'MKS.L', 'MRRW.L', 'OCDO.L',
                'JD.L', 'FRES.L', 'WTB.L', 'CCH.L', 'SMDS.L',

                # 医疗股
                'AZN.L', 'GSK.L', 'HLMA.L', 'SN.L', 'INDV.L',

                # 电信股
                'VOD.L', 'BT-A.L', 'TALK.L',

                # 工业股
                'RR.L', 'BA.L', 'WEIR.L', 'SMDS.L', 'SMIN.L', 'CRH.L',

                # 其他
                'REL.L', 'RKT.L', 'IAG.L', 'EZJ.L', 'EXPN.L', 'AUTO.L'
            ]

    async def get_jp_stock_list(self) -> List[str]:
        """获取日股列表"""
        if STOCK_NAMES_AVAILABLE:
            return list(stock_names_db.all_stocks['JP'].keys())
        else:
            # 扩展日股列表，涵盖日经225主要成分股
            return [
                # 汽车股
                '7203.T', '7267.T', '7201.T', '7269.T', '7211.T', '7261.T',

                # 科技股
                '6758.T', '6861.T', '6954.T', '6981.T', '6752.T', '6503.T',
                '6762.T', '6971.T', '6976.T', '6770.T', '6723.T',

                # 金融股
                '8306.T', '8316.T', '8411.T', '8604.T', '8601.T', '8628.T',
                '8766.T', '8750.T', '8725.T',

                # 电信股
                '9432.T', '9433.T', '9434.T', '9613.T',

                # 消费股
                '4063.T', '4502.T', '4568.T', '2914.T', '2802.T', '2801.T',
                '7974.T', '9983.T', '3382.T', '8267.T',

                # 工业股
                '6301.T', '6326.T', '6367.T', '6471.T', '6473.T', '7011.T',
                '7012.T', '5401.T', '5411.T', '5713.T',

                # 其他
                '9984.T', '8035.T', '6098.T', '4755.T', '1605.T', '1801.T',
                '4901.T', '4911.T', '5020.T', '5108.T'
            ]
    
    async def get_stock_data(self, market: str, stock_list: List[str]) -> pd.DataFrame:
        """批量获取股票数据"""
        data = []
        failed_count = 0
        delisted_count = 0

        self.logger.info(f"开始批量查询{market}市场{len(stock_list)}只股票数据")

        # 使用并发控制，避免过多同时请求
        semaphore = asyncio.Semaphore(10)  # 最多同时10个请求

        async def fetch_single_stock(symbol):
            async with semaphore:
                try:
                    # 获取股票数据（不再检查REAL_DATA_AVAILABLE，让具体方法自己处理）
                    if market == 'CN_A':
                        stock_data = await self.get_real_china_stock_data(symbol)
                    else:
                        stock_data = await self.get_real_foreign_stock_data(symbol, market)

                    if stock_data:
                        # 验证数据有效性，过滤退市股票
                        if self.is_valid_stock_data(stock_data):
                            return stock_data, 'success'
                        else:
                            return None, 'delisted'
                    else:
                        return None, 'failed'

                except Exception as e:
                    self.logger.debug(f"获取{symbol}数据异常: {e}")
                    return None, 'error'

        # 并发获取所有股票数据
        tasks = [fetch_single_stock(symbol) for symbol in stock_list]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for i, result in enumerate(results):
            symbol = stock_list[i]

            if isinstance(result, Exception):
                failed_count += 1
                self.logger.debug(f"获取{symbol}数据异常: {result}")
                continue

            stock_data, status = result

            if status == 'success':
                data.append(stock_data)
            elif status == 'delisted':
                delisted_count += 1
                self.logger.debug(f"{symbol}疑似退市，已过滤")
            elif status == 'failed':
                failed_count += 1
                self.logger.debug(f"无法获取{symbol}的真实数据")
            elif status == 'error':
                failed_count += 1
                self.logger.debug(f"获取{symbol}数据时发生错误")
            else:
                # 处理未知状态
                failed_count += 1
                self.logger.debug(f"获取{symbol}数据返回未知状态: {status}")

        if data:
            self.logger.info(f"批量查询完成: 成功{len(data)}只, 失败{failed_count}只, 退市{delisted_count}只")
        else:
            self.logger.error(f"未能获取到任何{market}市场的真实数据")

        return pd.DataFrame(data)

    def is_valid_stock_data(self, stock_data: dict) -> bool:
        """验证股票数据有效性，过滤退市股票"""
        try:
            # 检查基本价格数据
            current_price = stock_data.get('current_price', 0)
            volume = stock_data.get('volume', 0)

            # 价格必须大于0
            if current_price <= 0:
                return False

            # 对于外国股票，检查价格是否过低（可能退市）
            market = stock_data.get('market', '')
            if market == 'US' and current_price < 1.0:  # 美股低于1美元可能退市
                return False
            elif market == 'HK' and current_price < 0.1:  # 港股低于0.1港币可能退市
                return False
            elif market == 'UK' and current_price < 10:  # 英股低于10便士可能退市
                return False
            elif market == 'JP' and current_price < 100:  # 日股低于100日元可能退市
                return False
            elif market == 'CN_A' and current_price < 1.0:  # A股低于1元可能ST
                return False

            # 检查成交量（0成交量可能停牌或退市）
            if volume <= 0:
                return False

            # 检查数据来源
            data_source = stock_data.get('data_source', '')
            if data_source == 'yahoo_real':
                # 真实Yahoo数据，进一步验证
                name = stock_data.get('name', '')
                if not name or name == stock_data.get('symbol', ''):
                    # 如果没有公司名称，可能是无效股票
                    return False

            return True

        except Exception as e:
            self.logger.debug(f"验证股票数据时出错: {e}")
            return False

    async def get_real_china_stock_data(self, symbol: str) -> Optional[Dict]:
        """获取中国股票真实数据 - 只返回真实数据，无真实数据时返回None"""
        try:
            from qbot.data.real_data_sources import real_data_manager

            # 获取真实数据
            real_data = await real_data_manager.get_china_stock_data(symbol)

            if real_data and real_data.get('current', 0) > 0:
                # 只使用真实数据，不进行任何估算或生成
                return {
                    'symbol': symbol,
                    'market': 'CN_A',
                    'name': real_data.get('name', self.get_stock_name(symbol, 'CN_A')),
                    'display_name': get_stock_display_name(symbol, 'CN_A', 'auto'),
                    'current_price': float(real_data['current']),
                    'open': float(real_data.get('open', 0)),
                    'high': float(real_data.get('high', 0)),
                    'low': float(real_data.get('low', 0)),
                    'volume': int(real_data.get('volume', 0)),
                    'amount': float(real_data.get('amount', 0)),
                    'close_prev': float(real_data.get('close_prev', 0)),
                    'price_change': round((real_data['current'] / real_data.get('close_prev', real_data['current']) - 1), 4) if real_data.get('close_prev', 0) > 0 else 0,
                    'timestamp': real_data.get('timestamp', datetime.now().timestamp()),
                    'data_source': real_data.get('source', 'unknown_real')
                }
            else:
                self.logger.warning(f"无法获取{symbol}的真实数据")
                return None

        except Exception as e:
            self.logger.error(f"获取{symbol}真实数据失败: {e}")
            return None



    async def get_real_foreign_stock_data(self, symbol: str, market: str) -> Optional[Dict]:
        """获取外国股票真实数据 - 只返回真实数据，无真实数据时返回None"""
        try:
            from qbot.data.real_data_sources import real_data_manager

            # 获取真实数据
            real_data = await real_data_manager.get_foreign_stock_data(symbol, market)

            if real_data and real_data.get('current', 0) > 0:
                # 只使用真实数据，不进行任何估算或生成
                return {
                    'symbol': symbol,
                    'market': market,
                    'name': real_data.get('name', self.get_stock_name(symbol, market)),
                    'display_name': get_stock_display_name(symbol, market, 'auto'),
                    'current_price': float(real_data['current']),
                    'open': float(real_data.get('open', 0)),
                    'high': float(real_data.get('high', 0)),
                    'low': float(real_data.get('low', 0)),
                    'volume': int(real_data.get('volume', 0)),
                    'close_prev': float(real_data.get('close_prev', 0)),
                    'price_change': round((real_data['current'] / real_data.get('close_prev', real_data['current']) - 1), 4) if real_data.get('close_prev', 0) > 0 else 0,
                    'timestamp': real_data.get('timestamp', datetime.now().timestamp()),
                    'data_source': real_data.get('source', 'unknown_real')
                }
            else:
                self.logger.warning(f"无法获取{market}市场{symbol}的真实数据")
                return None

        except Exception as e:
            self.logger.error(f"获取{market}市场{symbol}真实数据失败: {e}")
            return None

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """计算RSI指标"""
        try:
            if len(prices) < period + 1:
                return 50.0

            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

            # 获取最新的gain和loss值
            latest_gain = gain.iloc[-1]
            latest_loss = loss.iloc[-1]

            # 处理零除错误
            if pd.isna(latest_loss) or latest_loss == 0:
                # 如果没有损失，说明价格一直上涨，RSI应该接近100
                return 100.0 if latest_gain > 0 else 50.0

            if pd.isna(latest_gain) or latest_gain == 0:
                # 如果没有收益，说明价格一直下跌，RSI应该接近0
                return 0.0

            rs = latest_gain / latest_loss
            rsi = 100 - (100 / (1 + rs))

            # 确保RSI在合理范围内
            rsi = max(0.0, min(100.0, rsi))

            return float(rsi) if not pd.isna(rsi) else 50.0

        except (ZeroDivisionError, ValueError) as e:
            self.logger.warning(f"RSI计算失败: {e}")
            return 50.0
        except Exception as e:
            self.logger.error(f"RSI计算异常: {e}")
            return 50.0

    def get_stock_name(self, symbol: str, market: str) -> str:
        """获取股票名称"""
        if STOCK_NAMES_AVAILABLE:
            return stock_names_db.get_stock_name(symbol, market, 'auto')
        else:
            return f"股票{symbol}"
    

    
    def apply_screening_criteria(self, data: pd.DataFrame, criteria: Dict) -> pd.DataFrame:
        """应用筛选条件"""
        try:
            filtered_data = data.copy()

            # 基本面筛选
            fundamental = criteria.get('fundamental', {})
            for key, value in fundamental.items():
                if key in filtered_data.columns:
                    min_val = value.get('min', -np.inf)
                    max_val = value.get('max', np.inf)
                    filtered_data = filtered_data[
                        (filtered_data[key] >= min_val) &
                        (filtered_data[key] <= max_val)
                    ]
                else:
                    self.logger.warning(f"筛选条件中的列'{key}'不存在，跳过此条件")

            # 技术面筛选
            technical = criteria.get('technical', {})

            # RSI筛选
            if 'rsi' in filtered_data.columns:
                rsi_min = technical.get('rsi_min', 0)
                rsi_max = technical.get('rsi_max', 100)
                filtered_data = filtered_data[
                    (filtered_data['rsi'] >= rsi_min) &
                    (filtered_data['rsi'] <= rsi_max)
                ]
            else:
                self.logger.warning("RSI列不存在，跳过RSI筛选")

            # 均线趋势筛选
            ma_trend = technical.get('ma_trend', '不限')
            if ma_trend != '不限':
                if 'ma_trend' in filtered_data.columns:
                    filtered_data = filtered_data[filtered_data['ma_trend'] == ma_trend]
                else:
                    self.logger.warning("ma_trend列不存在，跳过均线趋势筛选")

            # 融资融券筛选
            leverage = criteria.get('leverage', {})
            if leverage.get('short_interest_max'):
                if 'short_interest' in filtered_data.columns:
                    filtered_data = filtered_data[
                        filtered_data['short_interest'] <= leverage['short_interest_max']
                    ]
                else:
                    self.logger.warning("short_interest列不存在，跳过融券筛选")

            if leverage.get('liquidity_min'):
                if 'liquidity_ratio' in filtered_data.columns:
                    filtered_data = filtered_data[
                        filtered_data['liquidity_ratio'] >= leverage['liquidity_min']
                    ]
                else:
                    self.logger.warning("liquidity_ratio列不存在，跳过流动性筛选")

            return filtered_data

        except Exception as e:
            self.logger.error(f"应用筛选条件失败: {e}")
            return data  # 返回原始数据
    
    def evaluate_margin_eligibility(self, data: pd.DataFrame, market: str) -> pd.DataFrame:
        """评估融资融券适用性"""
        try:
            if market not in self.margin_criteria:
                self.logger.warning(f"市场{market}没有融资融券标准，返回原数据")
                return data

            criteria = self.margin_criteria[market]

            # 检查必需的列是否存在
            required_columns = ['market_cap', 'daily_volume', 'current_price', 'volatility', 'liquidity_ratio']
            missing_columns = [col for col in required_columns if col not in data.columns]

            if missing_columns:
                self.logger.warning(f"缺少融资融券评估所需的列: {missing_columns}，返回原数据")
                return data

            # 应用融资融券标准
            eligible_data = data[
                (data['market_cap'] >= criteria['min_market_cap']) &
                (data['daily_volume'] >= criteria['min_daily_volume']) &
                (data['current_price'] >= criteria['min_price']) &
                (data['volatility'] <= criteria['max_volatility']) &
                (data['liquidity_ratio'] >= criteria['min_liquidity_ratio'])
            ].copy()

            if not eligible_data.empty:
                # 计算融资融券评分
                eligible_data['margin_score'] = self.calculate_margin_score(eligible_data, criteria)
                eligible_data['margin_eligible'] = True
            else:
                self.logger.info(f"没有股票符合{market}市场的融资融券标准")

            return eligible_data

        except Exception as e:
            self.logger.error(f"评估融资融券适用性失败: {e}")
            return data  # 返回原始数据
    
    def calculate_margin_score(self, data: pd.DataFrame, criteria: Dict) -> pd.Series:
        """计算融资融券评分"""
        try:
            scores = pd.Series(index=data.index, dtype=float)

            # 检查必需的列是否存在
            required_columns = ['market_cap', 'liquidity_ratio', 'volatility', 'daily_volume']
            missing_columns = [col for col in required_columns if col not in data.columns]

            if missing_columns:
                self.logger.warning(f"缺少融资融券评分所需的列: {missing_columns}，返回默认评分")
                return pd.Series([50.0] * len(data), index=data.index)

            for idx, row in data.iterrows():
                score = 0

                # 市值评分 (30%)
                market_cap_score = min(row['market_cap'] / criteria['min_market_cap'], 5) * 0.3

                # 流动性评分 (25%)
                liquidity_score = min(row['liquidity_ratio'] / criteria['min_liquidity_ratio'], 3) * 0.25

                # 价格稳定性评分 (20%)
                volatility_score = max(0, (criteria['max_volatility'] - row['volatility']) / criteria['max_volatility']) * 0.2

                # 成交量评分 (15%)
                volume_score = min(row['daily_volume'] / criteria['min_daily_volume'], 3) * 0.15

                # 基本面评分 (10%)
                fundamental_score = 0.1
                if 'pe_ratio' in data.columns and row.get('pe_ratio', 0) > 0 and row['pe_ratio'] < 30:
                    fundamental_score *= 1.5
                if 'roe' in data.columns and row.get('roe', 0) > 0.1:
                    fundamental_score *= 1.5

                scores[idx] = market_cap_score + liquidity_score + volatility_score + volume_score + fundamental_score

            return scores

        except Exception as e:
            self.logger.error(f"计算融资融券评分失败: {e}")
            return pd.Series([50.0] * len(data), index=data.index)  # 返回默认评分
    
    def rank_stocks(self, data: pd.DataFrame, criteria: Dict) -> List[Dict]:
        """对股票进行排序和排名"""
        try:
            if data.empty:
                return []

            # 计算综合评分
            data['total_score'] = self.calculate_total_score(data, criteria)

            # 按评分排序
            ranked_data = data.sort_values('total_score', ascending=False)

            # 转换为字典列表
            result = []
            for idx, row in ranked_data.iterrows():
                stock_info = {
                    'rank': len(result) + 1,
                    'symbol': row.get('symbol', ''),
                    'name': row.get('name', ''),
                    'market': row.get('market', ''),
                    'current_price': round(row.get('current_price', 0), 2),
                    'market_cap': row.get('market_cap', 0),
                    'pe_ratio': round(row.get('pe_ratio', 0), 2),
                    'pb_ratio': round(row.get('pb_ratio', 0), 2),
                    'roe': round(row.get('roe', 0), 4),
                    'total_score': round(row.get('total_score', 50), 2),
                    'margin_eligible': row.get('margin_eligible', False),
                    'margin_score': round(row.get('margin_score', 0), 2),
                    'recommendation': self.get_recommendation(row),
                    'risk_level': self.assess_risk_level(row)
                }
                result.append(stock_info)

            return result

        except Exception as e:
            self.logger.error(f"股票排序失败: {e}")
            return []  # 返回空列表
    
    def calculate_total_score(self, data: pd.DataFrame, criteria: Dict) -> pd.Series:
        """计算综合评分"""
        try:
            scores = pd.Series(index=data.index, dtype=float)

            for idx, row in data.iterrows():
                score = 0

                # 基本面评分 (40%)
                fundamental_score = 0
                if 'pe_ratio' in data.columns:
                    pe_ratio = row.get('pe_ratio', 0)
                    if 0 < pe_ratio < 25:
                        fundamental_score += 0.3

                if 'pb_ratio' in data.columns:
                    pb_ratio = row.get('pb_ratio', 0)
                    if 0 < pb_ratio < 3:
                        fundamental_score += 0.2

                if 'roe' in data.columns:
                    roe = row.get('roe', 0)
                    if roe > 0.1:
                        fundamental_score += 0.3

                if 'debt_ratio' in data.columns:
                    debt_ratio = row.get('debt_ratio', 1.0)
                    if debt_ratio < 0.5:
                        fundamental_score += 0.2

                # 技术面评分 (30%)
                technical_score = 0
                if 'rsi' in data.columns:
                    rsi = row.get('rsi', 50)
                    if 30 <= rsi <= 70:
                        technical_score += 0.4

                if 'ma_trend' in data.columns:
                    ma_trend = row.get('ma_trend', '')
                    if ma_trend == '上升':
                        technical_score += 0.6

                # 流动性评分 (20%)
                liquidity_score = 0
                if 'liquidity_ratio' in data.columns:
                    liquidity_ratio = row.get('liquidity_ratio', 0)
                    liquidity_score = min(liquidity_ratio * 10, 1.0)

                # 融资融券评分 (10%)
                margin_score = row.get('margin_score', 0) / 5.0

                scores[idx] = (fundamental_score * 0.4 +
                              technical_score * 0.3 +
                              liquidity_score * 0.2 +
                              margin_score * 0.1) * 100

            return scores

        except Exception as e:
            self.logger.error(f"计算综合评分失败: {e}")
            return pd.Series([50.0] * len(data), index=data.index)  # 返回默认评分
    
    def get_recommendation(self, row: pd.Series) -> str:
        """获取投资建议"""
        try:
            score = row.get('total_score', 50.0)

            if score >= 80:
                return '强烈买入'
            elif score >= 65:
                return '买入'
            elif score >= 50:
                return '持有'
            elif score >= 35:
                return '卖出'
            else:
                return '强烈卖出'
        except Exception as e:
            self.logger.warning(f"获取投资建议失败: {e}")
            return '持有'  # 默认建议

    def assess_risk_level(self, row: pd.Series) -> str:
        """评估风险等级"""
        try:
            volatility = row.get('volatility', 0.3)  # 默认中等波动率
            debt_ratio = row.get('debt_ratio', 0.5)  # 默认中等负债率

            risk_score = volatility * 0.6 + debt_ratio * 0.4

            if risk_score >= 0.6:
                return '高风险'
            elif risk_score >= 0.4:
                return '中等风险'
            else:
                return '低风险'
        except Exception as e:
            self.logger.warning(f"评估风险等级失败: {e}")
            return '中等风险'  # 默认风险等级

# 全局筛选器实例
global_screener = GlobalStockScreener()

async def screen_global_stocks(market: str, criteria: Dict) -> List[Dict]:
    """全球股票筛选入口函数"""
    return await global_screener.screen_stocks(market, criteria)
